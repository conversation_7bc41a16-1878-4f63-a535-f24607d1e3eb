using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Net;
using System.Net.Sockets;
using System.Security.Cryptography;
using System.Threading;
using System.Threading.Tasks;

namespace USDP2
{
    /// <summary>
    /// Sends data over UDP with optional security enhancements and circuit breaker protection.
    ///
    /// This class provides UDP communication with support for:
    /// - Standard UDP transmission (default)
    /// - Optional message authentication and integrity protection
    /// - Circuit breaker protection against failing endpoints
    /// - Per-endpoint circuit breaker isolation
    /// - Graceful fallback when security features are unavailable
    /// - Comprehensive security logging and monitoring
    /// </summary>
    public class UdpNetworkSender : INetworkSender, IDisposable
    {
        private UdpSecurityOverride? _udpSecurity;
        private readonly UsdpConfiguration _config;
        private readonly ConcurrentDictionary<string, CircuitBreaker> _circuitBreakers = new();
        private bool _disposed;

        /// <summary>
        /// Initializes a new instance of the UdpNetworkSender class.
        /// </summary>
        /// <param name="config">The USDP configuration instance. If null, uses UsdpConfiguration.Instance.</param>
        /// <exception cref="ArgumentNullException">Thrown when config is explicitly passed as null.</exception>
        public UdpNetworkSender(UsdpConfiguration? config = null)
        {
            _config = config ?? UsdpConfiguration.Instance;

            // Note: UDP security initialization is deferred to first use to avoid blocking constructor
            // This prevents deadlock issues with async operations in constructors
            Diagnostics.Log("UdpNetworkSender", new
            {
                Message = _config.EnableUdpSecurity ?
                    "Initialized with deferred UDP security initialization" :
                    "Initialized with standard UDP transmission",
                SecurityEnabled = _config.EnableUdpSecurity,
                DeferredInitialization = _config.EnableUdpSecurity
            });
        }

        /// <summary>
        /// Ensures UDP security is initialized if enabled. Called before first use.
        /// </summary>
        /// <exception cref="ObjectDisposedException">Thrown when the instance has been disposed.</exception>
        private async Task EnsureSecurityInitializedAsync()
        {
            ObjectDisposedException.ThrowIf(_disposed, this);

            if (_config.EnableUdpSecurity && _udpSecurity == null)
            {
                try
                {
                    _udpSecurity = await UdpSecurityOverride.CreateAsync();

                    Diagnostics.Log("UdpNetworkSender", new
                    {
                        Message = "Successfully initialized UDP security override",
                        SecurityEnabled = true
                    });
                }
                catch (TypeLoadException ex)
                {
                    // Handle case where UdpSecurityOverride class is not available
                    // (e.g., if the class was removed for simplicity)
                    Diagnostics.Log("UdpNetworkSender", new
                    {
                        Message = "UDP security class not available, falling back to unsecured UDP",
                        SecurityEnabled = false,
                        Error = ex.Message,
                        FallbackToUnsecured = true,
                        ErrorType = "TypeLoadException"
                    });

                    // _udpSecurity remains null, system will use standard UDP
                }
                catch (CryptographicException ex)
                {
                    // Handle cryptographic initialization failures
                    Diagnostics.Log("UdpNetworkSenderWarning", new
                    {
                        Message = "UDP security cryptographic initialization failed, falling back to unsecured UDP",
                        SecurityEnabled = false,
                        Error = ex.Message,
                        FallbackToUnsecured = true,
                        ErrorType = "CryptographicException"
                    });

                    // _udpSecurity remains null, system will use standard UDP
                }
                catch (Exception ex)
                {
                    // Handle any other initialization failures
                    Diagnostics.Log("UdpNetworkSenderWarning", new
                    {
                        Message = "UDP security initialization failed, falling back to unsecured UDP",
                        SecurityEnabled = false,
                        Error = ex.Message,
                        FallbackToUnsecured = true,
                        ErrorType = ex.GetType().Name
                    });

                    // _udpSecurity remains null, system will use standard UDP
                }
            }
        }
        /// <summary>
        /// Sends data asynchronously via UDP to the specified address and port with circuit breaker protection.
        ///
        /// This method automatically applies security enhancements if UDP security is enabled
        /// and available. If security fails, it falls back to unsecured transmission with
        /// appropriate logging. Circuit breaker protection prevents cascading failures.
        /// </summary>
        /// <param name="data">The data to send.</param>
        /// <param name="address">The target IP address.</param>
        /// <param name="port">The target port number.</param>
        /// <param name="cancellationToken">The cancellation token to cancel the operation.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous send operation.</returns>
        /// <exception cref="CircuitBreakerOpenException">Thrown when the circuit breaker is open for the target endpoint.</exception>
        /// <exception cref="SocketException">Thrown when a socket error occurs.</exception>
        public async Task SendAsync(byte[] data, string address, int port, CancellationToken cancellationToken = default)
        {
            ObjectDisposedException.ThrowIf(_disposed, this);
            ArgumentNullException.ThrowIfNull(data);
            ArgumentException.ThrowIfNullOrEmpty(address);

            // Validate network parameters before attempting UDP operations
            InputValidator.ValidatePort(port, nameof(port));
            InputValidator.ValidateIpAddress(address, nameof(address));

            // Get or create circuit breaker for this endpoint
            var endpointKey = $"{address}:{port}";
            var circuitBreaker = GetOrCreateCircuitBreaker(endpointKey);

            // Execute the UDP send through the circuit breaker
            await circuitBreaker.ExecuteAsync(async ct =>
            {
                // Ensure security is initialized if enabled
                await EnsureSecurityInitializedAsync();

                byte[] dataToSend = data;
                bool securityApplied = false;

                UsdpLogger.Log("UdpNetworkSender.RequestStarted", new
                {
                    Address = address,
                    Port = port,
                    DataSize = data.Length,
                    CircuitBreakerState = circuitBreaker.State
                });

                // Apply security if enabled and available
                if (_config.EnableUdpSecurity && _udpSecurity != null)
                {
                    try
                    {
                        dataToSend = _udpSecurity.SecureData(data);
                        securityApplied = true;

                        UsdpLogger.Log("UdpNetworkSender.SecurityApplied", new
                        {
                            Message = "Applied UDP security to outgoing data",
                            OriginalSize = data.Length,
                            SecuredSize = dataToSend.Length,
                            Address = address,
                            Port = port
                        });
                    }
                    catch (Exception ex)
                    {
                        // Security failed, fall back to unsecured transmission
                        UsdpLogger.Log("UdpNetworkSender.SecurityFallback", new
                        {
                            Message = "UDP security failed, falling back to unsecured transmission",
                            Address = address,
                            Port = port,
                            Error = ex.Message,
                            FallbackToUnsecured = true
                        });

                        dataToSend = data; // Use original data
                        securityApplied = false;
                    }
                }

                using var udpClient = new UdpClient();
                var ip = IPAddress.Parse(address);
                var endpoint = new IPEndPoint(ip, port);
                await udpClient.SendAsync(dataToSend, dataToSend.Length, endpoint).WaitAsync(ct);

                UsdpLogger.Log("UdpNetworkSender.Success", new
                {
                    Message = "Successfully sent UDP data",
                    Address = address,
                    Port = port,
                    DataSize = dataToSend.Length,
                    SecurityApplied = securityApplied
                });

            }, cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        /// Gets or creates a circuit breaker for the specified endpoint.
        /// </summary>
        /// <param name="endpointKey">The endpoint key (address:port).</param>
        /// <returns>A circuit breaker instance for the endpoint.</returns>
        private CircuitBreaker GetOrCreateCircuitBreaker(string endpointKey)
        {
            return _circuitBreakers.GetOrAdd(endpointKey, key =>
            {
                var options = new CircuitBreakerOptions
                {
                    FailureThreshold = _config.CircuitBreakerFailureThreshold,
                    OpenTimeout = _config.CircuitBreakerOpenTimeout,
                    OperationTimeout = _config.NetworkTimeout,
                    SuccessThreshold = _config.CircuitBreakerSuccessThreshold,
                    ShouldHandleException = ex => ex switch
                    {
                        TaskCanceledException when ex.InnerException is TimeoutException => true, // Count timeout cancellations as failures
                        OperationCanceledException => false, // Don't count cancellations as failures
                        TimeoutException => true,            // Count timeouts as failures
                        SocketException => true,             // Count socket errors as failures
                        FormatException => false,            // Don't count format errors as failures (bad input)
                        _ => true // Count other exceptions as failures by default
                    }
                };

                var circuitBreaker = new CircuitBreaker($"UdpNetworkSender.{key}", options);

                UsdpLogger.Log("UdpNetworkSender.CircuitBreakerCreated", new
                {
                    EndpointKey = key,
                    FailureThreshold = options.FailureThreshold,
                    OpenTimeout = options.OpenTimeout.TotalSeconds,
                    OperationTimeout = options.OperationTimeout.TotalSeconds
                });

                return circuitBreaker;
            });
        }

        /// <summary>
        /// Gets statistics for all circuit breakers managed by this sender.
        /// </summary>
        /// <returns>A dictionary of endpoint keys and their circuit breaker statistics.</returns>
        public Dictionary<string, CircuitBreakerStatistics> GetCircuitBreakerStatistics()
        {
            var statistics = new Dictionary<string, CircuitBreakerStatistics>();

            foreach (var kvp in _circuitBreakers)
            {
                try
                {
                    statistics[kvp.Key] = kvp.Value.GetStatistics();
                }
                catch (Exception ex)
                {
                    UsdpLogger.Log("UdpNetworkSender.StatisticsError", new
                    {
                        EndpointKey = kvp.Key,
                        Error = ex.Message
                    });
                }
            }

            return statistics;
        }

        /// <summary>
        /// Gets the circuit breaker for a specific endpoint, if it exists.
        /// </summary>
        /// <param name="address">The target address.</param>
        /// <param name="port">The target port.</param>
        /// <returns>The circuit breaker for the endpoint, or null if it doesn't exist.</returns>
        public CircuitBreaker? GetCircuitBreaker(string address, int port)
        {
            var endpointKey = $"{address}:{port}";
            return _circuitBreakers.TryGetValue(endpointKey, out var circuitBreaker) ? circuitBreaker : null;
        }

        /// <summary>
        /// Disposes of managed resources.
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                // Note: UdpSecurityOverride doesn't implement IDisposable, so no disposal needed
                // UdpClient instances are created with 'using' statements in SendAsync, so they're automatically disposed

                UsdpLogger.Log("UdpNetworkSender.Disposed", new
                {
                    Message = "UdpNetworkSender disposed",
                    CircuitBreakersCount = _circuitBreakers.Count,
                    Disposed = true
                });

                _disposed = true;
                GC.SuppressFinalize(this);
            }
        }
    }
}