﻿﻿using System;
using System.Net;
using System.Net.Sockets;
using System.Security.Cryptography;
using System.Threading;
using System.Threading.Tasks;

namespace USDP2
{
    /// <summary>
    /// UDP network receiver implementation that supports both unicast and multicast communication
    /// with optional security enhancements.
    ///
    /// This class provides UDP communication with support for:
    /// - Standard UDP reception (default)
    /// - Optional message authentication and integrity verification
    /// - Graceful handling of both secured and unsecured messages
    /// - Comprehensive security logging and monitoring
    /// </summary>
    public class UdpNetworkReceiver : INetworkReceiver
    {
        private readonly int _port;
        private readonly bool _isMulticast;
        private readonly string? _multicastAddress;
        private UdpSecurityOverride? _udpSecurity;
        private readonly UsdpConfiguration _config;
        private UdpClient? _udpClient;
        private CancellationTokenSource? _cancellationTokenSource;
        private Task? _receiveTask;
        private bool _disposed;

        /// <summary>
        /// Initializes a new instance of the <see cref="UdpNetworkReceiver"/> class.
        /// </summary>
        /// <param name="port">The UDP port to listen on. Must be between 1 and 65535.</param>
        /// <param name="isMulticast">Indicates whether this is a multicast receiver.</param>
        /// <param name="multicastAddress">The multicast address to join (required if isMulticast is true). Must be a valid IP address if provided.</param>
        /// <param name="config">The USDP configuration instance. If null, uses UsdpConfiguration.Instance.</param>
        /// <exception cref="ArgumentOutOfRangeException">Thrown when port is outside the valid range.</exception>
        /// <exception cref="ArgumentException">Thrown when multicast address is required but not provided, or when multicast address is invalid.</exception>
        public UdpNetworkReceiver(int port, bool isMulticast = false, string? multicastAddress = null, UsdpConfiguration? config = null)
        {
            // Validate network parameters before initializing UDP receiver
            InputValidator.ValidatePort(port, nameof(port));

            _port = port;
            _isMulticast = isMulticast;
            _multicastAddress = multicastAddress;
            _config = config ?? UsdpConfiguration.Instance;

            if (_isMulticast && string.IsNullOrEmpty(_multicastAddress))
            {
                throw new ArgumentException("Multicast address must be provided when isMulticast is true", nameof(multicastAddress));
            }

            // Validate multicast address format if provided
            if (!string.IsNullOrEmpty(_multicastAddress))
            {
                InputValidator.ValidateIpAddress(_multicastAddress, nameof(multicastAddress));
            }

            // Note: UDP security initialization is deferred to avoid blocking constructor
            // This prevents deadlock issues with async operations in constructors
            Diagnostics.Log("UdpNetworkReceiver", new
            {
                Message = _config.EnableUdpSecurity ?
                    "Initialized with deferred UDP security initialization" :
                    "Initialized with standard UDP reception",
                Port = port,
                IsMulticast = isMulticast,
                SecurityEnabled = _config.EnableUdpSecurity,
                DeferredInitialization = _config.EnableUdpSecurity
            });
        }

        /// <summary>
        /// Ensures UDP security is initialized if enabled. Called before first use.
        /// </summary>
        /// <exception cref="ObjectDisposedException">Thrown when the instance has been disposed.</exception>
        private async Task EnsureSecurityInitializedAsync()
        {
            ObjectDisposedException.ThrowIf(_disposed, this);

            if (_config.EnableUdpSecurity && _udpSecurity == null)
            {
                try
                {
                    _udpSecurity = await UdpSecurityOverride.CreateAsync();

                    Diagnostics.Log("UdpNetworkReceiver", new
                    {
                        Message = "Successfully initialized UDP security override",
                        Port = _port,
                        IsMulticast = _isMulticast,
                        SecurityEnabled = true
                    });
                }
                catch (TypeLoadException ex)
                {
                    // Handle case where UdpSecurityOverride class is not available
                    // (e.g., if the class was removed for simplicity)
                    Diagnostics.Log("UdpNetworkReceiver", new
                    {
                        Message = "UDP security class not available, will accept unsecured messages only",
                        Port = _port,
                        IsMulticast = _isMulticast,
                        SecurityEnabled = false,
                        Error = ex.Message,
                        FallbackToUnsecured = true,
                        ErrorType = "TypeLoadException"
                    });

                    // _udpSecurity remains null, system will process unsecured messages only
                }
                catch (CryptographicException ex)
                {
                    // Handle cryptographic initialization failures
                    Diagnostics.Log("UdpNetworkReceiverWarning", new
                    {
                        Message = "UDP security cryptographic initialization failed, will accept unsecured messages only",
                        Port = _port,
                        IsMulticast = _isMulticast,
                        SecurityEnabled = false,
                        Error = ex.Message,
                        FallbackToUnsecured = true,
                        ErrorType = "CryptographicException"
                    });

                    // _udpSecurity remains null, system will process unsecured messages only
                }
                catch (Exception ex)
                {
                    // Handle any other initialization failures
                    Diagnostics.Log("UdpNetworkReceiverWarning", new
                    {
                        Message = "UDP security initialization failed, will accept unsecured messages only",
                        Port = _port,
                        IsMulticast = _isMulticast,
                        SecurityEnabled = false,
                        Error = ex.Message,
                        FallbackToUnsecured = true,
                        ErrorType = ex.GetType().Name
                    });

                    // _udpSecurity remains null, system will process unsecured messages only
                }
            }
        }

        /// <summary>
        /// Starts receiving UDP packets asynchronously.
        /// </summary>
        /// <param name="onMessageReceived">Callback function to handle received messages.</param>
        /// <param name="cancellationToken">Cancellation token to stop receiving.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        /// <exception cref="ObjectDisposedException">Thrown when the instance has been disposed.</exception>
        /// <exception cref="ArgumentNullException">Thrown when onMessageReceived is null.</exception>
        public async Task StartReceivingAsync(Func<byte[], string, int, Task> onMessageReceived, CancellationToken cancellationToken = default)
        {
            ObjectDisposedException.ThrowIf(_disposed, this);
            ArgumentNullException.ThrowIfNull(onMessageReceived);

            // Ensure security is initialized if enabled
            await EnsureSecurityInitializedAsync();

            _cancellationTokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
            var combinedToken = _cancellationTokenSource.Token;

            _udpClient = new UdpClient();
            _udpClient.Client.SetSocketOption(SocketOptionLevel.Socket, SocketOptionName.ReuseAddress, true);
            _udpClient.Client.Bind(new IPEndPoint(IPAddress.Any, _port));

            if (_isMulticast && !string.IsNullOrEmpty(_multicastAddress))
            {
                _udpClient.JoinMulticastGroup(IPAddress.Parse(_multicastAddress));
            }

            _receiveTask = Task.Run(async () =>
            {
                try
                {
                    while (!combinedToken.IsCancellationRequested)
                    {
                        var result = await _udpClient.ReceiveAsync(combinedToken);
                        var remoteEndPoint = result.RemoteEndPoint;

                        _ = ProcessMessageAsync(result, remoteEndPoint, onMessageReceived, combinedToken);
                    }
                }
                catch (OperationCanceledException)
                {
                    // Normal cancellation, no action needed
                }
                catch (Exception ex) when (!combinedToken.IsCancellationRequested)
                {
                    Diagnostics.Log("UdpReceiverError", new { Message = "Unexpected error in UDP receiver.", Exception = ex.Message });
                    throw;
                }
            }, combinedToken);
        }

        /// <summary>
        /// Processes a received UDP message with security verification and error handling.
        /// </summary>
        /// <param name="result">The UDP receive result containing the message data.</param>
        /// <param name="remoteEndPoint">The remote endpoint that sent the message.</param>
        /// <param name="onMessageReceived">The callback to invoke with the processed message.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>A task representing the asynchronous message processing operation.</returns>
        private Task ProcessMessageAsync(
            UdpReceiveResult result,
            IPEndPoint remoteEndPoint,
            Func<byte[], string, int, Task> onMessageReceived,
            CancellationToken cancellationToken)
        {
            return Task.Run(async () =>
            {
                try
                {
                    byte[] messageData = result.Buffer;
                    bool securityVerified = false;

                    // Attempt to verify and extract data if security is enabled
                    if (_config.EnableUdpSecurity && _udpSecurity != null)
                    {
                        try
                        {
                            messageData = _udpSecurity.VerifyAndExtractData(result.Buffer);
                            securityVerified = true;

                            Diagnostics.Log("UdpNetworkReceiver", new
                            {
                                Message = "Successfully verified secured UDP message",
                                SecuredSize = result.Buffer.Length,
                                OriginalSize = messageData.Length,
                                RemoteAddress = remoteEndPoint.Address.ToString(),
                                RemotePort = remoteEndPoint.Port,
                                SecurityVerified = securityVerified
                            });
                        }
                        catch (CryptographicException secEx)
                        {
                            // Security verification failed due to cryptographic issues
                            Diagnostics.Log("UdpNetworkReceiverWarning", new
                            {
                                Message = "UDP security verification failed due to cryptographic error, treating as unsecured message",
                                RemoteAddress = remoteEndPoint.Address.ToString(),
                                RemotePort = remoteEndPoint.Port,
                                DataSize = result.Buffer.Length,
                                SecurityError = secEx.Message,
                                TreatingAsUnsecured = true,
                                ErrorType = "CryptographicException"
                            });

                            // Use original data as unsecured message
                            messageData = result.Buffer;
                            securityVerified = false;
                        }
                        catch (Exception secEx)
                        {
                            // Security verification failed - could be unsecured message or other issues
                            Diagnostics.Log("UdpNetworkReceiverWarning", new
                            {
                                Message = "UDP security verification failed, treating as unsecured message",
                                RemoteAddress = remoteEndPoint.Address.ToString(),
                                RemotePort = remoteEndPoint.Port,
                                DataSize = result.Buffer.Length,
                                SecurityError = secEx.Message,
                                TreatingAsUnsecured = true,
                                ErrorType = secEx.GetType().Name
                            });

                            // Use original data as unsecured message
                            messageData = result.Buffer;
                            securityVerified = false;
                        }
                    }
                    else
                    {
                        // Security not enabled, process as unsecured message
                        securityVerified = false; // Explicitly set for clarity
                        Diagnostics.Log("UdpNetworkReceiver", new
                        {
                            Message = "Processing unsecured UDP message",
                            RemoteAddress = remoteEndPoint.Address.ToString(),
                            RemotePort = remoteEndPoint.Port,
                            DataSize = messageData.Length,
                            SecurityEnabled = false,
                            SecurityVerified = securityVerified
                        });
                    }

                    // Invoke the message handler with the processed data
                    await onMessageReceived(messageData, remoteEndPoint.Address.ToString(), remoteEndPoint.Port);
                }
                catch (Exception ex)
                {
                    Diagnostics.Log("UdpMessageProcessingError", new
                    {
                        Message = "Error processing UDP message",
                        RemoteAddress = remoteEndPoint.Address.ToString(),
                        RemotePort = remoteEndPoint.Port,
                        Exception = ex.Message,
                        ExceptionType = ex.GetType().Name
                    });
                }
            }, cancellationToken);
        }

        /// <summary>
        /// Disposes the UDP client and cancels any ongoing receive operations.
        /// </summary>
        /// <returns>A ValueTask representing the asynchronous dispose operation.</returns>
        public async ValueTask DisposeAsync()
        {
            if (_disposed)
            {
                return; // Already disposed
            }

            _disposed = true;

            if (_cancellationTokenSource != null)
            {
                // Cancel the receive operation
                if (!_cancellationTokenSource.IsCancellationRequested)
                {
                    await _cancellationTokenSource.CancelAsync();
                }

                // Wait for the receive task to complete with timeout to avoid deadlocks
                if (_receiveTask != null)
                {
                    try
                    {
                        // Use a timeout to avoid potential deadlocks when awaiting tasks from different contexts
                        using var timeoutCts = new CancellationTokenSource(TimeSpan.FromSeconds(5));
                        await _receiveTask.WaitAsync(timeoutCts.Token).ConfigureAwait(false);
                    }
                    catch (OperationCanceledException)
                    {
                        // Expected during cancellation or timeout
                        Diagnostics.Log("UdpReceiverShutdownTimeout", new
                        {
                            Message = "UDP receiver shutdown timed out or was cancelled.",
                            Port = _port,
                            IsMulticast = _isMulticast
                        });
                    }
                    catch (Exception ex)
                    {
                        Diagnostics.Log("UdpReceiverShutdownError", new
                        {
                            Message = "Error during UDP receiver shutdown.",
                            Exception = ex.Message,
                            Port = _port,
                            IsMulticast = _isMulticast
                        });
                    }
                }

                _cancellationTokenSource.Dispose();
                _cancellationTokenSource = null;
            }

            if (_udpClient != null)
            {
                // Leave multicast group if joined
                if (_isMulticast && !string.IsNullOrEmpty(_multicastAddress))
                {
                    try
                    {
                        _udpClient.DropMulticastGroup(IPAddress.Parse(_multicastAddress));
                    }
                    catch (Exception ex)
                    {
                        Diagnostics.Log("UdpMulticastLeaveError", new
                        {
                            Message = "Error leaving multicast group.",
                            Exception = ex.Message,
                            MulticastAddress = _multicastAddress,
                            Port = _port
                        });
                    }
                }

                _udpClient.Dispose();
                _udpClient = null;
            }

            Diagnostics.Log("UdpNetworkReceiver", new
            {
                Message = "UdpNetworkReceiver disposed",
                Port = _port,
                IsMulticast = _isMulticast,
                Disposed = true
            });

            GC.SuppressFinalize(this);
        }
    }
}