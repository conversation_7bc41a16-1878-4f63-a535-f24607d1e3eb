using System;
using System.Diagnostics;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using USDP2.Tests.Mocks;

namespace USDP2.Tests
{
    /// <summary>
    /// Performance tests for the security validation system to ensure that security features
    /// don't significantly impact system performance under normal and high-load conditions.
    /// 
    /// These tests verify that:
    /// - Security overhead is minimal for legitimate traffic
    /// - System remains responsive under attack conditions
    /// - Memory usage is controlled and doesn't grow unbounded
    /// - Rate limiting and security tracking scale appropriately
    /// </summary>
    [TestClass]
    public class SecurityPerformanceTests
    {
        private UsdpConfiguration _testConfig = null!;
        private DirectoryNode _directoryNode = null!;
        private MockNetworkSender _mockSender = null!;
        private MockNetworkReceiver _mockReceiver = null!;

        [TestInitialize]
        public void Setup()
        {
            _testConfig = new UsdpConfiguration(forTesting: true)
            {
                EnableRateLimiting = true,
                RateLimitMaxMessagesPerWindow = 1000, // High limit for performance testing
                RateLimitTimeWindow = TimeSpan.FromSeconds(60),
                RateLimitBurstFactor = 2.0,
                EnableEnhancedDosProtection = true,
                RateLimitCleanupInterval = TimeSpan.FromSeconds(30)
            };

            _mockSender = new MockNetworkSender();
            _mockReceiver = new MockNetworkReceiver();
            _directoryNode = new DirectoryNode(_mockSender, _mockReceiver, _testConfig);
        }

        [TestCleanup]
        public async Task Cleanup()
        {
            await _directoryNode.DisposeAsync();
        }

        [TestMethod]
        public async Task SecurityOverhead_LegitimateTraffic_ShouldBeLowImpact()
        {
            // Test the performance impact of security features on legitimate traffic
            var validMessage = CreateValidServiceAdvertisement();
            var messageData = validMessage.ToCbor();
            var endpoint = "192.168.1.100";
            var port = 12345;
            var messageCount = 1000;

            // Measure time with security enabled
            var stopwatch = Stopwatch.StartNew();
            
            for (int i = 0; i < messageCount; i++)
            {
                await _mockReceiver.SimulateMessageReception(messageData, endpoint, port + (i % 100));
            }
            
            stopwatch.Stop();
            var timeWithSecurity = stopwatch.ElapsedMilliseconds;

            // Assert reasonable performance
            var averageTimePerMessage = (double)timeWithSecurity / messageCount;
            Assert.IsTrue(averageTimePerMessage < 10, // Less than 10ms per message
                $"Security overhead too high: {averageTimePerMessage:F2}ms per message");

            Console.WriteLine($"Processed {messageCount} messages in {timeWithSecurity}ms");
            Console.WriteLine($"Average time per message: {averageTimePerMessage:F2}ms");

            // Verify security tracking is working
            var rateLimitStats = _directoryNode.GetRateLimitStatistics();
            Assert.IsTrue(rateLimitStats.Count > 0, "Should be tracking endpoints");
        }

        [TestMethod]
        public async Task RateLimiter_HighVolumeTraffic_ShouldScaleEfficiently()
        {
            // Test rate limiter performance with high volume traffic from many endpoints
            var messageCount = 5000;
            var endpointCount = 100;
            var validMessage = CreateValidServiceAdvertisement();
            var messageData = validMessage.ToCbor();

            var stopwatch = Stopwatch.StartNew();

            // Send messages from many different endpoints
            for (int i = 0; i < messageCount; i++)
            {
                var endpoint = $"192.168.1.{i % endpointCount + 1}";
                var port = 12345 + (i % 10);
                await _mockReceiver.SimulateMessageReception(messageData, endpoint, port);
            }

            stopwatch.Stop();

            // Assert performance is acceptable
            var averageTimePerMessage = (double)stopwatch.ElapsedMilliseconds / messageCount;
            Assert.IsTrue(averageTimePerMessage < 5, // Less than 5ms per message
                $"Rate limiter performance too slow: {averageTimePerMessage:F2}ms per message");

            // Verify tracking is working correctly
            var rateLimitStats = _directoryNode.GetRateLimitStatistics();
            Assert.IsTrue(rateLimitStats.Count <= endpointCount * 10, "Should track unique endpoints efficiently");

            Console.WriteLine($"Rate limiter processed {messageCount} messages from {endpointCount} endpoints");
            Console.WriteLine($"Average time per message: {averageTimePerMessage:F2}ms");
            Console.WriteLine($"Tracked endpoints: {rateLimitStats.Count}");
        }

        [TestMethod]
        public async Task SecurityEventTracker_HighEventVolume_ShouldMaintainPerformance()
        {
            // Test security event tracker performance with high volume of security events
            var eventCount = 2000;
            var endpointCount = 50;

            var stopwatch = Stopwatch.StartNew();

            // Generate various security events
            for (int i = 0; i < eventCount; i++)
            {
                var endpoint = $"10.0.0.{i % endpointCount + 1}";
                var port = 54321 + (i % 5);

                // Alternate between different types of suspicious data
                byte[] suspiciousData;
                if (i % 3 == 0)
                {
                    // Oversized data
                    suspiciousData = new byte[_testConfig.MaxNetworkDataSize + 10];
                    Array.Fill(suspiciousData, (byte)'A');
                }
                else if (i % 3 == 1)
                {
                    // Suspicious binary pattern
                    suspiciousData = new byte[500];
                    Array.Fill(suspiciousData, (byte)0);
                }
                else
                {
                    // Very small data
                    suspiciousData = new byte[] { 0x01, 0x02 };
                }

                await _mockReceiver.SimulateMessageReception(suspiciousData, endpoint, port);
            }

            stopwatch.Stop();

            // Assert performance is acceptable even with many security events
            var averageTimePerEvent = (double)stopwatch.ElapsedMilliseconds / eventCount;
            Assert.IsTrue(averageTimePerEvent < 15, // Less than 15ms per security event
                $"Security event processing too slow: {averageTimePerEvent:F2}ms per event");

            // Verify security tracking is working
            var securityStats = _directoryNode.GetSecurityStatistics();
            Assert.IsTrue(securityStats.RecentEventsCount > 0, "Should have recorded security events");
            Assert.IsTrue(securityStats.TotalEndpoints <= endpointCount, "Should track endpoints efficiently");

            Console.WriteLine($"Processed {eventCount} security events in {stopwatch.ElapsedMilliseconds}ms");
            Console.WriteLine($"Average time per event: {averageTimePerEvent:F2}ms");
            Console.WriteLine($"Security health score: {securityStats.SecurityHealthScore}");
        }

        [TestMethod]
        public async Task MemoryUsage_ExtendedOperation_ShouldNotGrowUnbounded()
        {
            // Test that memory usage doesn't grow unbounded during extended operation
            var initialMemory = GC.GetTotalMemory(true);
            var validMessage = CreateValidServiceAdvertisement();
            var messageData = validMessage.ToCbor();

            // Simulate extended operation with many endpoints
            for (int cycle = 0; cycle < 10; cycle++)
            {
                // Send messages from many endpoints
                for (int i = 0; i < 100; i++)
                {
                    var endpoint = $"192.168.{cycle % 10}.{i % 100 + 1}";
                    var port = 12345 + (i % 20);
                    await _mockReceiver.SimulateMessageReception(messageData, endpoint, port);
                }

                // Force garbage collection to get accurate memory measurement
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
            }

            var finalMemory = GC.GetTotalMemory(false);
            var memoryGrowth = finalMemory - initialMemory;

            // Assert memory growth is reasonable (less than 10MB for this test)
            Assert.IsTrue(memoryGrowth < 10 * 1024 * 1024, 
                $"Memory growth too high: {memoryGrowth / 1024 / 1024:F2}MB");

            Console.WriteLine($"Memory growth: {memoryGrowth / 1024:F2}KB");

            // Verify cleanup is working
            var rateLimitStats = _directoryNode.GetRateLimitStatistics();
            Console.WriteLine($"Tracked endpoints after extended operation: {rateLimitStats.Count}");
        }

        [TestMethod]
        public async Task ConcurrentAccess_MultipleThreads_ShouldMaintainPerformance()
        {
            // Test performance under concurrent access from multiple threads
            var threadsCount = 10;
            var messagesPerThread = 200;
            var validMessage = CreateValidServiceAdvertisement();
            var messageData = validMessage.ToCbor();

            var stopwatch = Stopwatch.StartNew();

            // Create tasks for concurrent message processing
            var tasks = new Task[threadsCount];
            for (int t = 0; t < threadsCount; t++)
            {
                var threadId = t;
                tasks[t] = Task.Run(async () =>
                {
                    for (int i = 0; i < messagesPerThread; i++)
                    {
                        var endpoint = $"192.168.{threadId}.{i % 50 + 1}";
                        var port = 12345 + threadId;
                        await _mockReceiver.SimulateMessageReception(messageData, endpoint, port);
                    }
                });
            }

            await Task.WhenAll(tasks);
            stopwatch.Stop();

            var totalMessages = threadsCount * messagesPerThread;
            var averageTimePerMessage = (double)stopwatch.ElapsedMilliseconds / totalMessages;

            // Assert concurrent performance is acceptable
            Assert.IsTrue(averageTimePerMessage < 20, // Less than 20ms per message under concurrency
                $"Concurrent performance too slow: {averageTimePerMessage:F2}ms per message");

            // Verify thread safety - no exceptions should have occurred
            var rateLimitStats = _directoryNode.GetRateLimitStatistics();
            var securityStats = _directoryNode.GetSecurityStatistics();

            Assert.IsTrue(rateLimitStats.Count > 0, "Should be tracking endpoints");
            Assert.AreEqual(100, securityStats.SecurityHealthScore, "Should maintain perfect health for valid messages");

            Console.WriteLine($"Processed {totalMessages} messages concurrently in {stopwatch.ElapsedMilliseconds}ms");
            Console.WriteLine($"Average time per message: {averageTimePerMessage:F2}ms");
            Console.WriteLine($"Threads: {threadsCount}, Messages per thread: {messagesPerThread}");
        }

        [TestMethod]
        public void SecurityStatistics_LargeDataSet_ShouldCalculateEfficiently()
        {
            // Test performance of security statistics calculation with large datasets
            var endpointCount = 1000;

            // Manually create a large number of endpoints with various statistics
            // This simulates what would happen after extended operation
            var stopwatch = Stopwatch.StartNew();

            // Get statistics multiple times to test calculation performance
            for (int i = 0; i < 100; i++)
            {
                var stats = _directoryNode.GetSecurityStatistics();
                var rateLimitStats = _directoryNode.GetRateLimitStatistics();
                
                // Access various properties to ensure they're calculated
                var healthScore = stats.SecurityHealthScore;
                var totalEndpoints = stats.TotalEndpoints;
                var suspiciousCount = stats.SuspiciousEndpoints;
            }

            stopwatch.Stop();

            var averageCalculationTime = (double)stopwatch.ElapsedMilliseconds / 100;
            
            // Assert statistics calculation is fast
            Assert.IsTrue(averageCalculationTime < 50, // Less than 50ms per calculation
                $"Statistics calculation too slow: {averageCalculationTime:F2}ms");

            Console.WriteLine($"Average statistics calculation time: {averageCalculationTime:F2}ms");
        }

        /// <summary>
        /// Creates a valid service advertisement for performance testing.
        /// </summary>
        private static ServiceAdvertisement CreateValidServiceAdvertisement()
        {
            var serviceId = new ServiceIdentifier("perf-test", "performance-test-service");
            var endpoint = new TransportEndpoint
            {
                Protocol = "http",
                Address = "127.0.0.1",
                Port = 8080,
                Security = "none"
            };

            return new ServiceAdvertisement(serviceId, endpoint)
            {
                Metadata = new Dictionary<string, object>
                {
                    ["version"] = "1.0",
                    ["description"] = "Performance test service"
                },
                Ttl = TimeSpan.FromMinutes(30)
            };
        }
    }
}
