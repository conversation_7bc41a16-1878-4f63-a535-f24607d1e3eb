using System;

namespace USDP2
{
    /// <summary>
    /// Defines event arguments for service cache operations.
    /// </summary>
    public abstract class ServiceCacheEventArgs : EventArgs
    {
        /// <summary>
        /// Gets the timestamp when the event occurred.
        /// </summary>
        public DateTimeOffset Timestamp { get; }

        /// <summary>
        /// Gets the service identifier associated with the event.
        /// </summary>
        public string ServiceId { get; }

        /// <summary>
        /// Initializes a new instance of the ServiceCacheEventArgs class.
        /// </summary>
        /// <param name="serviceId">The service identifier.</param>
        protected ServiceCacheEventArgs(string serviceId)
        {
            ServiceId = serviceId ?? throw new ArgumentNullException(nameof(serviceId));
            Timestamp = DateTimeOffset.UtcNow;
        }
    }

    /// <summary>
    /// Event arguments for cache hit events.
    /// </summary>
    public sealed class CacheHitEventArgs : ServiceCacheEventArgs
    {
        /// <summary>
        /// Gets the access count for this entry.
        /// </summary>
        public int AccessCount { get; }

        /// <summary>
        /// Gets the time since the entry was last accessed.
        /// </summary>
        public TimeSpan TimeSinceLastAccess { get; }

        /// <summary>
        /// Initializes a new instance of the CacheHitEventArgs class.
        /// </summary>
        /// <param name="serviceId">The service identifier.</param>
        /// <param name="accessCount">The access count for this entry.</param>
        /// <param name="timeSinceLastAccess">The time since last access.</param>
        public CacheHitEventArgs(string serviceId, int accessCount, TimeSpan timeSinceLastAccess)
            : base(serviceId)
        {
            AccessCount = accessCount;
            TimeSinceLastAccess = timeSinceLastAccess;
        }
    }

    /// <summary>
    /// Event arguments for cache miss events.
    /// </summary>
    public sealed class CacheMissEventArgs : ServiceCacheEventArgs
    {
        /// <summary>
        /// Gets the reason for the cache miss.
        /// </summary>
        public CacheMissReason Reason { get; }

        /// <summary>
        /// Initializes a new instance of the CacheMissEventArgs class.
        /// </summary>
        /// <param name="serviceId">The service identifier.</param>
        /// <param name="reason">The reason for the cache miss.</param>
        public CacheMissEventArgs(string serviceId, CacheMissReason reason)
            : base(serviceId)
        {
            Reason = reason;
        }
    }

    /// <summary>
    /// Event arguments for entry added events.
    /// </summary>
    public sealed class EntryAddedEventArgs : ServiceCacheEventArgs
    {
        /// <summary>
        /// Gets the estimated memory size of the added entry.
        /// </summary>
        public long EstimatedSize { get; }

        /// <summary>
        /// Gets whether this was an update to an existing entry.
        /// </summary>
        public bool IsUpdate { get; }

        /// <summary>
        /// Initializes a new instance of the EntryAddedEventArgs class.
        /// </summary>
        /// <param name="serviceId">The service identifier.</param>
        /// <param name="estimatedSize">The estimated memory size.</param>
        /// <param name="isUpdate">Whether this was an update.</param>
        public EntryAddedEventArgs(string serviceId, long estimatedSize, bool isUpdate)
            : base(serviceId)
        {
            EstimatedSize = estimatedSize;
            IsUpdate = isUpdate;
        }
    }

    /// <summary>
    /// Event arguments for entry evicted events.
    /// </summary>
    public sealed class EntryEvictedEventArgs : ServiceCacheEventArgs
    {
        /// <summary>
        /// Gets the reason for the eviction.
        /// </summary>
        public EvictionReason Reason { get; }

        /// <summary>
        /// Gets the access count for the evicted entry.
        /// </summary>
        public int AccessCount { get; }

        /// <summary>
        /// Gets the age of the evicted entry.
        /// </summary>
        public TimeSpan Age { get; }

        /// <summary>
        /// Gets the estimated memory size of the evicted entry.
        /// </summary>
        public long EstimatedSize { get; }

        /// <summary>
        /// Initializes a new instance of the EntryEvictedEventArgs class.
        /// </summary>
        /// <param name="serviceId">The service identifier.</param>
        /// <param name="reason">The eviction reason.</param>
        /// <param name="accessCount">The access count.</param>
        /// <param name="age">The entry age.</param>
        /// <param name="estimatedSize">The estimated memory size.</param>
        public EntryEvictedEventArgs(string serviceId, EvictionReason reason, int accessCount, TimeSpan age, long estimatedSize)
            : base(serviceId)
        {
            Reason = reason;
            AccessCount = accessCount;
            Age = age;
            EstimatedSize = estimatedSize;
        }
    }

    /// <summary>
    /// Event arguments for memory threshold warning events.
    /// </summary>
    public sealed class MemoryThresholdWarningEventArgs : EventArgs
    {
        /// <summary>
        /// Gets the current memory usage in bytes.
        /// </summary>
        public long CurrentMemoryBytes { get; }

        /// <summary>
        /// Gets the maximum memory limit in bytes.
        /// </summary>
        public long MaxMemoryBytes { get; }

        /// <summary>
        /// Gets the current memory utilization percentage.
        /// </summary>
        public double UtilizationPercent { get; }

        /// <summary>
        /// Gets the threshold percentage that triggered the warning.
        /// </summary>
        public int ThresholdPercent { get; }

        /// <summary>
        /// Gets the timestamp when the warning was triggered.
        /// </summary>
        public DateTimeOffset Timestamp { get; }

        /// <summary>
        /// Initializes a new instance of the MemoryThresholdWarningEventArgs class.
        /// </summary>
        /// <param name="currentMemoryBytes">The current memory usage.</param>
        /// <param name="maxMemoryBytes">The maximum memory limit.</param>
        /// <param name="thresholdPercent">The threshold percentage.</param>
        public MemoryThresholdWarningEventArgs(long currentMemoryBytes, long maxMemoryBytes, int thresholdPercent)
        {
            CurrentMemoryBytes = currentMemoryBytes;
            MaxMemoryBytes = maxMemoryBytes;
            ThresholdPercent = thresholdPercent;
            UtilizationPercent = maxMemoryBytes > 0 ? (double)currentMemoryBytes / maxMemoryBytes * 100 : 0;
            Timestamp = DateTimeOffset.UtcNow;
        }
    }

    /// <summary>
    /// Event arguments for cache cleared events.
    /// </summary>
    public sealed class CacheClearedEventArgs : EventArgs
    {
        /// <summary>
        /// Gets the number of entries that were cleared.
        /// </summary>
        public int EntriesCleared { get; }

        /// <summary>
        /// Gets the amount of memory freed in bytes.
        /// </summary>
        public long MemoryFreed { get; }

        /// <summary>
        /// Gets the reason for clearing the cache.
        /// </summary>
        public string Reason { get; }

        /// <summary>
        /// Gets the timestamp when the cache was cleared.
        /// </summary>
        public DateTimeOffset Timestamp { get; }

        /// <summary>
        /// Initializes a new instance of the CacheClearedEventArgs class.
        /// </summary>
        /// <param name="entriesCleared">The number of entries cleared.</param>
        /// <param name="memoryFreed">The amount of memory freed.</param>
        /// <param name="reason">The reason for clearing.</param>
        public CacheClearedEventArgs(int entriesCleared, long memoryFreed, string reason)
        {
            EntriesCleared = entriesCleared;
            MemoryFreed = memoryFreed;
            Reason = reason ?? "Unknown";
            Timestamp = DateTimeOffset.UtcNow;
        }
    }

    /// <summary>
    /// Defines reasons for cache misses.
    /// </summary>
    public enum CacheMissReason
    {
        /// <summary>
        /// Entry was not found in the cache.
        /// </summary>
        NotFound,

        /// <summary>
        /// Entry was found but had expired.
        /// </summary>
        Expired,

        /// <summary>
        /// Entry was found but failed validation.
        /// </summary>
        Invalid
    }

    /// <summary>
    /// Defines reasons for cache evictions.
    /// </summary>
    public enum EvictionReason
    {
        /// <summary>
        /// Entry expired based on TTL.
        /// </summary>
        Expired,

        /// <summary>
        /// Entry evicted due to cache size limit.
        /// </summary>
        SizeLimit,

        /// <summary>
        /// Entry evicted due to memory limit.
        /// </summary>
        MemoryLimit,

        /// <summary>
        /// Entry evicted by LRU policy.
        /// </summary>
        LeastRecentlyUsed,

        /// <summary>
        /// Entry evicted by LFU policy.
        /// </summary>
        LeastFrequentlyUsed,

        /// <summary>
        /// Entry manually removed.
        /// </summary>
        Manual
    }
}
