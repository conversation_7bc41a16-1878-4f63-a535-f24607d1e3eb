using System;
using System.Collections.Generic;
using System.Linq;

namespace USDP2
{
    /// <summary>
    /// Defines the interface for cache eviction policies.
    /// </summary>
    public interface IEvictionPolicy
    {
        /// <summary>
        /// Gets the name of the eviction policy.
        /// </summary>
        string Name { get; }

        /// <summary>
        /// Selects entries to evict from the cache.
        /// </summary>
        /// <param name="entries">The current cache entries.</param>
        /// <param name="targetCount">The number of entries to evict.</param>
        /// <returns>The entries to evict.</returns>
        IEnumerable<string> SelectEntriesForEviction(
            IReadOnlyDictionary<string, EnhancedCachedAdvertisement> entries, 
            int targetCount);

        /// <summary>
        /// Determines if an entry should be evicted based on policy-specific criteria.
        /// </summary>
        /// <param name="entry">The cache entry to evaluate.</param>
        /// <param name="currentTime">The current time.</param>
        /// <returns>True if the entry should be evicted, false otherwise.</returns>
        bool ShouldEvict(EnhancedCachedAdvertisement entry, DateTimeOffset currentTime);
    }

    /// <summary>
    /// Least Recently Used (LRU) eviction policy.
    /// Evicts entries that haven't been accessed recently.
    /// </summary>
    public sealed class LruEvictionPolicy : IEvictionPolicy
    {
        /// <inheritdoc/>
        public string Name => "LRU";

        /// <inheritdoc/>
        public IEnumerable<string> SelectEntriesForEviction(
            IReadOnlyDictionary<string, EnhancedCachedAdvertisement> entries, 
            int targetCount)
        {
            return entries
                .OrderBy(kvp => kvp.Value.LastAccessedAt)
                .Take(targetCount)
                .Select(kvp => kvp.Key);
        }

        /// <inheritdoc/>
        public bool ShouldEvict(EnhancedCachedAdvertisement entry, DateTimeOffset currentTime)
        {
            // LRU doesn't have specific eviction criteria beyond ordering
            return false;
        }
    }

    /// <summary>
    /// Least Frequently Used (LFU) eviction policy.
    /// Evicts entries with the lowest access count.
    /// </summary>
    public sealed class LfuEvictionPolicy : IEvictionPolicy
    {
        /// <inheritdoc/>
        public string Name => "LFU";

        /// <inheritdoc/>
        public IEnumerable<string> SelectEntriesForEviction(
            IReadOnlyDictionary<string, EnhancedCachedAdvertisement> entries, 
            int targetCount)
        {
            return entries
                .OrderBy(kvp => kvp.Value.AccessCount)
                .ThenBy(kvp => kvp.Value.LastAccessedAt) // Tie-breaker: older entries first
                .Take(targetCount)
                .Select(kvp => kvp.Key);
        }

        /// <inheritdoc/>
        public bool ShouldEvict(EnhancedCachedAdvertisement entry, DateTimeOffset currentTime)
        {
            // LFU doesn't have specific eviction criteria beyond ordering
            return false;
        }
    }

    /// <summary>
    /// Time To Live (TTL) eviction policy.
    /// Evicts entries based on expiration time only.
    /// </summary>
    public sealed class TtlEvictionPolicy : IEvictionPolicy
    {
        /// <inheritdoc/>
        public string Name => "TTL";

        /// <inheritdoc/>
        public IEnumerable<string> SelectEntriesForEviction(
            IReadOnlyDictionary<string, EnhancedCachedAdvertisement> entries, 
            int targetCount)
        {
            var currentTime = DateTimeOffset.UtcNow;
            
            // First, select expired entries
            var expiredEntries = entries
                .Where(kvp => kvp.Value.IsExpired(currentTime))
                .Select(kvp => kvp.Key)
                .ToList();

            if (expiredEntries.Count >= targetCount)
            {
                return expiredEntries.Take(targetCount);
            }

            // If not enough expired entries, select entries closest to expiration
            var remainingCount = targetCount - expiredEntries.Count;
            var nearExpirationEntries = entries
                .Where(kvp => !kvp.Value.IsExpired(currentTime) && kvp.Value.Expiry.HasValue)
                .OrderBy(kvp => kvp.Value.Expiry!.Value)
                .Take(remainingCount)
                .Select(kvp => kvp.Key);

            return expiredEntries.Concat(nearExpirationEntries);
        }

        /// <inheritdoc/>
        public bool ShouldEvict(EnhancedCachedAdvertisement entry, DateTimeOffset currentTime)
        {
            return entry.IsExpired(currentTime);
        }
    }

    /// <summary>
    /// Hybrid eviction policy that combines LRU and TTL strategies.
    /// Prioritizes expired entries, then uses LRU for remaining evictions.
    /// </summary>
    public sealed class HybridEvictionPolicy : IEvictionPolicy
    {
        /// <inheritdoc/>
        public string Name => "Hybrid";

        /// <inheritdoc/>
        public IEnumerable<string> SelectEntriesForEviction(
            IReadOnlyDictionary<string, EnhancedCachedAdvertisement> entries, 
            int targetCount)
        {
            var currentTime = DateTimeOffset.UtcNow;
            
            // First, select expired entries
            var expiredEntries = entries
                .Where(kvp => kvp.Value.IsExpired(currentTime))
                .Select(kvp => kvp.Key)
                .ToList();

            if (expiredEntries.Count >= targetCount)
            {
                return expiredEntries.Take(targetCount);
            }

            // For remaining evictions, use LRU policy on non-expired entries
            var remainingCount = targetCount - expiredEntries.Count;
            var lruEntries = entries
                .Where(kvp => !kvp.Value.IsExpired(currentTime))
                .OrderBy(kvp => kvp.Value.LastAccessedAt)
                .Take(remainingCount)
                .Select(kvp => kvp.Key);

            return expiredEntries.Concat(lruEntries);
        }

        /// <inheritdoc/>
        public bool ShouldEvict(EnhancedCachedAdvertisement entry, DateTimeOffset currentTime)
        {
            return entry.IsExpired(currentTime);
        }
    }

    /// <summary>
    /// Factory for creating eviction policy instances.
    /// </summary>
    public static class EvictionPolicyFactory
    {
        /// <summary>
        /// Creates an eviction policy instance based on the policy name.
        /// </summary>
        /// <param name="policyName">The name of the eviction policy.</param>
        /// <returns>An eviction policy instance.</returns>
        /// <exception cref="ArgumentException">Thrown when the policy name is not recognized.</exception>
        public static IEvictionPolicy CreatePolicy(string policyName)
        {
            return policyName?.ToUpperInvariant() switch
            {
                "LRU" => new LruEvictionPolicy(),
                "LFU" => new LfuEvictionPolicy(),
                "TTL" => new TtlEvictionPolicy(),
                "HYBRID" => new HybridEvictionPolicy(),
                _ => throw new ArgumentException($"Unknown eviction policy: {policyName}", nameof(policyName))
            };
        }

        /// <summary>
        /// Gets the names of all available eviction policies.
        /// </summary>
        /// <returns>An array of policy names.</returns>
        public static string[] GetAvailablePolicies()
        {
            return new[] { "LRU", "LFU", "TTL", "Hybrid" };
        }
    }
}
