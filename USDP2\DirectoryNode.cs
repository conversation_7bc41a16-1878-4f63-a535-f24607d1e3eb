using System;
using System.Threading;
using System.Threading.Tasks;

namespace USDP2
{
    /// <summary>
    /// The directory node with enhanced security features including rate limiting,
    /// DoS protection, and comprehensive security monitoring.
    /// </summary>
    public class DirectoryNode : IAsyncDisposable
    {
        /// <summary>
        /// The sender.
        /// </summary>
        private readonly INetworkSender _sender;
        /// <summary>
        /// The receiver.
        /// </summary>
        private readonly INetworkReceiver _receiver;
        /// <summary>
        /// The rate limiter for DoS protection.
        /// </summary>
        private readonly RateLimiter _rateLimiter;
        /// <summary>
        /// The security event tracker for monitoring and analysis.
        /// </summary>
        private readonly SecurityEventTracker _securityTracker;
        /// <summary>
        /// The configuration instance.
        /// </summary>
        private readonly UsdpConfiguration _config;

        /// <summary>
        /// Initializes a new instance of the <see cref="DirectoryNode"/> class.
        /// </summary>
        /// <param name="sender">The sender.</param>
        /// <param name="receiver">The receiver.</param>
        /// <param name="config">The configuration instance. If null, uses the singleton instance.</param>
        public DirectoryNode(INetworkSender sender, INetworkReceiver receiver, UsdpConfiguration? config = null)
        {
            _sender = sender ?? throw new ArgumentNullException(nameof(sender));
            _receiver = receiver ?? throw new ArgumentNullException(nameof(receiver));
            _config = config ?? UsdpConfiguration.Instance;
            _rateLimiter = new RateLimiter(_config);
            _securityTracker = new SecurityEventTracker(_config);
        }

        /// <summary>
        /// TODO: Add Summary.
        /// </summary>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>A <see cref="Task"/></returns>
        public async Task StartAsync(CancellationToken cancellationToken = default)
        {
            await _receiver.StartReceivingAsync(OnMessageReceivedAsync, cancellationToken);
        }

        /// <summary>
        /// On message received asynchronously with enhanced security validation.
        /// </summary>
        /// <param name="data">The data.</param>
        /// <param name="remoteAddress">The remote address.</param>
        /// <param name="remotePort">The remote port.</param>
        /// <returns>A <see cref="Task"/></returns>
        private async Task OnMessageReceivedAsync(byte[] data, string remoteAddress, int remotePort)
        {
            try
            {
                // Check if endpoint is blocked
                if (_securityTracker.IsEndpointBlocked(remoteAddress, remotePort))
                {
                    _securityTracker.RecordEvent(SecurityEventType.SecurityViolation, remoteAddress, remotePort,
                        "Message received from blocked endpoint", SecurityEventSeverity.High);

                    UsdpLogger.Log("DirectoryNode.BlockedEndpointMessage", new
                    {
                        RemoteAddress = remoteAddress,
                        RemotePort = remotePort,
                        DataSize = data?.Length ?? 0,
                        Severity = "Warning"
                    });
                    return;
                }

                // Check rate limits
                var rateLimitResult = _rateLimiter.CheckRateLimit(remoteAddress, remotePort, data?.Length ?? 0);
                if (!rateLimitResult.IsAllowed)
                {
                    _securityTracker.RecordEvent(SecurityEventType.RateLimitExceeded, remoteAddress, remotePort,
                        rateLimitResult.RejectionReason ?? "Rate limit exceeded", SecurityEventSeverity.Medium);

                    UsdpLogger.Log("DirectoryNode.RateLimitExceeded", new
                    {
                        RemoteAddress = remoteAddress,
                        RemotePort = remotePort,
                        DataSize = data?.Length ?? 0,
                        CurrentCount = rateLimitResult.CurrentCount,
                        MaxCount = rateLimitResult.MaxCount,
                        Reason = rateLimitResult.RejectionReason,
                        Severity = "Warning"
                    });
                    return;
                }

                // Basic data validation
                if (data == null || data.Length == 0)
                {
                    _securityTracker.RecordEvent(SecurityEventType.InvalidData, remoteAddress, remotePort,
                        "Empty or null data received", SecurityEventSeverity.Low);
                    return;
                }

                // Check for oversized messages
                if (data.Length > _config.MaxNetworkDataSize)
                {
                    _securityTracker.RecordEvent(SecurityEventType.OversizedMessage, remoteAddress, remotePort,
                        $"Message size {data.Length} exceeds limit {_config.MaxNetworkDataSize}", SecurityEventSeverity.High);

                    UsdpLogger.Log("DirectoryNode.OversizedMessage", new
                    {
                        RemoteAddress = remoteAddress,
                        RemotePort = remotePort,
                        DataSize = data.Length,
                        MaxAllowed = _config.MaxNetworkDataSize,
                        Severity = "Warning"
                    });
                    return;
                }

                // Try to deserialize as ServiceAdvertisement with validation
                if (TryDeserializeServiceAdvertisement(data, out var advertisement, remoteAddress, remotePort))
                {
                    await HandleServiceAdvertisementAsync(advertisement!, remoteAddress, remotePort);
                    return;
                }

                // Try to deserialize as ServiceQuery with validation
                if (TryDeserializeServiceQuery(data, out var query, remoteAddress, remotePort))
                {
                    await HandleServiceQueryAsync(query!, remoteAddress, remotePort);
                    return;
                }

                // Unknown message type - record security event and log for monitoring
                _securityTracker.RecordEvent(SecurityEventType.UnknownMessageType, remoteAddress, remotePort,
                    $"Unknown message type, size: {data.Length}", SecurityEventSeverity.Low);

                UsdpLogger.Log("DirectoryNode.UnknownMessageType", new
                {
                    RemoteAddress = remoteAddress,
                    RemotePort = remotePort,
                    DataSize = data?.Length ?? 0,
                    Severity = "Info"
                });
            }
            catch (Exception ex)
            {
                _securityTracker.RecordEvent(SecurityEventType.SecurityViolation, remoteAddress, remotePort,
                    $"Exception in message processing: {ex.Message}", SecurityEventSeverity.High);

                UsdpLogger.Log("DirectoryNode.MessageProcessingError", new
                {
                    RemoteAddress = remoteAddress,
                    RemotePort = remotePort,
                    Error = ex.Message,
                    Severity = "Error"
                });
            }
        }

        /// <summary>
        /// Try deserialize service advertisement with comprehensive validation and sanitization.
        /// </summary>
        /// <param name="data">The data.</param>
        /// <param name="advertisement">The advertisement.</param>
        /// <param name="remoteAddress">The remote address for validation logging.</param>
        /// <param name="remotePort">The remote port for validation logging.</param>
        /// <returns>A <see cref="bool"/></returns>
        private bool TryDeserializeServiceAdvertisement(
            byte[] data,
            out ServiceAdvertisement? advertisement,
            string remoteAddress = "unknown",
            int remotePort = 0)
        {
            advertisement = null;

            // First validate the incoming network data
            var dataValidation = NetworkDataValidator.ValidateIncomingData(data, remoteAddress, remotePort);
            if (!dataValidation.IsValid)
            {
                _securityTracker.RecordEvent(SecurityEventType.InvalidData, remoteAddress, remotePort,
                    $"Network data validation failed: {dataValidation.ErrorMessage}", SecurityEventSeverity.Medium);

                UsdpLogger.Log("DirectoryNode.InvalidNetworkData", new
                {
                    RemoteAddress = remoteAddress,
                    RemotePort = remotePort,
                    ErrorType = dataValidation.ErrorType,
                    ErrorMessage = dataValidation.ErrorMessage,
                    DataSize = data?.Length ?? 0,
                    Severity = "Warning"
                });
                return false;
            }

            // Try CBOR deserialization first
            try
            {
                var cborResult = ServiceAdvertisement.FromCborWithResult(data);
                if (cborResult.IsSuccess && cborResult.Value != null)
                {
                    // Validate and sanitize the service advertisement
                    var advValidation = NetworkDataValidator.ValidateServiceAdvertisement(
                        cborResult.Value, remoteAddress, remotePort);

                    if (advValidation.IsValid && advValidation.SanitizedAdvertisement != null)
                    {
                        advertisement = advValidation.SanitizedAdvertisement;
                        return true;
                    }
                    else
                    {
                        _securityTracker.RecordEvent(SecurityEventType.ValidationFailure, remoteAddress, remotePort,
                            $"Service advertisement validation failed: {advValidation.ErrorMessage}", SecurityEventSeverity.Medium);

                        UsdpLogger.Log("DirectoryNode.ServiceAdvertisementValidationFailed", new
                        {
                            RemoteAddress = remoteAddress,
                            RemotePort = remotePort,
                            ErrorMessage = advValidation.ErrorMessage,
                            Severity = "Warning"
                        });
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                UsdpLogger.Log("DirectoryNode.CborDeserializationError", new
                {
                    RemoteAddress = remoteAddress,
                    RemotePort = remotePort,
                    Error = ex.Message,
                    Severity = "Debug"
                });
            }

            // Try JSON deserialization as fallback
            try
            {
                var json = System.Text.Encoding.UTF8.GetString(data);
                var jsonResult = ServiceAdvertisement.FromJsonWithResult(json);

                if (jsonResult.IsSuccess && jsonResult.Value != null)
                {
                    // Validate and sanitize the service advertisement
                    var advValidation = NetworkDataValidator.ValidateServiceAdvertisement(
                        jsonResult.Value, remoteAddress, remotePort);

                    if (advValidation.IsValid && advValidation.SanitizedAdvertisement != null)
                    {
                        advertisement = advValidation.SanitizedAdvertisement;
                        return true;
                    }
                    else
                    {
                        UsdpLogger.Log("DirectoryNode.ServiceAdvertisementValidationFailed", new
                        {
                            RemoteAddress = remoteAddress,
                            RemotePort = remotePort,
                            ErrorMessage = advValidation.ErrorMessage,
                            Severity = "Warning"
                        });
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                UsdpLogger.Log("DirectoryNode.JsonDeserializationError", new
                {
                    RemoteAddress = remoteAddress,
                    RemotePort = remotePort,
                    Error = ex.Message,
                    Severity = "Debug"
                });
            }

            // Log failed deserialization attempt
            UsdpLogger.Log("DirectoryNode.DeserializationFailed", new
            {
                RemoteAddress = remoteAddress,
                RemotePort = remotePort,
                DataSize = data?.Length ?? 0,
                Severity = "Info"
            });

            return false;
        }

        /// <summary>
        /// Try deserialize service query with comprehensive validation and sanitization.
        /// </summary>
        /// <param name="data">The data.</param>
        /// <param name="query">The query.</param>
        /// <param name="remoteAddress">The remote address for validation logging.</param>
        /// <param name="remotePort">The remote port for validation logging.</param>
        /// <returns>A <see cref="bool"/></returns>
        private bool TryDeserializeServiceQuery(
            byte[] data,
            out ServiceQuery? query,
            string remoteAddress = "unknown",
            int remotePort = 0)
        {
            query = null;

            // First validate the incoming network data
            var dataValidation = NetworkDataValidator.ValidateIncomingData(data, remoteAddress, remotePort);
            if (!dataValidation.IsValid)
            {
                _securityTracker.RecordEvent(SecurityEventType.InvalidData, remoteAddress, remotePort,
                    $"Query data validation failed: {dataValidation.ErrorMessage}", SecurityEventSeverity.Medium);

                UsdpLogger.Log("DirectoryNode.InvalidNetworkDataForQuery", new
                {
                    RemoteAddress = remoteAddress,
                    RemotePort = remotePort,
                    ErrorType = dataValidation.ErrorType,
                    ErrorMessage = dataValidation.ErrorMessage,
                    DataSize = data?.Length ?? 0,
                    Severity = "Warning"
                });
                return false;
            }

            // Try CBOR deserialization first
            try
            {
                var cborResult = ServiceQuery.FromCborWithResult(data);
                if (cborResult.IsSuccess && cborResult.Value != null)
                {
                    // Basic validation for service query
                    if (ValidateServiceQuery(cborResult.Value, remoteAddress, remotePort))
                    {
                        query = cborResult.Value;
                        return true;
                    }
                    return false;
                }
            }
            catch (Exception ex)
            {
                UsdpLogger.Log("DirectoryNode.QueryCborDeserializationError", new
                {
                    RemoteAddress = remoteAddress,
                    RemotePort = remotePort,
                    Error = ex.Message,
                    Severity = "Debug"
                });
            }

            // Try JSON deserialization as fallback
            try
            {
                var json = System.Text.Encoding.UTF8.GetString(data);
                var jsonResult = ServiceQuery.FromJsonWithResult(json);

                if (jsonResult.IsSuccess && jsonResult.Value != null)
                {
                    // Basic validation for service query
                    if (ValidateServiceQuery(jsonResult.Value, remoteAddress, remotePort))
                    {
                        query = jsonResult.Value;
                        return true;
                    }
                    return false;
                }
            }
            catch (Exception ex)
            {
                UsdpLogger.Log("DirectoryNode.QueryJsonDeserializationError", new
                {
                    RemoteAddress = remoteAddress,
                    RemotePort = remotePort,
                    Error = ex.Message,
                    Severity = "Debug"
                });
            }

            return false;
        }

        /// <summary>
        /// Validates a service query for security and data integrity.
        /// </summary>
        /// <param name="query">The service query to validate.</param>
        /// <param name="remoteAddress">The remote address for logging.</param>
        /// <param name="remotePort">The remote port for logging.</param>
        /// <returns>True if the query is valid, false otherwise.</returns>
        private bool ValidateServiceQuery(ServiceQuery query, string remoteAddress, int remotePort)
        {
            try
            {
                // Validate query is not null
                if (query == null)
                {
                    _securityTracker.RecordEvent(SecurityEventType.InvalidData, remoteAddress, remotePort,
                        "Null service query received", SecurityEventSeverity.Medium);

                    UsdpLogger.Log("DirectoryNode.NullServiceQuery", new
                    {
                        RemoteAddress = remoteAddress,
                        RemotePort = remotePort,
                        Severity = "Warning"
                    });
                    return false;
                }

                // Validate SID filter length
                if (!string.IsNullOrEmpty(query.SidFilter) &&
                    query.SidFilter.Length > NetworkDataValidator.MaxStringLength)
                {
                    UsdpLogger.Log("DirectoryNode.OversizedSidFilter", new
                    {
                        RemoteAddress = remoteAddress,
                        RemotePort = remotePort,
                        SidFilterLength = query.SidFilter.Length,
                        MaxAllowed = NetworkDataValidator.MaxStringLength,
                        Severity = "Warning"
                    });
                    return false;
                }

                // Validate metadata filter entries
                if (query.MetadataFilter != null &&
                    query.MetadataFilter.Count > NetworkDataValidator.MaxMetadataEntries)
                {
                    UsdpLogger.Log("DirectoryNode.TooManyMetadataFilters", new
                    {
                        RemoteAddress = remoteAddress,
                        RemotePort = remotePort,
                        FilterCount = query.MetadataFilter.Count,
                        MaxAllowed = NetworkDataValidator.MaxMetadataEntries,
                        Severity = "Warning"
                    });
                    return false;
                }

                // Validate individual metadata filter values
                if (query.MetadataFilter != null)
                {
                    foreach (var kvp in query.MetadataFilter)
                    {
                        if (kvp.Key.Length > NetworkDataValidator.MaxStringLength ||
                            kvp.Value.Length > NetworkDataValidator.MaxStringLength)
                        {
                            UsdpLogger.Log("DirectoryNode.OversizedMetadataFilter", new
                            {
                                RemoteAddress = remoteAddress,
                                RemotePort = remotePort,
                                Key = kvp.Key,
                                KeyLength = kvp.Key.Length,
                                ValueLength = kvp.Value.Length,
                                MaxAllowed = NetworkDataValidator.MaxStringLength,
                                Severity = "Warning"
                            });
                            return false;
                        }
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                UsdpLogger.Log("DirectoryNode.ServiceQueryValidationError", new
                {
                    RemoteAddress = remoteAddress,
                    RemotePort = remotePort,
                    Error = ex.Message,
                    Severity = "Error"
                });
                return false;
            }
        }

        // Mark as static and discard unused parameters
        /// <summary>
        /// Handle service advertisement asynchronously.
        /// </summary>
        /// <param name="_advertisement">The advertisement.</param>
        /// <param name="_remoteAddress">The remote address.</param>
        /// <param name="_remotePort">The remote port.</param>
        /// <returns>A <see cref="Task"/></returns>
        private static Task HandleServiceAdvertisementAsync(ServiceAdvertisement _advertisement, string _remoteAddress, int _remotePort)
        {
            // TODO: Add logic to cache, forward, or respond to advertisements
            return Task.CompletedTask;
        }

        // Mark as static and discard unused parameters
        /// <summary>
        /// Handle service query asynchronously.
        /// </summary>
        /// <param name="_query">The query.</param>
        /// <param name="_remoteAddress">The remote address.</param>
        /// <param name="_remotePort">The remote port.</param>
        /// <returns>A <see cref="Task"/></returns>
        private static Task HandleServiceQueryAsync(ServiceQuery _query, string _remoteAddress, int _remotePort)
        {
            // TODO: Add logic to match services and respond
            // Example: Respond with a ServiceAdvertisement if a match is found
            // var response = ...;
            // await AdvertiseServiceAsync(response, remoteAddress, remotePort);
            return Task.CompletedTask;
        }

        /// <summary>
        /// Advertises the service asynchronously.
        /// </summary>
        /// <param name="advertisement">The advertisement.</param>
        /// <param name="address">The address.</param>
        /// <param name="port">The port.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>A <see cref="Task"/></returns>
        public async Task AdvertiseServiceAsync(ServiceAdvertisement advertisement, string address, int port, CancellationToken cancellationToken = default)
        {
            var data = advertisement.ToCbor();
            await _sender.SendAsync(data, address, port, cancellationToken);
        }

        /// <summary>
        /// Disposes the directory node and releases all resources including security components.
        /// </summary>
        /// <returns>A <see cref="ValueTask"/></returns>
        public async ValueTask DisposeAsync()
        {
            try
            {
                // Dispose security components
                _rateLimiter?.Dispose();
                _securityTracker?.Dispose();

                // Dispose network components
                await _receiver.DisposeAsync().ConfigureAwait(false);

                if (_sender is IAsyncDisposable asyncDisposableSender)
                {
                    await asyncDisposableSender.DisposeAsync().ConfigureAwait(false);
                }
                else if (_sender is IDisposable disposableSender)
                {
                    disposableSender.Dispose();
                }
            }
            catch (Exception ex)
            {
                UsdpLogger.Log("DirectoryNode.DisposeError", new
                {
                    Error = ex.Message,
                    Severity = "Error"
                });
            }
            finally
            {
                GC.SuppressFinalize(this);
            }
        }

        /// <summary>
        /// Gets security statistics for monitoring and reporting.
        /// </summary>
        /// <returns>Security statistics including rate limiting and threat information.</returns>
        public SecurityStatistics GetSecurityStatistics()
        {
            return _securityTracker.GetSecurityStatistics();
        }

        /// <summary>
        /// Gets rate limiting statistics for monitoring and debugging.
        /// </summary>
        /// <returns>Rate limiting statistics for all tracked endpoints.</returns>
        public Dictionary<string, RateLimitStats> GetRateLimitStatistics()
        {
            return _rateLimiter.GetStatistics();
        }

        /// <summary>
        /// Manually blocks an endpoint for security reasons.
        /// </summary>
        /// <param name="remoteAddress">The remote IP address to block.</param>
        /// <param name="remotePort">The remote port number to block.</param>
        /// <param name="reason">The reason for blocking.</param>
        /// <param name="duration">The duration to block for. If null, blocks indefinitely.</param>
        public void BlockEndpoint(string remoteAddress, int remotePort, string reason, TimeSpan? duration = null)
        {
            _securityTracker.BlockEndpoint(remoteAddress, remotePort, reason, duration);
        }

        /// <summary>
        /// Manually unblocks an endpoint.
        /// </summary>
        /// <param name="remoteAddress">The remote IP address to unblock.</param>
        /// <param name="remotePort">The remote port number to unblock.</param>
        public void UnblockEndpoint(string remoteAddress, int remotePort)
        {
            _securityTracker.UnblockEndpoint(remoteAddress, remotePort);
        }
    }
}