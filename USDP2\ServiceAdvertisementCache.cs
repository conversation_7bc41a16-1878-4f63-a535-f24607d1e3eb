using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace USDP2
{
    /// <summary>
    /// Enhanced service advertisement cache with comprehensive caching features.
    ///
    /// Features:
    /// - Configurable eviction policies (LRU, LFU, TTL, Hybrid)
    /// - Memory usage limits and monitoring
    /// - Detailed cache statistics and performance metrics
    /// - Event notifications for cache operations
    /// - Thread-safe operations with minimal locking
    /// - Automatic cleanup and maintenance
    /// </summary>
    public class ServiceAdvertisementCache : IDisposable
    {
        #region Fields

        /// <summary>
        /// The main cache storage using enhanced cached advertisements.
        /// </summary>
        private readonly ConcurrentDictionary<string, EnhancedCachedAdvertisement> _cache = new();

        /// <summary>
        /// The eviction policy used for cache management.
        /// </summary>
        private readonly IEvictionPolicy _evictionPolicy;

        /// <summary>
        /// Timer for periodic maintenance operations.
        /// </summary>
        private readonly Timer _maintenanceTimer;

        /// <summary>
        /// Configuration settings for the cache.
        /// </summary>
        private readonly UsdpConfiguration _config;

        /// <summary>
        /// Cache statistics tracking.
        /// </summary>
        private readonly ServiceCacheStatistics _statistics;

        /// <summary>
        /// Lock for statistics updates to ensure consistency.
        /// </summary>
        private readonly object _statisticsLock = new object();

        /// <summary>
        /// Flag indicating if the cache has been disposed.
        /// </summary>
        private volatile bool _disposed = false;

        #endregion

        #region Events

        /// <summary>
        /// Raised when a cache hit occurs.
        /// </summary>
        public event EventHandler<CacheHitEventArgs>? CacheHit;

        /// <summary>
        /// Raised when a cache miss occurs.
        /// </summary>
        public event EventHandler<CacheMissEventArgs>? CacheMiss;

        /// <summary>
        /// Raised when an entry is added to the cache.
        /// </summary>
        public event EventHandler<EntryAddedEventArgs>? EntryAdded;

        /// <summary>
        /// Raised when an entry is evicted from the cache.
        /// </summary>
        public event EventHandler<EntryEvictedEventArgs>? EntryEvicted;

        /// <summary>
        /// Raised when memory usage exceeds the warning threshold.
        /// </summary>
        public event EventHandler<MemoryThresholdWarningEventArgs>? MemoryThresholdWarning;

        /// <summary>
        /// Raised when the cache is cleared.
        /// </summary>
        public event EventHandler<CacheClearedEventArgs>? CacheCleared;

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the <see cref="ServiceAdvertisementCache"/> class.
        /// Uses configuration settings from UsdpConfiguration.Instance.
        /// </summary>
        public ServiceAdvertisementCache() : this(UsdpConfiguration.Instance)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="ServiceAdvertisementCache"/> class.
        /// </summary>
        /// <param name="config">The configuration to use for cache settings.</param>
        /// <exception cref="ArgumentNullException">Thrown when config is null.</exception>
        public ServiceAdvertisementCache(UsdpConfiguration config)
        {
            _config = config ?? throw new ArgumentNullException(nameof(config));

            // Initialize eviction policy
            _evictionPolicy = EvictionPolicyFactory.CreatePolicy(_config.ServiceCacheEvictionPolicy);

            // Initialize statistics
            _statistics = new ServiceCacheStatistics
            {
                MaxSize = _config.ServiceCacheMaxSize,
                MaxMemoryBytes = _config.ServiceCacheMaxMemoryBytes
            };

            // Start maintenance timer
            _maintenanceTimer = new Timer(
                PerformMaintenance,
                null,
                _config.ServiceCacheMaintenanceInterval,
                _config.ServiceCacheMaintenanceInterval);

            UsdpLogger.Log("ServiceCache.Initialized", new
            {
                MaxSize = _config.ServiceCacheMaxSize,
                MaxMemoryMB = _config.ServiceCacheMaxMemoryBytes / (1024 * 1024),
                EvictionPolicy = _evictionPolicy.Name,
                MaintenanceInterval = _config.ServiceCacheMaintenanceInterval
            });
        }

        #endregion

        #region Public API

        /// <summary>
        /// Adds or updates a service advertisement in the cache.
        /// If the cache is at capacity, entries will be evicted according to the configured policy.
        /// </summary>
        /// <param name="advertisement">The service advertisement to cache.</param>
        /// <exception cref="ArgumentNullException">Thrown when advertisement is null.</exception>
        /// <exception cref="ObjectDisposedException">Thrown when the cache has been disposed.</exception>
        public void AddOrUpdate(ServiceAdvertisement advertisement)
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(ServiceAdvertisementCache));

            ArgumentNullException.ThrowIfNull(advertisement, nameof(advertisement));

            var key = advertisement.ServiceId.ToString();
            var expiry = advertisement.Ttl.HasValue ? advertisement.Timestamp + advertisement.Ttl.Value : (DateTimeOffset?)null;
            var isUpdate = _cache.ContainsKey(key);

            // Create enhanced cached entry
            var cachedEntry = new EnhancedCachedAdvertisement(advertisement, expiry);

            // Add or update the entry
            _cache.AddOrUpdate(key, cachedEntry, (k, existing) => cachedEntry);

            // Update statistics
            lock (_statisticsLock)
            {
                if (!isUpdate)
                {
                    _statistics.TotalAdds++;
                    _statistics.CurrentSize = _cache.Count;
                }
                _statistics.CurrentMemoryBytes = CalculateCurrentMemoryUsage();
                _statistics.LastUpdateTime = DateTimeOffset.UtcNow;
            }

            // Raise event if enabled
            if (_config.ServiceCacheEventsEnabled)
            {
                EntryAdded?.Invoke(this, new EntryAddedEventArgs(key, cachedEntry.EstimatedMemorySize, isUpdate));
            }

            // Check if we need to evict entries
            CheckAndEnforceCapacityLimits();

            UsdpLogger.Log("ServiceCache.EntryAdded", new
            {
                ServiceId = key,
                IsUpdate = isUpdate,
                EstimatedSize = cachedEntry.EstimatedMemorySize,
                CacheSize = _cache.Count
            });
        }


        /// <summary>
        /// Gets all active (non-expired) service advertisements from the cache.
        /// This method updates access statistics for retrieved entries.
        /// </summary>
        /// <returns>An enumerable of active service advertisements.</returns>
        /// <exception cref="ObjectDisposedException">Thrown when the cache has been disposed.</exception>
        public IEnumerable<ServiceAdvertisement> GetActiveAdvertisements()
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(ServiceAdvertisementCache));

            var now = DateTimeOffset.UtcNow;
            var activeAdvertisements = new List<ServiceAdvertisement>();
            var hitCount = 0;
            var missCount = 0;

            foreach (var kvp in _cache)
            {
                var entry = kvp.Value;

                if (entry.IsExpired(now))
                {
                    missCount++;
                    if (_config.ServiceCacheEventsEnabled)
                    {
                        CacheMiss?.Invoke(this, new CacheMissEventArgs(kvp.Key, CacheMissReason.Expired));
                    }
                    continue;
                }

                // Mark as accessed and count as hit
                entry.MarkAccessed();
                activeAdvertisements.Add(entry.Advertisement);
                hitCount++;

                if (_config.ServiceCacheEventsEnabled)
                {
                    var stats = entry.GetAccessStatistics();
                    CacheHit?.Invoke(this, new CacheHitEventArgs(kvp.Key, stats.AccessCount, entry.TimeSinceLastAccess));
                }
            }

            // Update statistics
            lock (_statisticsLock)
            {
                _statistics.TotalHits += hitCount;
                _statistics.TotalMisses += missCount;
                _statistics.LastUpdateTime = DateTimeOffset.UtcNow;
            }

            return activeAdvertisements;
        }

        /// <summary>
        /// Gets a specific service advertisement by service identifier.
        /// </summary>
        /// <param name="serviceId">The service identifier to look up.</param>
        /// <returns>The service advertisement if found and active, null otherwise.</returns>
        /// <exception cref="ArgumentNullException">Thrown when serviceId is null.</exception>
        /// <exception cref="ObjectDisposedException">Thrown when the cache has been disposed.</exception>
        public ServiceAdvertisement? GetAdvertisement(ServiceIdentifier serviceId)
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(ServiceAdvertisementCache));

            ArgumentNullException.ThrowIfNull(serviceId, nameof(serviceId));

            var key = serviceId.ToString();
            var now = DateTimeOffset.UtcNow;

            if (_cache.TryGetValue(key, out var entry))
            {
                if (entry.IsExpired(now))
                {
                    // Update statistics
                    lock (_statisticsLock)
                    {
                        _statistics.TotalMisses++;
                        _statistics.LastUpdateTime = DateTimeOffset.UtcNow;
                    }

                    if (_config.ServiceCacheEventsEnabled)
                    {
                        CacheMiss?.Invoke(this, new CacheMissEventArgs(key, CacheMissReason.Expired));
                    }

                    return null;
                }

                // Mark as accessed and count as hit
                entry.MarkAccessed();

                // Update statistics
                lock (_statisticsLock)
                {
                    _statistics.TotalHits++;
                    _statistics.LastUpdateTime = DateTimeOffset.UtcNow;
                }

                if (_config.ServiceCacheEventsEnabled)
                {
                    var stats = entry.GetAccessStatistics();
                    CacheHit?.Invoke(this, new CacheHitEventArgs(key, stats.AccessCount, entry.TimeSinceLastAccess));
                }

                return entry.Advertisement;
            }

            // Cache miss - entry not found
            lock (_statisticsLock)
            {
                _statistics.TotalMisses++;
                _statistics.LastUpdateTime = DateTimeOffset.UtcNow;
            }

            if (_config.ServiceCacheEventsEnabled)
            {
                CacheMiss?.Invoke(this, new CacheMissEventArgs(key, CacheMissReason.NotFound));
            }

            return null;
        }

        /// <summary>
        /// Processes a heartbeat for a service, updating its timestamp if cached.
        /// </summary>
        /// <param name="serviceId">The service identifier.</param>
        /// <exception cref="ArgumentNullException">Thrown when serviceId is null.</exception>
        /// <exception cref="ObjectDisposedException">Thrown when the cache has been disposed.</exception>
        public void ReceiveHeartbeat(ServiceIdentifier serviceId)
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(ServiceAdvertisementCache));

            ArgumentNullException.ThrowIfNull(serviceId, nameof(serviceId));

            var key = serviceId.ToString();

            if (_cache.TryGetValue(key, out var cached))
            {
                // Update timestamp to now for ephemeral services
                var advertisement = cached.Advertisement;
                advertisement.Timestamp = DateTimeOffset.UtcNow;
                AddOrUpdate(advertisement);

                UsdpLogger.Log("ServiceCache.HeartbeatReceived", new { ServiceId = key });
            }
            else
            {
                UsdpLogger.Log("ServiceCache.HeartbeatForUnknownService", new { ServiceId = key });
            }
        }

        /// <summary>
        /// Gets current cache statistics.
        /// </summary>
        /// <returns>A copy of the current cache statistics.</returns>
        /// <exception cref="ObjectDisposedException">Thrown when the cache has been disposed.</exception>
        public ServiceCacheStatistics GetStatistics()
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(ServiceAdvertisementCache));

            lock (_statisticsLock)
            {
                _statistics.CurrentSize = _cache.Count;
                _statistics.CurrentMemoryBytes = CalculateCurrentMemoryUsage();
                _statistics.LastUpdateTime = DateTimeOffset.UtcNow;
                return _statistics.Clone();
            }
        }

        /// <summary>
        /// Clears all entries from the cache.
        /// </summary>
        /// <param name="reason">The reason for clearing the cache.</param>
        /// <exception cref="ObjectDisposedException">Thrown when the cache has been disposed.</exception>
        public void Clear(string reason = "Manual")
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(ServiceAdvertisementCache));

            var entriesCleared = _cache.Count;
            var memoryFreed = CalculateCurrentMemoryUsage();

            _cache.Clear();

            // Update statistics
            lock (_statisticsLock)
            {
                _statistics.CurrentSize = 0;
                _statistics.CurrentMemoryBytes = 0;
                _statistics.LastUpdateTime = DateTimeOffset.UtcNow;
            }

            // Raise event if enabled
            if (_config.ServiceCacheEventsEnabled)
            {
                CacheCleared?.Invoke(this, new CacheClearedEventArgs(entriesCleared, memoryFreed, reason));
            }

            UsdpLogger.Log("ServiceCache.Cleared", new
            {
                EntriesCleared = entriesCleared,
                MemoryFreedMB = memoryFreed / (1024 * 1024),
                Reason = reason
            });
        }

        /// <summary>
        /// Removes a specific service advertisement from the cache.
        /// </summary>
        /// <param name="serviceId">The service identifier to remove.</param>
        /// <returns>True if the entry was removed, false if it wasn't found.</returns>
        /// <exception cref="ArgumentNullException">Thrown when serviceId is null.</exception>
        /// <exception cref="ObjectDisposedException">Thrown when the cache has been disposed.</exception>
        public bool Remove(ServiceIdentifier serviceId)
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(ServiceAdvertisementCache));

            ArgumentNullException.ThrowIfNull(serviceId, nameof(serviceId));

            var key = serviceId.ToString();

            if (_cache.TryRemove(key, out var removed))
            {
                // Update statistics
                lock (_statisticsLock)
                {
                    _statistics.CurrentSize = _cache.Count;
                    _statistics.CurrentMemoryBytes = CalculateCurrentMemoryUsage();
                    _statistics.TotalEvictions++;
                    _statistics.LastUpdateTime = DateTimeOffset.UtcNow;
                }

                // Raise event if enabled
                if (_config.ServiceCacheEventsEnabled)
                {
                    var stats = removed.GetAccessStatistics();
                    EntryEvicted?.Invoke(this, new EntryEvictedEventArgs(
                        key,
                        EvictionReason.Manual,
                        stats.AccessCount,
                        removed.Age,
                        removed.EstimatedMemorySize));
                }

                UsdpLogger.Log("ServiceCache.EntryRemoved", new { ServiceId = key });
                return true;
            }

            return false;
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Performs periodic maintenance operations on the cache.
        /// </summary>
        /// <param name="state">Timer state (unused).</param>
        private void PerformMaintenance(object? state)
        {
            if (_disposed)
                return;

            try
            {
                var now = DateTimeOffset.UtcNow;
                var expiredKeys = new List<string>();
                var totalEvictions = 0;

                // Find expired entries
                foreach (var kvp in _cache)
                {
                    if (kvp.Value.IsExpired(now))
                    {
                        expiredKeys.Add(kvp.Key);
                    }
                }

                // Remove expired entries
                foreach (var key in expiredKeys)
                {
                    if (_cache.TryRemove(key, out var removed))
                    {
                        totalEvictions++;

                        // Update statistics
                        lock (_statisticsLock)
                        {
                            _statistics.TotalEvictions++;
                            _statistics.TtlEvictions++;
                        }

                        // Raise event if enabled
                        if (_config.ServiceCacheEventsEnabled)
                        {
                            var stats = removed.GetAccessStatistics();
                            EntryEvicted?.Invoke(this, new EntryEvictedEventArgs(
                                key,
                                EvictionReason.Expired,
                                stats.AccessCount,
                                removed.Age,
                                removed.EstimatedMemorySize));
                        }
                    }
                }

                // Update statistics
                lock (_statisticsLock)
                {
                    _statistics.CurrentSize = _cache.Count;
                    _statistics.CurrentMemoryBytes = CalculateCurrentMemoryUsage();
                    _statistics.LastUpdateTime = DateTimeOffset.UtcNow;
                }

                // Check memory threshold
                CheckMemoryThreshold();

                // Enforce capacity limits
                CheckAndEnforceCapacityLimits();

                if (totalEvictions > 0)
                {
                    UsdpLogger.Log("ServiceCache.MaintenanceCompleted", new
                    {
                        ExpiredEvictions = totalEvictions,
                        CurrentSize = _cache.Count,
                        MemoryUsageMB = CalculateCurrentMemoryUsage() / (1024 * 1024)
                    });
                }
            }
            catch (Exception ex)
            {
                UsdpLogger.Log("ServiceCache.MaintenanceError", new { Error = ex.Message });
            }
        }

        /// <summary>
        /// Checks and enforces cache capacity limits (size and memory).
        /// </summary>
        private void CheckAndEnforceCapacityLimits()
        {
            if (_disposed)
                return;

            var currentSize = _cache.Count;
            var currentMemory = CalculateCurrentMemoryUsage();
            var maxSize = _config.ServiceCacheMaxSize;
            var maxMemory = _config.ServiceCacheMaxMemoryBytes;

            // Check if we need to evict entries
            var needsEviction = currentSize > maxSize || currentMemory > maxMemory;

            if (!needsEviction)
                return;

            // Calculate how many entries to evict
            var targetEvictions = Math.Max(
                currentSize - maxSize,
                EstimateEntriesForMemoryTarget(currentMemory - maxMemory)
            );

            if (targetEvictions <= 0)
                return;

            // Get entries to evict based on policy
            var entriesToEvict = _evictionPolicy.SelectEntriesForEviction(_cache, targetEvictions).ToList();
            var actualEvictions = 0;

            foreach (var key in entriesToEvict)
            {
                if (_cache.TryRemove(key, out var removed))
                {
                    actualEvictions++;

                    // Determine eviction reason
                    var reason = currentMemory > maxMemory ? EvictionReason.MemoryLimit : EvictionReason.SizeLimit;

                    // Update statistics
                    lock (_statisticsLock)
                    {
                        _statistics.TotalEvictions++;
                        if (reason == EvictionReason.MemoryLimit)
                            _statistics.MemoryEvictions++;
                        else
                            _statistics.SizeEvictions++;
                    }

                    // Raise event if enabled
                    if (_config.ServiceCacheEventsEnabled)
                    {
                        var stats = removed.GetAccessStatistics();
                        EntryEvicted?.Invoke(this, new EntryEvictedEventArgs(
                            key,
                            reason,
                            stats.AccessCount,
                            removed.Age,
                            removed.EstimatedMemorySize));
                    }
                }
            }

            if (actualEvictions > 0)
            {
                UsdpLogger.Log("ServiceCache.CapacityEviction", new
                {
                    EvictionsRequested = targetEvictions,
                    ActualEvictions = actualEvictions,
                    Policy = _evictionPolicy.Name,
                    Reason = currentMemory > maxMemory ? "Memory" : "Size"
                });
            }
        }

        /// <summary>
        /// Calculates the current memory usage of the cache.
        /// </summary>
        /// <returns>The estimated memory usage in bytes.</returns>
        private long CalculateCurrentMemoryUsage()
        {
            return _cache.Values.Sum(entry => entry.EstimatedMemorySize);
        }

        /// <summary>
        /// Estimates the number of entries to evict to free the target amount of memory.
        /// </summary>
        /// <param name="targetMemoryToFree">The target amount of memory to free in bytes.</param>
        /// <returns>The estimated number of entries to evict.</returns>
        private int EstimateEntriesForMemoryTarget(long targetMemoryToFree)
        {
            if (targetMemoryToFree <= 0 || _cache.IsEmpty)
                return 0;

            var averageEntrySize = CalculateCurrentMemoryUsage() / _cache.Count;
            return averageEntrySize > 0 ? (int)Math.Ceiling((double)targetMemoryToFree / averageEntrySize) : 0;
        }

        /// <summary>
        /// Checks if memory usage exceeds the warning threshold and raises events if needed.
        /// </summary>
        private void CheckMemoryThreshold()
        {
            var currentMemory = CalculateCurrentMemoryUsage();
            var maxMemory = _config.ServiceCacheMaxMemoryBytes;
            var thresholdPercent = _config.ServiceCacheMemoryWarningThresholdPercent;
            var thresholdBytes = (long)(maxMemory * thresholdPercent / 100.0);

            if (currentMemory > thresholdBytes && _config.ServiceCacheEventsEnabled)
            {
                MemoryThresholdWarning?.Invoke(this, new MemoryThresholdWarningEventArgs(
                    currentMemory, maxMemory, thresholdPercent));
            }
        }

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Disposes the cache and releases all resources.
        /// </summary>
        public void Dispose()
        {
            if (_disposed)
                return;

            _disposed = true;

            try
            {
                _maintenanceTimer?.Dispose();

                var entriesCleared = _cache.Count;
                var memoryFreed = CalculateCurrentMemoryUsage();

                _cache.Clear();

                UsdpLogger.Log("ServiceCache.Disposed", new
                {
                    EntriesCleared = entriesCleared,
                    MemoryFreedMB = memoryFreed / (1024 * 1024)
                });
            }
            catch (Exception ex)
            {
                UsdpLogger.Log("ServiceCache.DisposeError", new { Error = ex.Message });
            }

            GC.SuppressFinalize(this);
        }

        #endregion
    }
}