using System;

namespace USDP2
{
    /// <summary>
    /// Factory interface for creating circuit breakers with proper dependency injection support.
    /// This interface enables testable and configurable circuit breaker creation.
    /// </summary>
    public interface ICircuitBreakerFactory
    {
        /// <summary>
        /// Creates a circuit breaker with the specified name and options.
        /// </summary>
        /// <param name="name">The unique name for the circuit breaker.</param>
        /// <param name="options">The circuit breaker configuration options.</param>
        /// <returns>A configured circuit breaker instance.</returns>
        /// <exception cref="ArgumentNullException">Thrown when name or options is null.</exception>
        /// <exception cref="ArgumentException">Thrown when name is empty or whitespace.</exception>
        ICircuitBreaker Create(string name, CircuitBreakerOptions options);

        /// <summary>
        /// Gets an existing circuit breaker by name, or creates a new one if it doesn't exist.
        /// </summary>
        /// <param name="name">The unique name for the circuit breaker.</param>
        /// <param name="options">The circuit breaker configuration options (used only if creating new).</param>
        /// <returns>A circuit breaker instance.</returns>
        /// <exception cref="ArgumentNullException">Thrown when name or options is null.</exception>
        /// <exception cref="ArgumentException">Thrown when name is empty or whitespace.</exception>
        ICircuitBreaker GetOrCreate(string name, CircuitBreakerOptions options);

        /// <summary>
        /// Checks if a circuit breaker with the specified name exists.
        /// </summary>
        /// <param name="name">The circuit breaker name to check.</param>
        /// <returns>True if the circuit breaker exists, false otherwise.</returns>
        bool Exists(string name);

        /// <summary>
        /// Removes a circuit breaker by name.
        /// </summary>
        /// <param name="name">The name of the circuit breaker to remove.</param>
        /// <returns>True if the circuit breaker was removed, false if it didn't exist.</returns>
        bool Remove(string name);
    }
}
