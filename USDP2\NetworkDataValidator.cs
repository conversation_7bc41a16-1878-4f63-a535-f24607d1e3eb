using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;

namespace USDP2
{
    /// <summary>
    /// Provides comprehensive validation and sanitization for network data to prevent security issues
    /// and ensure data integrity. This includes protection against DoS attacks, malformed data,
    /// and malicious payloads.
    /// </summary>
    public static class NetworkDataValidator
    {
        /// <summary>
        /// Gets or sets the maximum allowed size for incoming network data.
        /// This prevents DoS attacks through oversized payloads.
        ///
        /// For production use, this delegates to configuration.
        /// For testing, this can be overridden temporarily.
        /// </summary>
        public static int MaxDataSize
        {
            get => _maxDataSizeOverride ?? UsdpConfiguration.Instance.MaxNetworkDataSize;
            set => _maxDataSizeOverride = value;
        }
        private static int? _maxDataSizeOverride;

        /// <summary>
        /// Gets or sets the maximum allowed size for JSON strings.
        ///
        /// For production use, this delegates to configuration.
        /// For testing, this can be overridden temporarily.
        /// </summary>
        public static int MaxJsonSize
        {
            get => _maxJsonSizeOverride ?? UsdpConfiguration.Instance.MaxJsonDataSize;
            set => _maxJsonSizeOverride = value;
        }
        private static int? _maxJsonSizeOverride;

        /// <summary>
        /// Gets the maximum allowed size for CBOR data from configuration.
        /// </summary>
        public static int MaxCborSize => UsdpConfiguration.Instance.MaxCborDataSize;

        /// <summary>
        /// Gets the maximum allowed string length for individual string fields from configuration.
        /// </summary>
        public static int MaxStringLength => UsdpConfiguration.Instance.MaxStringFieldLength;

        /// <summary>
        /// Gets or sets the maximum allowed number of metadata entries.
        ///
        /// For production use, this delegates to configuration.
        /// For testing, this can be overridden temporarily.
        /// </summary>
        public static int MaxMetadataEntries
        {
            get => _maxMetadataEntriesOverride ?? UsdpConfiguration.Instance.MaxMetadataEntries;
            set => _maxMetadataEntriesOverride = value;
        }
        private static int? _maxMetadataEntriesOverride;

        /// <summary>
        /// Gets the maximum allowed depth for nested objects from configuration.
        /// </summary>
        public static int MaxNestingDepth => UsdpConfiguration.Instance.MaxNestingDepth;

        /// <summary>
        /// Regex pattern for detecting potentially malicious content.
        /// </summary>
        private static readonly Regex MaliciousContentPattern = new(
            @"(<script|javascript:|vbscript:|onload=|onerror=|eval\(|expression\(|url\(|@import|<iframe|<object|<embed)",
            RegexOptions.IgnoreCase | RegexOptions.Compiled);

        /// <summary>
        /// Validates incoming network data before deserialization.
        /// </summary>
        /// <param name="data">The raw network data to validate.</param>
        /// <param name="remoteAddress">The remote address for logging purposes.</param>
        /// <param name="remotePort">The remote port for logging purposes.</param>
        /// <returns>A validation result indicating success or failure with details.</returns>
        public static NetworkDataValidationResult ValidateIncomingData(
            byte[] data,
            string remoteAddress,
            int remotePort)
        {
            if (data == null)
            {
                return NetworkDataValidationResult.Failure(
                    NetworkDataValidationError.NullData,
                    "Incoming data is null",
                    remoteAddress,
                    remotePort);
            }

            // Check data size limits
            if (data.Length == 0)
            {
                return NetworkDataValidationResult.Failure(
                    NetworkDataValidationError.EmptyData,
                    "Incoming data is empty",
                    remoteAddress,
                    remotePort);
            }

            if (data.Length > MaxDataSize)
            {
                UsdpLogger.Log("NetworkDataValidation.OversizedPayload", new
                {
                    RemoteAddress = remoteAddress,
                    RemotePort = remotePort,
                    DataSize = data.Length,
                    MaxAllowed = MaxDataSize,
                    Severity = "Critical"
                });

                return NetworkDataValidationResult.Failure(
                    NetworkDataValidationError.OversizedPayload,
                    $"Data size {data.Length} exceeds maximum allowed size {MaxDataSize}",
                    remoteAddress,
                    remotePort);
            }

            // Check for binary data patterns that might indicate attacks
            if (ContainsSuspiciousBinaryPatterns(data))
            {
                UsdpLogger.Log("NetworkDataValidation.SuspiciousBinaryPattern", new
                {
                    RemoteAddress = remoteAddress,
                    RemotePort = remotePort,
                    DataSize = data.Length,
                    Severity = "Warning"
                });

                return NetworkDataValidationResult.Failure(
                    NetworkDataValidationError.SuspiciousContent,
                    "Data contains suspicious binary patterns",
                    remoteAddress,
                    remotePort);
            }

            // If data appears to be text, validate string content
            if (IsLikelyTextData(data))
            {
                try
                {
                    string textContent = Encoding.UTF8.GetString(data);
                    var textValidation = ValidateTextContent(textContent, remoteAddress, remotePort);
                    if (!textValidation.IsValid)
                    {
                        return textValidation;
                    }
                }
                catch (Exception ex)
                {
                    return NetworkDataValidationResult.Failure(
                        NetworkDataValidationError.InvalidEncoding,
                        $"Failed to decode text data: {ex.Message}",
                        remoteAddress,
                        remotePort);
                }
            }

            // Check for potential DoS patterns
            var dosResult = CheckForDosPatterns(data, remoteAddress, remotePort);
            if (!dosResult.IsValid)
            {
                return dosResult;
            }

            return NetworkDataValidationResult.Success(remoteAddress, remotePort);
        }

        /// <summary>
        /// Checks for patterns that might indicate DoS attacks or malicious behavior.
        /// </summary>
        /// <param name="data">The data to analyze.</param>
        /// <param name="remoteAddress">The remote address for logging.</param>
        /// <param name="remotePort">The remote port for logging.</param>
        /// <returns>A validation result indicating if DoS patterns were detected.</returns>
        private static NetworkDataValidationResult CheckForDosPatterns(byte[] data, string remoteAddress, int remotePort)
        {
            // Check for extremely small payloads that might indicate scanning
            if (data.Length < 10)
            {
                UsdpLogger.Log("NetworkDataValidation.SuspiciouslySmallPayload", new
                {
                    RemoteAddress = remoteAddress,
                    RemotePort = remotePort,
                    DataSize = data.Length,
                    Severity = "Info"
                });

                return NetworkDataValidationResult.Failure(
                    NetworkDataValidationError.SuspiciousContent,
                    "Payload too small - possible scanning activity",
                    remoteAddress,
                    remotePort);
            }

            // Check for payloads that are exactly at common buffer sizes (potential overflow attempts)
            int[] suspiciousSizes = { 256, 512, 1024, 2048, 4096, 8192, 16384, 32768, 65536 };
            if (suspiciousSizes.Contains(data.Length))
            {
                UsdpLogger.Log("NetworkDataValidation.SuspiciousBufferSize", new
                {
                    RemoteAddress = remoteAddress,
                    RemotePort = remotePort,
                    DataSize = data.Length,
                    Severity = "Warning"
                });

                // Don't reject, but log for monitoring
            }

            // Check for highly compressible data (might indicate compression bombs)
            if (data.Length > 1000)
            {
                var compressionRatio = CalculateCompressionRatio(data);
                if (compressionRatio > 10.0) // Data compresses to less than 10% of original size
                {
                    UsdpLogger.Log("NetworkDataValidation.HighlyCompressibleData", new
                    {
                        RemoteAddress = remoteAddress,
                        RemotePort = remotePort,
                        DataSize = data.Length,
                        CompressionRatio = compressionRatio,
                        Severity = "Warning"
                    });

                    return NetworkDataValidationResult.Failure(
                        NetworkDataValidationError.SuspiciousContent,
                        "Data appears to be highly compressible - potential compression bomb",
                        remoteAddress,
                        remotePort);
                }
            }

            return NetworkDataValidationResult.Success(remoteAddress, remotePort);
        }

        /// <summary>
        /// Calculates a rough compression ratio for the given data.
        /// </summary>
        /// <param name="data">The data to analyze.</param>
        /// <returns>The compression ratio (original size / compressed size).</returns>
        private static double CalculateCompressionRatio(byte[] data)
        {
            try
            {
                using var memoryStream = new MemoryStream();
                using (var gzipStream = new System.IO.Compression.GZipStream(memoryStream, System.IO.Compression.CompressionMode.Compress))
                {
                    gzipStream.Write(data, 0, data.Length);
                }

                var compressedSize = memoryStream.Length;
                return compressedSize > 0 ? (double)data.Length / compressedSize : 1.0;
            }
            catch
            {
                // If compression fails, assume normal ratio
                return 1.0;
            }
        }

        /// <summary>
        /// Validates and sanitizes a ServiceAdvertisement before processing.
        /// </summary>
        /// <param name="advertisement">The service advertisement to validate.</param>
        /// <param name="remoteAddress">The remote address for logging purposes.</param>
        /// <param name="remotePort">The remote port for logging purposes.</param>
        /// <returns>A validation result with sanitized data if successful.</returns>
        public static ServiceAdvertisementValidationResult ValidateServiceAdvertisement(
            ServiceAdvertisement advertisement,
            string remoteAddress,
            int remotePort)
        {
            if (advertisement == null)
            {
                return ServiceAdvertisementValidationResult.Failure(
                    "Service advertisement is null",
                    remoteAddress,
                    remotePort);
            }

            try
            {
                // Validate and sanitize service identifier
                var sanitizedServiceId = SanitizeServiceIdentifier(advertisement.ServiceId);

                // Validate and sanitize endpoint
                var sanitizedEndpoint = SanitizeTransportEndpoint(advertisement.Endpoint);

                // Validate and sanitize metadata
                var sanitizedMetadata = SanitizeMetadata(advertisement.Metadata);

                // Validate timestamp is reasonable
                if (!IsValidTimestamp(advertisement.Timestamp))
                {
                    return ServiceAdvertisementValidationResult.Failure(
                        "Invalid timestamp - too far in past or future",
                        remoteAddress,
                        remotePort);
                }

                // Validate TTL is reasonable
                if (advertisement.Ttl.HasValue && !IsValidTtl(advertisement.Ttl.Value))
                {
                    return ServiceAdvertisementValidationResult.Failure(
                        "Invalid TTL value",
                        remoteAddress,
                        remotePort);
                }

                // Create sanitized advertisement
                var sanitizedAdvertisement = new ServiceAdvertisement(sanitizedServiceId, sanitizedEndpoint)
                {
                    Metadata = sanitizedMetadata,
                    Timestamp = advertisement.Timestamp,
                    Ttl = advertisement.Ttl,
                    Signature = advertisement.Signature // Keep signature for verification
                };

                return ServiceAdvertisementValidationResult.Success(
                    sanitizedAdvertisement,
                    remoteAddress,
                    remotePort);
            }
            catch (Exception ex)
            {
                UsdpLogger.Log("NetworkDataValidation.ServiceAdvertisementValidationError", new
                {
                    RemoteAddress = remoteAddress,
                    RemotePort = remotePort,
                    Error = ex.Message,
                    Severity = "Error"
                });

                return ServiceAdvertisementValidationResult.Failure(
                    $"Service advertisement validation failed: {ex.Message}",
                    remoteAddress,
                    remotePort);
            }
        }

        /// <summary>
        /// Validates text content for malicious patterns and size limits.
        /// </summary>
        private static NetworkDataValidationResult ValidateTextContent(
            string content,
            string remoteAddress,
            int remotePort)
        {
            if (content.Length > MaxJsonSize)
            {
                return NetworkDataValidationResult.Failure(
                    NetworkDataValidationError.OversizedPayload,
                    $"Text content size {content.Length} exceeds maximum allowed size {MaxJsonSize}",
                    remoteAddress,
                    remotePort);
            }

            // Check for malicious content patterns
            if (MaliciousContentPattern.IsMatch(content))
            {
                UsdpLogger.Log("NetworkDataValidation.MaliciousContentDetected", new
                {
                    RemoteAddress = remoteAddress,
                    RemotePort = remotePort,
                    ContentLength = content.Length,
                    Severity = "Critical"
                });

                return NetworkDataValidationResult.Failure(
                    NetworkDataValidationError.MaliciousContent,
                    "Text content contains potentially malicious patterns",
                    remoteAddress,
                    remotePort);
            }

            // Check for excessive control characters
            int controlCharCount = content.Count(c => char.IsControl(c) && c != '\n' && c != '\r' && c != '\t');
            if (controlCharCount > content.Length * 0.1) // More than 10% control characters
            {
                return NetworkDataValidationResult.Failure(
                    NetworkDataValidationError.SuspiciousContent,
                    "Text content contains excessive control characters",
                    remoteAddress,
                    remotePort);
            }

            return NetworkDataValidationResult.Success(remoteAddress, remotePort);
        }

        /// <summary>
        /// Checks if data contains suspicious binary patterns.
        /// </summary>
        private static bool ContainsSuspiciousBinaryPatterns(byte[] data)
        {
            // Check for excessive null bytes (potential buffer overflow attempt)
            int nullByteCount = data.Count(b => b == 0);
            if (nullByteCount > data.Length * 0.5) // More than 50% null bytes
            {
                return true;
            }

            // Check for repeating patterns that might indicate padding attacks
            if (data.Length > 100)
            {
                byte firstByte = data[0];
                int consecutiveCount = data.TakeWhile(b => b == firstByte).Count();
                if (consecutiveCount > data.Length * 0.8) // More than 80% same byte
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// Determines if data is likely text-based (JSON, XML, etc.).
        /// </summary>
        private static bool IsLikelyTextData(byte[] data)
        {
            if (data.Length == 0) return false;

            // Check first few bytes for common text indicators
            byte firstByte = data[0];

            // JSON typically starts with { or [
            if (firstByte == '{' || firstByte == '[') return true;

            // XML typically starts with <
            if (firstByte == '<') return true;

            // Check if most bytes are printable ASCII
            int printableCount = data.Take(Math.Min(100, data.Length))
                .Count(b => (b >= 32 && b <= 126) || b == '\n' || b == '\r' || b == '\t');

            return printableCount > data.Length * 0.8;
        }

        /// <summary>
        /// Sanitizes a service identifier by removing potentially harmful content.
        /// </summary>
        private static ServiceIdentifier SanitizeServiceIdentifier(ServiceIdentifier serviceId)
        {
            if (serviceId == null)
                throw new ArgumentException("Service identifier cannot be null");

            string sanitizedNamespace = SanitizeString(serviceId.Namespace, "namespace");
            string sanitizedName = SanitizeString(serviceId.Name, "name");

            return new ServiceIdentifier(sanitizedNamespace, sanitizedName);
        }

        /// <summary>
        /// Sanitizes a transport endpoint by validating and cleaning its properties.
        /// </summary>
        private static TransportEndpoint SanitizeTransportEndpoint(TransportEndpoint endpoint)
        {
            if (endpoint == null)
                throw new ArgumentException("Transport endpoint cannot be null");

            return new TransportEndpoint
            {
                Protocol = SanitizeString(endpoint.Protocol, "protocol"),
                Address = SanitizeString(endpoint.Address, "address"),
                Port = endpoint.Port, // Already validated by property setter
                Security = SanitizeString(endpoint.Security, "security")
            };
        }

        /// <summary>
        /// Sanitizes metadata by validating keys and values.
        /// </summary>
        private static Dictionary<string, object> SanitizeMetadata(Dictionary<string, object> metadata)
        {
            if (metadata == null)
                return new Dictionary<string, object>();

            if (metadata.Count > MaxMetadataEntries)
            {
                throw new ArgumentException($"Metadata cannot contain more than {MaxMetadataEntries} entries");
            }

            var sanitized = new Dictionary<string, object>();

            foreach (var kvp in metadata)
            {
                string sanitizedKey = SanitizeString(kvp.Key, "metadata key");
                object sanitizedValue = SanitizeMetadataValue(kvp.Value);
                sanitized[sanitizedKey] = sanitizedValue;
            }

            return sanitized;
        }

        /// <summary>
        /// Sanitizes a metadata value.
        /// </summary>
        private static object SanitizeMetadataValue(object value)
        {
            if (value == null)
                throw new ArgumentException("Metadata values cannot be null");

            if (value is string stringValue)
            {
                return SanitizeString(stringValue, "metadata value");
            }

            // For non-string values, ensure they're safe types
            if (value is int || value is long || value is double || value is bool || value is DateTime)
            {
                return value;
            }

            // Convert other types to string and sanitize
            return SanitizeString(value.ToString() ?? "", "metadata value");
        }

        /// <summary>
        /// Sanitizes a string by removing potentially harmful content and enforcing length limits.
        /// </summary>
        private static string SanitizeString(string input, string fieldName)
        {
            if (string.IsNullOrEmpty(input))
                throw new ArgumentException($"{fieldName} cannot be null or empty");

            if (input.Length > MaxStringLength)
            {
                throw new ArgumentException($"{fieldName} exceeds maximum length of {MaxStringLength} characters");
            }

            // Remove control characters except common whitespace
            string sanitized = new string(input.Where(c =>
                !char.IsControl(c) || c == '\n' || c == '\r' || c == '\t').ToArray());

            // Remove potentially malicious patterns
            sanitized = MaliciousContentPattern.Replace(sanitized, "");

            // Trim whitespace
            sanitized = sanitized.Trim();

            if (string.IsNullOrEmpty(sanitized))
                throw new ArgumentException($"{fieldName} contains no valid content after sanitization");

            return sanitized;
        }

        /// <summary>
        /// Validates that a timestamp is within reasonable bounds.
        /// </summary>
        private static bool IsValidTimestamp(DateTimeOffset timestamp)
        {
            var now = DateTimeOffset.UtcNow;
            var minTime = now.AddDays(-30); // Not older than 30 days
            var maxTime = now.AddHours(1);  // Not more than 1 hour in future

            return timestamp >= minTime && timestamp <= maxTime;
        }

        /// <summary>
        /// Validates that a TTL value is reasonable.
        /// </summary>
        private static bool IsValidTtl(TimeSpan ttl)
        {
            return ttl >= TimeSpan.Zero && ttl <= TimeSpan.FromDays(365);
        }
    }
}
