using System;
using System.Collections.Generic;

namespace USDP2.Metrics
{
    /// <summary>
    /// Modular diagnostics monitor that replaces the static metrics functionality in the Diagnostics class.
    /// Provides a cleaner, more testable approach to diagnostics and performance monitoring.
    /// </summary>
    public class ModularDiagnosticsMonitor
    {
        private readonly PerformanceMetricsCollector _performanceCollector;
        private readonly bool _isEnabled;

        /// <summary>
        /// Initializes a new instance of the <see cref="ModularDiagnosticsMonitor"/> class.
        /// </summary>
        /// <param name="performanceCollector">The performance metrics collector to use.</param>
        /// <param name="isEnabled">Whether diagnostics monitoring is enabled.</param>
        public ModularDiagnosticsMonitor(
            PerformanceMetricsCollector? performanceCollector = null,
            bool isEnabled = true)
        {
            _performanceCollector = performanceCollector ?? 
                MetricsManager.Instance.GetCollector<PerformanceMetricsCollector>("Performance") ??
                new PerformanceMetricsCollector();
            _isEnabled = isEnabled;
        }

        /// <summary>
        /// Gets whether diagnostics monitoring is enabled.
        /// </summary>
        public bool IsEnabled => _isEnabled;

        /// <summary>
        /// Records query latency.
        /// </summary>
        /// <param name="queryType">The type of query performed.</param>
        /// <param name="latencyMs">The query latency in milliseconds.</param>
        /// <param name="resultCount">The number of results returned.</param>
        /// <param name="isSuccess">Whether the query was successful.</param>
        public void RecordQueryLatency(string queryType, long latencyMs, int resultCount = 0, bool isSuccess = true)
        {
            if (!_isEnabled) return;

            var latency = TimeSpan.FromMilliseconds(latencyMs);
            _performanceCollector.RecordQuery(queryType, latency, resultCount, isSuccess);
        }

        /// <summary>
        /// Records an operation with timing and success tracking.
        /// </summary>
        /// <param name="operationName">The name of the operation.</param>
        /// <param name="component">The component performing the operation.</param>
        /// <param name="duration">The operation duration.</param>
        /// <param name="isSuccess">Whether the operation was successful.</param>
        /// <param name="errorType">The type of error if operation failed.</param>
        public void RecordOperation(string operationName, string component, TimeSpan duration, 
            bool isSuccess = true, string? errorType = null)
        {
            if (!_isEnabled) return;

            _performanceCollector.RecordOperation(operationName, component, duration, isSuccess, errorType);
        }

        /// <summary>
        /// Records throughput metrics for data processing operations.
        /// </summary>
        /// <param name="operationType">The type of operation.</param>
        /// <param name="itemsProcessed">The number of items processed.</param>
        /// <param name="duration">The time taken to process the items.</param>
        /// <param name="bytesProcessed">The number of bytes processed (optional).</param>
        public void RecordThroughput(string operationType, long itemsProcessed, TimeSpan duration, 
            long bytesProcessed = 0)
        {
            if (!_isEnabled) return;

            _performanceCollector.RecordThroughput(operationType, itemsProcessed, duration, bytesProcessed);
        }

        /// <summary>
        /// Records system resource usage metrics.
        /// </summary>
        /// <param name="component">The component being monitored.</param>
        public void RecordSystemMetrics(string component)
        {
            if (!_isEnabled) return;

            _performanceCollector.RecordSystemMetrics(component);
        }

        /// <summary>
        /// Gets the average query latency for a specific query type.
        /// </summary>
        /// <param name="queryType">The query type to get latency for. If null, returns overall average.</param>
        /// <returns>The average query latency.</returns>
        public TimeSpan GetAverageQueryLatency(string? queryType = null)
        {
            var snapshot = _performanceCollector.GetSnapshot();
            
            foreach (var timing in snapshot.Timings.Values)
            {
                if (timing.Name == "query_latency")
                {
                    if (queryType == null || 
                        (timing.Tags.TryGetValue("query_type", out var type) && type == queryType))
                    {
                        return timing.AverageDuration;
                    }
                }
            }

            return TimeSpan.Zero;
        }

        /// <summary>
        /// Gets query statistics for a specific query type.
        /// </summary>
        /// <param name="queryType">The query type to get statistics for.</param>
        /// <returns>Query statistics including count, latency, and success rate.</returns>
        public QueryStatistics GetQueryStatistics(string queryType)
        {
            var snapshot = _performanceCollector.GetSnapshot();
            var stats = new QueryStatistics { QueryType = queryType };

            foreach (var counter in snapshot.Counters.Values)
            {
                if (counter.Name == "queries_total" && 
                    counter.Tags.TryGetValue("query_type", out var type) && type == queryType)
                {
                    if (counter.Tags.TryGetValue("result", out var result))
                    {
                        if (result == "success")
                            stats.SuccessfulQueries = counter.Value;
                        else if (result == "error")
                            stats.FailedQueries = counter.Value;
                    }
                }
            }

            foreach (var timing in snapshot.Timings.Values)
            {
                if (timing.Name == "query_latency" && 
                    timing.Tags.TryGetValue("query_type", out var type) && type == queryType)
                {
                    stats.AverageLatency = timing.AverageDuration;
                    stats.MinLatency = timing.MinDuration;
                    stats.MaxLatency = timing.MaxDuration;
                    stats.P95Latency = timing.GetPercentileDuration(95);
                    stats.P99Latency = timing.GetPercentileDuration(99);
                }
            }

            foreach (var histogram in snapshot.Histograms.Values)
            {
                if (histogram.Name == "query_result_count" && 
                    histogram.Tags.TryGetValue("query_type", out var type) && type == queryType)
                {
                    stats.AverageResultCount = histogram.Average;
                    stats.TotalResults = (long)histogram.Sum;
                }
            }

            return stats;
        }

        /// <summary>
        /// Gets a comprehensive performance summary.
        /// </summary>
        /// <returns>A performance summary with key metrics.</returns>
        public PerformanceSummary GetPerformanceSummary()
        {
            return _performanceCollector.GetPerformanceSummary();
        }

        /// <summary>
        /// Gets all query types that have been recorded.
        /// </summary>
        /// <returns>A list of query types.</returns>
        public List<string> GetQueryTypes()
        {
            var snapshot = _performanceCollector.GetSnapshot();
            var queryTypes = new HashSet<string>();

            foreach (var counter in snapshot.Counters.Values)
            {
                if (counter.Name == "queries_total" && 
                    counter.Tags.TryGetValue("query_type", out var queryType))
                {
                    queryTypes.Add(queryType);
                }
            }

            return new List<string>(queryTypes);
        }

        /// <summary>
        /// Clears all diagnostics metrics.
        /// </summary>
        public void ClearMetrics()
        {
            _performanceCollector.Reset();
        }

        /// <summary>
        /// Gets the status of the diagnostics monitor.
        /// </summary>
        /// <returns>Status information about the monitor.</returns>
        public DiagnosticsMonitorStatus GetStatus()
        {
            var collectorStatus = _performanceCollector.GetStatus();
            var summary = GetPerformanceSummary();

            return new DiagnosticsMonitorStatus
            {
                IsEnabled = _isEnabled,
                MetricsCount = collectorStatus.MetricsCount,
                LastCollectionTime = collectorStatus.LastCollectionTime,
                ErrorMessage = collectorStatus.ErrorMessage,
                TotalQueries = summary.TotalQueries,
                TotalOperations = summary.TotalOperations,
                AverageQueryLatency = summary.AverageQueryLatency,
                MemoryUsage = summary.CurrentMemoryUsage
            };
        }
    }

    /// <summary>
    /// Statistics for a specific query type.
    /// </summary>
    public class QueryStatistics
    {
        /// <summary>
        /// Gets or sets the query type.
        /// </summary>
        public string QueryType { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the number of successful queries.
        /// </summary>
        public long SuccessfulQueries { get; set; }

        /// <summary>
        /// Gets or sets the number of failed queries.
        /// </summary>
        public long FailedQueries { get; set; }

        /// <summary>
        /// Gets the total number of queries.
        /// </summary>
        public long TotalQueries => SuccessfulQueries + FailedQueries;

        /// <summary>
        /// Gets the success rate as a percentage.
        /// </summary>
        public double SuccessRate => TotalQueries > 0 ? (double)SuccessfulQueries / TotalQueries * 100 : 0;

        /// <summary>
        /// Gets or sets the average query latency.
        /// </summary>
        public TimeSpan AverageLatency { get; set; }

        /// <summary>
        /// Gets or sets the minimum query latency.
        /// </summary>
        public TimeSpan MinLatency { get; set; }

        /// <summary>
        /// Gets or sets the maximum query latency.
        /// </summary>
        public TimeSpan MaxLatency { get; set; }

        /// <summary>
        /// Gets or sets the 95th percentile latency.
        /// </summary>
        public TimeSpan P95Latency { get; set; }

        /// <summary>
        /// Gets or sets the 99th percentile latency.
        /// </summary>
        public TimeSpan P99Latency { get; set; }

        /// <summary>
        /// Gets or sets the average number of results per query.
        /// </summary>
        public double AverageResultCount { get; set; }

        /// <summary>
        /// Gets or sets the total number of results across all queries.
        /// </summary>
        public long TotalResults { get; set; }
    }

    /// <summary>
    /// Status information about the diagnostics monitor.
    /// </summary>
    public class DiagnosticsMonitorStatus
    {
        /// <summary>
        /// Gets or sets whether the monitor is enabled.
        /// </summary>
        public bool IsEnabled { get; set; }

        /// <summary>
        /// Gets or sets the number of metrics being tracked.
        /// </summary>
        public int MetricsCount { get; set; }

        /// <summary>
        /// Gets or sets the last time metrics were collected.
        /// </summary>
        public DateTimeOffset? LastCollectionTime { get; set; }

        /// <summary>
        /// Gets or sets any error message from the monitor.
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Gets or sets the total number of queries recorded.
        /// </summary>
        public long TotalQueries { get; set; }

        /// <summary>
        /// Gets or sets the total number of operations recorded.
        /// </summary>
        public long TotalOperations { get; set; }

        /// <summary>
        /// Gets or sets the average query latency.
        /// </summary>
        public TimeSpan AverageQueryLatency { get; set; }

        /// <summary>
        /// Gets or sets the current memory usage.
        /// </summary>
        public long MemoryUsage { get; set; }
    }

    /// <summary>
    /// Static facade for backward compatibility with existing Diagnostics usage.
    /// This provides a migration path while encouraging use of the modular approach.
    /// </summary>
    public static class DiagnosticsMonitorFacade
    {
        private static readonly Lazy<ModularDiagnosticsMonitor> _instance = 
            new(() => new ModularDiagnosticsMonitor());

        /// <summary>
        /// Records query latency.
        /// </summary>
        /// <param name="ms">The query latency in milliseconds.</param>
        public static void RecordQueryLatency(long ms)
        {
            _instance.Value.RecordQueryLatency("General", ms);
        }

        /// <summary>
        /// Gets the average query latency.
        /// </summary>
        /// <returns>The average query latency in milliseconds.</returns>
        public static double GetAverageQueryLatency()
        {
            return _instance.Value.GetAverageQueryLatency().TotalMilliseconds;
        }

        /// <summary>
        /// Clears all metrics.
        /// </summary>
        public static void ClearMetrics()
        {
            _instance.Value.ClearMetrics();
        }
    }
}
