using System;
using System.Collections.Generic;

namespace USDP2.Metrics
{
    /// <summary>
    /// Specialized metrics collector for serialization operations.
    /// Tracks serialization/deserialization performance, cache effectiveness, and error rates.
    /// </summary>
    public class SerializationMetricsCollector : BaseMetricsCollector
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="SerializationMetricsCollector"/> class.
        /// </summary>
        public SerializationMetricsCollector() : base("Serialization", MetricsCategory.Serialization)
        {
        }

        /// <summary>
        /// Records a serialization operation.
        /// </summary>
        /// <param name="typeName">The name of the type being serialized.</param>
        /// <param name="format">The serialization format (JSON, CBOR, etc.).</param>
        /// <param name="duration">The time taken for the operation.</param>
        /// <param name="sizeInBytes">The size of the serialized data in bytes.</param>
        /// <param name="isError">Whether the operation resulted in an error.</param>
        /// <param name="isCacheHit">Whether this was served from cache.</param>
        public void RecordSerialization(string typeName, string format, TimeSpan duration, 
            long sizeInBytes, bool isError = false, bool isCacheHit = false)
        {
            var tags = new Dictionary<string, string>
            {
                ["type"] = typeName,
                ["format"] = format,
                ["operation"] = "serialize"
            };

            // Record basic metrics
            RecordCounter("operations_total", 1, tags);
            RecordTiming("operation_duration", duration, tags);
            RecordHistogram("data_size_bytes", sizeInBytes, tags);

            // Record error metrics
            if (isError)
            {
                var errorTags = new Dictionary<string, string>(tags) { ["result"] = "error" };
                RecordCounter("errors_total", 1, errorTags);
            }
            else
            {
                var successTags = new Dictionary<string, string>(tags) { ["result"] = "success" };
                RecordCounter("success_total", 1, successTags);
            }

            // Record cache metrics
            if (isCacheHit)
            {
                var cacheTags = new Dictionary<string, string>(tags) { ["cache"] = "hit" };
                RecordCounter("cache_hits_total", 1, cacheTags);
            }
            else
            {
                var cacheTags = new Dictionary<string, string>(tags) { ["cache"] = "miss" };
                RecordCounter("cache_misses_total", 1, cacheTags);
            }

            // Record throughput metrics
            var throughputMbps = sizeInBytes / (1024.0 * 1024.0) / duration.TotalSeconds;
            RecordHistogram("throughput_mbps", throughputMbps, tags);
        }

        /// <summary>
        /// Records a deserialization operation.
        /// </summary>
        /// <param name="typeName">The name of the type being deserialized.</param>
        /// <param name="format">The serialization format (JSON, CBOR, etc.).</param>
        /// <param name="duration">The time taken for the operation.</param>
        /// <param name="sizeInBytes">The size of the serialized data in bytes.</param>
        /// <param name="isError">Whether the operation resulted in an error.</param>
        /// <param name="isCacheHit">Whether this was served from cache.</param>
        public void RecordDeserialization(string typeName, string format, TimeSpan duration, 
            long sizeInBytes, bool isError = false, bool isCacheHit = false)
        {
            var tags = new Dictionary<string, string>
            {
                ["type"] = typeName,
                ["format"] = format,
                ["operation"] = "deserialize"
            };

            // Record basic metrics
            RecordCounter("operations_total", 1, tags);
            RecordTiming("operation_duration", duration, tags);
            RecordHistogram("data_size_bytes", sizeInBytes, tags);

            // Record error metrics
            if (isError)
            {
                var errorTags = new Dictionary<string, string>(tags) { ["result"] = "error" };
                RecordCounter("errors_total", 1, errorTags);
            }
            else
            {
                var successTags = new Dictionary<string, string>(tags) { ["result"] = "success" };
                RecordCounter("success_total", 1, successTags);
            }

            // Record cache metrics
            if (isCacheHit)
            {
                var cacheTags = new Dictionary<string, string>(tags) { ["cache"] = "hit" };
                RecordCounter("cache_hits_total", 1, cacheTags);
            }
            else
            {
                var cacheTags = new Dictionary<string, string>(tags) { ["cache"] = "miss" };
                RecordCounter("cache_misses_total", 1, cacheTags);
            }

            // Record throughput metrics
            var throughputMbps = sizeInBytes / (1024.0 * 1024.0) / duration.TotalSeconds;
            RecordHistogram("throughput_mbps", throughputMbps, tags);
        }

        /// <summary>
        /// Records cache statistics.
        /// </summary>
        /// <param name="format">The cache format (JSON, CBOR, etc.).</param>
        /// <param name="cacheSize">Current cache size.</param>
        /// <param name="maxCacheSize">Maximum cache size.</param>
        /// <param name="hitRate">Cache hit rate as a percentage.</param>
        /// <param name="evictions">Number of cache evictions.</param>
        public void RecordCacheStatistics(string format, int cacheSize, int maxCacheSize, 
            double hitRate, long evictions)
        {
            var tags = new Dictionary<string, string> { ["format"] = format };

            RecordGauge("cache_size", cacheSize, tags);
            RecordGauge("cache_max_size", maxCacheSize, tags);
            RecordGauge("cache_hit_rate_percent", hitRate, tags);
            RecordGauge("cache_utilization_percent", (double)cacheSize / maxCacheSize * 100, tags);
            RecordCounter("cache_evictions_total", evictions, tags);
        }

        /// <summary>
        /// Records pool statistics for object pooling.
        /// </summary>
        /// <param name="poolName">The name of the pool.</param>
        /// <param name="poolSize">Current pool size.</param>
        /// <param name="maxPoolSize">Maximum pool size.</param>
        /// <param name="borrowCount">Number of objects borrowed from pool.</param>
        /// <param name="returnCount">Number of objects returned to pool.</param>
        public void RecordPoolStatistics(string poolName, int poolSize, int maxPoolSize, 
            long borrowCount, long returnCount)
        {
            var tags = new Dictionary<string, string> { ["pool"] = poolName };

            RecordGauge("pool_size", poolSize, tags);
            RecordGauge("pool_max_size", maxPoolSize, tags);
            RecordGauge("pool_utilization_percent", (double)poolSize / maxPoolSize * 100, tags);
            RecordCounter("pool_borrows_total", borrowCount, tags);
            RecordCounter("pool_returns_total", returnCount, tags);
        }

        /// <summary>
        /// Gets a performance summary for serialization operations.
        /// </summary>
        /// <returns>A summary of serialization performance metrics.</returns>
        public SerializationPerformanceSummary GetPerformanceSummary()
        {
            var snapshot = GetSnapshot();
            var summary = new SerializationPerformanceSummary();

            // Calculate totals
            foreach (var counter in snapshot.Counters.Values)
            {
                if (counter.Name == "operations_total")
                {
                    if (counter.Tags.TryGetValue("operation", out var operation))
                    {
                        if (operation == "serialize")
                            summary.TotalSerializations += counter.Value;
                        else if (operation == "deserialize")
                            summary.TotalDeserializations += counter.Value;
                    }
                }
                else if (counter.Name == "errors_total")
                {
                    summary.TotalErrors += counter.Value;
                }
            }

            // Calculate averages from timing metrics
            foreach (var timing in snapshot.Timings.Values)
            {
                if (timing.Name == "operation_duration" && timing.Count > 0)
                {
                    if (timing.Tags.TryGetValue("operation", out var operation))
                    {
                        if (operation == "serialize")
                            summary.AverageSerializationTime = timing.AverageDuration;
                        else if (operation == "deserialize")
                            summary.AverageDeserializationTime = timing.AverageDuration;
                    }
                }
            }

            // Calculate cache hit rates
            long totalCacheHits = 0, totalCacheMisses = 0;
            foreach (var counter in snapshot.Counters.Values)
            {
                if (counter.Name == "cache_hits_total")
                    totalCacheHits += counter.Value;
                else if (counter.Name == "cache_misses_total")
                    totalCacheMisses += counter.Value;
            }

            var totalCacheOperations = totalCacheHits + totalCacheMisses;
            summary.CacheHitRate = totalCacheOperations > 0 
                ? (double)totalCacheHits / totalCacheOperations * 100 
                : 0;

            return summary;
        }
    }

    /// <summary>
    /// Summary of serialization performance metrics.
    /// </summary>
    public class SerializationPerformanceSummary
    {
        /// <summary>
        /// Gets or sets the total number of serialization operations.
        /// </summary>
        public long TotalSerializations { get; set; }

        /// <summary>
        /// Gets or sets the total number of deserialization operations.
        /// </summary>
        public long TotalDeserializations { get; set; }

        /// <summary>
        /// Gets or sets the total number of errors.
        /// </summary>
        public long TotalErrors { get; set; }

        /// <summary>
        /// Gets or sets the average serialization time.
        /// </summary>
        public TimeSpan AverageSerializationTime { get; set; }

        /// <summary>
        /// Gets or sets the average deserialization time.
        /// </summary>
        public TimeSpan AverageDeserializationTime { get; set; }

        /// <summary>
        /// Gets or sets the cache hit rate as a percentage.
        /// </summary>
        public double CacheHitRate { get; set; }

        /// <summary>
        /// Gets the error rate as a percentage.
        /// </summary>
        public double ErrorRate => (TotalSerializations + TotalDeserializations) > 0 
            ? (double)TotalErrors / (TotalSerializations + TotalDeserializations) * 100 
            : 0;
    }
}
