using System;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;

namespace USDP2.Tests
{
    /// <summary>
    /// Tests for network parameter validation in network operations.
    /// Validates that network components properly validate input parameters
    /// before attempting network operations.
    /// </summary>
    [TestClass]
    public class NetworkValidationTests
    {
        private const int ValidPort = 8080;
        private const string ValidIpAddress = "127.0.0.1";
        private const string ValidHostname = "example.com";
        private const string ValidMulticastAddress = "***************";

        #region ExampleIntegration Validation Tests

        [TestMethod]
        public async Task ExampleIntegration_RunAsync_InvalidPort_ThrowsArgumentOutOfRangeException()
        {
            // Act & Assert - Test invalid port values
            await Assert.ThrowsExceptionAsync<ArgumentOutOfRangeException>(
                () => ExampleIntegration.RunAsync(NetworkScope.Local, 0));

            await Assert.ThrowsExceptionAsync<ArgumentOutOfRangeException>(
                () => ExampleIntegration.RunAsync(NetworkScope.Local, -1));

            await Assert.ThrowsExceptionAsync<ArgumentOutOfRangeException>(
                () => ExampleIntegration.RunAsync(NetworkScope.Local, 65536));
        }

        [TestMethod]
        public async Task ExampleIntegration_RunAsync_InvalidMulticastAddress_ThrowsArgumentException()
        {
            // Act & Assert - Test invalid multicast address
            await Assert.ThrowsExceptionAsync<ArgumentException>(
                () => ExampleIntegration.RunAsync(NetworkScope.Local, ValidPort, "invalid-address"));

            await Assert.ThrowsExceptionAsync<ArgumentException>(
                () => ExampleIntegration.RunAsync(NetworkScope.Local, ValidPort, "300.300.300.300"));
        }

        [TestMethod]
        public async Task ExampleIntegration_RunAsync_NonMulticastAddress_ThrowsArgumentException()
        {
            // Act & Assert - Test non-multicast IP address
            await Assert.ThrowsExceptionAsync<ArgumentException>(
                () => ExampleIntegration.RunAsync(NetworkScope.Local, ValidPort, "***********"));

            await Assert.ThrowsExceptionAsync<ArgumentException>(
                () => ExampleIntegration.RunAsync(NetworkScope.Local, ValidPort, "********"));
        }

        [TestMethod]
        public async Task ExampleIntegration_RunAsync_ValidParameters_DoesNotThrow()
        {
            // Arrange
            using var cts = new System.Threading.CancellationTokenSource(TimeSpan.FromMilliseconds(100));

            // Act & Assert - Valid parameters should not throw during validation
            try
            {
                await ExampleIntegration.RunAsync(
                    NetworkScope.Local, 
                    ValidPort, 
                    ValidMulticastAddress, 
                    ReceiverStartMode.FireAndForget,
                    cts.Token);
            }
            catch (OperationCanceledException)
            {
                // Expected due to cancellation token
            }
            catch (Exception ex) when (ex.Message.Contains("validation") || ex is ArgumentException)
            {
                Assert.Fail($"Validation should not fail for valid parameters: {ex.Message}");
            }
        }

        #endregion

        #region NetworkReceiverFactory Validation Tests

        [TestMethod]
        public void NetworkReceiverFactory_CreateReceiver_InvalidPort_ThrowsArgumentOutOfRangeException()
        {
            // Act & Assert
            Assert.ThrowsException<ArgumentOutOfRangeException>(
                () => NetworkReceiverFactory.CreateReceiver(NetworkScope.Local, 0));

            Assert.ThrowsException<ArgumentOutOfRangeException>(
                () => NetworkReceiverFactory.CreateReceiver(NetworkScope.Local, -1));

            Assert.ThrowsException<ArgumentOutOfRangeException>(
                () => NetworkReceiverFactory.CreateReceiver(NetworkScope.Local, 65536));
        }

        [TestMethod]
        public void NetworkReceiverFactory_CreateReceiver_InvalidMulticastAddress_ThrowsArgumentException()
        {
            // Act & Assert
            Assert.ThrowsException<ArgumentException>(
                () => NetworkReceiverFactory.CreateReceiver(NetworkScope.Local, ValidPort, "invalid-address"));

            Assert.ThrowsException<ArgumentException>(
                () => NetworkReceiverFactory.CreateReceiver(NetworkScope.Local, ValidPort, "300.300.300.300"));
        }

        [TestMethod]
        public void NetworkReceiverFactory_CreateReceiver_ValidParameters_CreatesReceiver()
        {
            // Act
            var receiver1 = NetworkReceiverFactory.CreateReceiver(NetworkScope.Local, ValidPort);
            var receiver2 = NetworkReceiverFactory.CreateReceiver(NetworkScope.Local, ValidPort, ValidMulticastAddress);
            var receiver3 = NetworkReceiverFactory.CreateReceiver(NetworkScope.Global, ValidPort);

            // Assert
            Assert.IsNotNull(receiver1);
            Assert.IsNotNull(receiver2);
            Assert.IsNotNull(receiver3);
            Assert.IsInstanceOfType(receiver1, typeof(UdpNetworkReceiver));
            Assert.IsInstanceOfType(receiver2, typeof(UdpNetworkReceiver));
            Assert.IsInstanceOfType(receiver3, typeof(HttpNetworkReceiver));
        }

        #endregion

        #region HttpNetworkSender Validation Tests

        [TestMethod]
        public async Task HttpNetworkSender_SendAsync_NullData_ThrowsArgumentNullException()
        {
            // Arrange
            using var sender = new HttpNetworkSender();

            // Act & Assert
            await Assert.ThrowsExceptionAsync<ArgumentNullException>(
                () => sender.SendAsync(null!, ValidIpAddress, ValidPort));
        }

        [TestMethod]
        public async Task HttpNetworkSender_SendAsync_InvalidAddress_ThrowsArgumentException()
        {
            // Arrange
            using var sender = new HttpNetworkSender();
            var data = new byte[] { 1, 2, 3 };

            // Act & Assert
            await Assert.ThrowsExceptionAsync<ArgumentException>(
                () => sender.SendAsync(data, "", ValidPort));

            await Assert.ThrowsExceptionAsync<ArgumentException>(
                () => sender.SendAsync(data, "invalid..address", ValidPort));

            await Assert.ThrowsExceptionAsync<ArgumentException>(
                () => sender.SendAsync(data, ".invalid", ValidPort));
        }

        [TestMethod]
        public async Task HttpNetworkSender_SendAsync_InvalidPort_ThrowsArgumentOutOfRangeException()
        {
            // Arrange
            using var sender = new HttpNetworkSender();
            var data = new byte[] { 1, 2, 3 };

            // Act & Assert
            await Assert.ThrowsExceptionAsync<ArgumentOutOfRangeException>(
                () => sender.SendAsync(data, ValidIpAddress, 0));

            await Assert.ThrowsExceptionAsync<ArgumentOutOfRangeException>(
                () => sender.SendAsync(data, ValidIpAddress, -1));

            await Assert.ThrowsExceptionAsync<ArgumentOutOfRangeException>(
                () => sender.SendAsync(data, ValidIpAddress, 65536));
        }

        [TestMethod]
        public async Task HttpNetworkSender_SendAsync_ValidParameters_DoesNotThrowValidationErrors()
        {
            // Arrange
            using var sender = new HttpNetworkSender();
            var data = new byte[] { 1, 2, 3 };

            // Act & Assert - Should not throw validation errors (may throw network errors)
            try
            {
                await sender.SendAsync(data, ValidIpAddress, ValidPort);
            }
            catch (Exception ex) when (ex is ArgumentException || ex is ArgumentNullException || ex is ArgumentOutOfRangeException)
            {
                Assert.Fail($"Should not throw validation errors for valid parameters: {ex.Message}");
            }
            catch
            {
                // Network errors are expected in test environment
            }

            try
            {
                await sender.SendAsync(data, ValidHostname, ValidPort);
            }
            catch (Exception ex) when (ex is ArgumentException || ex is ArgumentNullException || ex is ArgumentOutOfRangeException)
            {
                Assert.Fail($"Should not throw validation errors for valid hostname: {ex.Message}");
            }
            catch
            {
                // Network errors are expected in test environment
            }
        }

        #endregion

        #region UdpNetworkSender Validation Tests

        [TestMethod]
        public async Task UdpNetworkSender_SendAsync_InvalidPort_ThrowsArgumentOutOfRangeException()
        {
            // Arrange
            using var sender = new UdpNetworkSender();
            var data = new byte[] { 1, 2, 3 };

            // Act & Assert
            await Assert.ThrowsExceptionAsync<ArgumentOutOfRangeException>(
                () => sender.SendAsync(data, ValidIpAddress, 0));

            await Assert.ThrowsExceptionAsync<ArgumentOutOfRangeException>(
                () => sender.SendAsync(data, ValidIpAddress, 65536));
        }

        [TestMethod]
        public async Task UdpNetworkSender_SendAsync_InvalidAddress_ThrowsArgumentException()
        {
            // Arrange
            using var sender = new UdpNetworkSender();
            var data = new byte[] { 1, 2, 3 };

            // Act & Assert
            await Assert.ThrowsExceptionAsync<ArgumentException>(
                () => sender.SendAsync(data, "invalid-address", ValidPort));

            await Assert.ThrowsExceptionAsync<ArgumentException>(
                () => sender.SendAsync(data, "300.300.300.300", ValidPort));
        }

        #endregion

        #region HttpNetworkReceiver Validation Tests

        [TestMethod]
        public void HttpNetworkReceiver_Constructor_InvalidPort_ThrowsArgumentOutOfRangeException()
        {
            // Act & Assert
            Assert.ThrowsException<ArgumentOutOfRangeException>(() => new HttpNetworkReceiver(0));
            Assert.ThrowsException<ArgumentOutOfRangeException>(() => new HttpNetworkReceiver(-1));
            Assert.ThrowsException<ArgumentOutOfRangeException>(() => new HttpNetworkReceiver(65536));
        }

        [TestMethod]
        public void HttpNetworkReceiver_Constructor_ValidPort_CreatesReceiver()
        {
            // Act
            var receiver = new HttpNetworkReceiver(ValidPort);

            // Assert
            Assert.IsNotNull(receiver);
        }

        #endregion

        #region UdpNetworkReceiver Validation Tests

        [TestMethod]
        public void UdpNetworkReceiver_Constructor_InvalidPort_ThrowsArgumentOutOfRangeException()
        {
            // Act & Assert
            Assert.ThrowsException<ArgumentOutOfRangeException>(() => new UdpNetworkReceiver(0));
            Assert.ThrowsException<ArgumentOutOfRangeException>(() => new UdpNetworkReceiver(-1));
            Assert.ThrowsException<ArgumentOutOfRangeException>(() => new UdpNetworkReceiver(65536));
        }

        [TestMethod]
        public void UdpNetworkReceiver_Constructor_InvalidMulticastAddress_ThrowsArgumentException()
        {
            // Act & Assert
            Assert.ThrowsException<ArgumentException>(
                () => new UdpNetworkReceiver(ValidPort, true, "invalid-address"));

            Assert.ThrowsException<ArgumentException>(
                () => new UdpNetworkReceiver(ValidPort, true, "300.300.300.300"));
        }

        [TestMethod]
        public void UdpNetworkReceiver_Constructor_ValidParameters_CreatesReceiver()
        {
            // Act
            var receiver1 = new UdpNetworkReceiver(ValidPort);
            var receiver2 = new UdpNetworkReceiver(ValidPort, true, ValidMulticastAddress);

            // Assert
            Assert.IsNotNull(receiver1);
            Assert.IsNotNull(receiver2);
        }

        #endregion
    }
}
