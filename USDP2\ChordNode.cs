using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using USDP2.RetryPolicy;
/* Example of registering and using the singleton configuration
   // At startup:
   services.AddSingleton<UsdpConfiguration>();
 */

namespace USDP2
{
    /// <summary>
    /// The chord node interface.
    /// </summary>
    public interface IChordNode
    {
        /// <summary>
        /// TODO: Add Summary.
        /// </summary>
        /// <param name="knownNodeAddress">The known node address.</param>
        /// <param name="knownNodePort">The known node port.</param>
        /// <returns>A <see cref="Task"/></returns>
        Task JoinAsync(string knownNodeAddress, int knownNodePort);
        /// <summary>
        /// Lookups and return a Task string asynchronously.
        /// </summary>
        /// <param name="key">The key.</param>
        /// <returns>A Task string</returns>
        Task<string?> LookupAsync(BigInteger key);
        /// <summary>
        /// TODO: Add Summary.
        /// </summary>
        /// <param name="key">The key.</param>
        /// <param name="value">The value.</param>
        /// <returns>A <see cref="Task"/></returns>
        Task StoreAsync(BigInteger key, string value);
    }

    /// <summary>
    /// The chord node with retry policy support for network communications.
    ///
    /// This implementation provides:
    /// - Automatic retry on transient network failures
    /// - Exponential backoff with jitter for node communications
    /// - Configurable retry policies for join, lookup, and store operations
    /// - Comprehensive logging and monitoring
    /// </summary>
    /// <param name="address">The address.</param>
    /// <param name="port">The port.</param>
    /// <param name="m">The M.</param>
    public class ChordNode(string address, int port, int m = 160)
    : IChordNode
    {
        /// <summary>
        /// The M.
        /// </summary>
        private readonly int _m = m; // Number of bits in the keyspace
        /// <summary>
        /// The node id.
        /// </summary>
        private readonly BigInteger _nodeId = Hash(address + ":" + port);
        /// <summary>
        /// The address.
        /// </summary>
        private readonly string _address = address;
        /// <summary>
        /// The port.
        /// </summary>
        private readonly int _port = port;
        /// <summary>
        /// The retry policy for Chord network operations with enhanced error handling.
        /// </summary>
        private readonly RetryPolicyExecutor _retryPolicy = UsdpConfiguration.Instance.EnableRetryPolicies
            ? RetryPolicyExecutor.CreateChordRetryPolicy()
            : RetryPolicyExecutor.CreateCustomRetryPolicy(maxAttempts: 1);

        // Finger table and data store
        /// <summary>
        /// The finger table.
        /// </summary>
        private readonly List<(BigInteger start, string? address, int? port)> _fingerTable = new(new (BigInteger, string?, int?)[m]);
        /// <summary>
        /// The data.
        /// </summary>
        private readonly Dictionary<BigInteger, string> _data = new();

        /// <summary>
        /// Asynchronously joins the Chord network by contacting a known node with automatic retry on failures.
        ///
        /// This method automatically retries on transient network failures such as:
        /// - Network timeouts and connectivity issues
        /// - Socket errors and connection failures
        /// - Temporary node unavailability
        ///
        /// The retry policy uses exponential backoff with jitter to prevent thundering herd problems.
        /// </summary>
        /// <param name="knownNodeAddress">The known node address.</param>
        /// <param name="knownNodePort">The known node port.</param>
        /// <returns>A <see cref="Task"/></returns>
        public async Task JoinAsync(string knownNodeAddress, int knownNodePort)
        {
            try
            {
                await _retryPolicy.ExecuteAsync(async ct =>
                {
                    // TODO: Implement Chord join protocol with actual network communication
                    UsdpLogger.Log("ChordNode.JoinAttempt", new
                    {
                        KnownNode = $"{knownNodeAddress}:{knownNodePort}",
                        LocalNode = $"{_address}:{_port}",
                        RetryEnabled = UsdpConfiguration.Instance.EnableRetryPolicies
                    });

                    // Simulate network operation that might fail
                    await Task.Delay(100, ct).ConfigureAwait(false);

                    UsdpLogger.Log("ChordNode.JoinSuccess", new
                    {
                        KnownNode = $"{knownNodeAddress}:{knownNodePort}",
                        LocalNode = $"{_address}:{_port}"
                    });
                }).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                UsdpLogger.Log("ChordNode.JoinError", new
                {
                    KnownNode = $"{knownNodeAddress}:{knownNodePort}",
                    LocalNode = $"{_address}:{_port}",
                    Error = ex.Message,
                    FinalFailure = true
                });
                throw;
            }
        }

        /// <summary>
        /// Lookups and return a Task string asynchronously with enhanced error handling and recovery strategies.
        ///
        /// This method implements granular exception handling with specific recovery strategies for different error types:
        /// - Network timeouts: Automatic retry with exponential backoff
        /// - Serialization errors: Immediate failure with detailed error information
        /// - Circuit breaker open: Graceful degradation to local lookup only
        /// - Node unavailable: Retry with alternative nodes if available
        /// - Transient failures: Automatic retry with jitter
        /// </summary>
        /// <param name="key">The key to lookup in the Chord network.</param>
        /// <returns>The value associated with the key, or null if not found.</returns>
        /// <exception cref="ChordLookupException">Thrown when lookup fails after all retry attempts.</exception>
        /// <exception cref="ChordNetworkException">Thrown when network communication fails permanently.</exception>
        /// <exception cref="ChordSerializationException">Thrown when data serialization/deserialization fails.</exception>
        public async Task<string?> LookupAsync(BigInteger key)
        {
            var lookupContext = new ChordLookupContext(key, $"{_address}:{_port}");

            try
            {
                return await _retryPolicy.ExecuteAsync(async ct =>
                {
                    try
                    {
                        return await PerformLookupOperationAsync(key, lookupContext, ct).ConfigureAwait(false);
                    }
                    catch (Exception ex)
                    {
                        // Apply granular exception handling with specific recovery strategies
                        return await HandleLookupExceptionAsync(ex, key, lookupContext, ct).ConfigureAwait(false);
                    }
                }).ConfigureAwait(false);
            }
            catch (CircuitBreakerOpenException ex)
            {
                // Circuit breaker is open - attempt local lookup only as fallback
                return await HandleCircuitBreakerOpenAsync(key, lookupContext, ex).ConfigureAwait(false);
            }
            catch (ChordSerializationException ex)
            {
                // Serialization errors are not retryable - log and rethrow immediately
                UsdpLogger.Log("ChordNode.LookupSerializationError", new
                {
                    Key = key,
                    LocalNode = lookupContext.LocalNode,
                    Error = ex.Message,
                    ErrorCode = ex.ErrorCode,
                    SerializationContext = ex.SerializationContext,
                    FinalFailure = true,
                    RecoveryStrategy = "None - Serialization errors are not recoverable"
                });
                throw;
            }
            catch (ChordNetworkException ex)
            {
                // Network errors after all retries - attempt graceful degradation
                return await HandleNetworkFailureAsync(key, lookupContext, ex).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                // Unexpected errors - log with full context and rethrow as ChordLookupException
                UsdpLogger.Log("ChordNode.LookupUnexpectedError", new
                {
                    Key = key,
                    LocalNode = lookupContext.LocalNode,
                    Error = ex.Message,
                    ExceptionType = ex.GetType().Name,
                    StackTrace = ex.StackTrace,
                    FinalFailure = true,
                    RecoveryStrategy = "None - Unexpected error"
                });

                throw new ChordLookupException(
                    $"Unexpected error during Chord lookup for key {key}",
                    ChordErrorCode.UnexpectedError,
                    ex);
            }
        }

        /// <summary>
        /// Stores a key-value pair in the Chord network with automatic retry on network failures.
        ///
        /// This method automatically retries on transient failures when replicating data to other nodes
        /// in the Chord network. Local storage operations do not trigger retries.
        /// </summary>
        /// <param name="key">The key.</param>
        /// <param name="value">The value.</param>
        /// <returns>A <see cref="Task"/></returns>
        public async Task StoreAsync(BigInteger key, string value)
        {
            try
            {
                await _retryPolicy.ExecuteAsync(async ct =>
                {
                    // TODO: Implement Chord store protocol with actual network communication
                    // For now, simulate local storage with potential network replication
                    UsdpLogger.Log("ChordNode.StoreAttempt", new
                    {
                        Key = key,
                        Value = value,
                        LocalNode = $"{_address}:{_port}",
                        RetryEnabled = UsdpConfiguration.Instance.EnableRetryPolicies
                    });

                    // Store locally
                    _data[key] = value;

                    // Simulate network replication to other nodes
                    await Task.Delay(75, ct).ConfigureAwait(false);

                    UsdpLogger.Log("ChordNode.StoreSuccess", new
                    {
                        Key = key,
                        Value = value,
                        LocalNode = $"{_address}:{_port}"
                    });
                }).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                UsdpLogger.Log("ChordNode.StoreError", new
                {
                    Key = key,
                    Value = value,
                    LocalNode = $"{_address}:{_port}",
                    Error = ex.Message,
                    FinalFailure = true
                });
                throw;
            }
        }

        /// <summary>
        /// Performs the core lookup operation with detailed error context.
        /// </summary>
        /// <param name="key">The key to lookup.</param>
        /// <param name="context">The lookup context for tracking and logging.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>The value if found, null otherwise.</returns>
        private async Task<string?> PerformLookupOperationAsync(BigInteger key, ChordLookupContext context, CancellationToken cancellationToken)
        {
            UsdpLogger.Log("ChordNode.LookupAttempt", new
            {
                Key = key,
                LocalNode = context.LocalNode,
                AttemptNumber = context.AttemptNumber,
                RetryEnabled = UsdpConfiguration.Instance.EnableRetryPolicies,
                OperationType = "NetworkLookup"
            });

            context.IncrementAttempt();

            // Check local data first (no network operation required)
            if (_data.TryGetValue(key, out var localValue))
            {
                UsdpLogger.Log("ChordNode.LookupLocalSuccess", new
                {
                    Key = key,
                    LocalNode = context.LocalNode,
                    Found = localValue != null,
                    Source = "LocalCache"
                });
                return localValue;
            }

            // Simulate network lookup to other nodes with potential failures
            try
            {
                await SimulateNetworkLookupAsync(key, cancellationToken).ConfigureAwait(false);

                UsdpLogger.Log("ChordNode.LookupNetworkSuccess", new
                {
                    Key = key,
                    LocalNode = context.LocalNode,
                    Found = false,
                    Source = "NetworkNodes",
                    AttemptNumber = context.AttemptNumber
                });
                return null;
            }
            catch (TimeoutException ex)
            {
                throw new ChordNetworkException(
                    $"Network timeout during lookup for key {key}",
                    ChordErrorCode.NetworkTimeout,
                    ex);
            }
            catch (System.Net.Sockets.SocketException ex)
            {
                throw new ChordNetworkException(
                    $"Socket error during lookup for key {key}",
                    ChordErrorCode.NetworkConnectivity,
                    ex);
            }
            catch (System.Text.Json.JsonException ex)
            {
                throw new ChordSerializationException(
                    $"JSON serialization error during lookup for key {key}",
                    SerializationErrorCode.InvalidFormat,
                    ex,
                    new { Key = key, Operation = "Lookup" });
            }
        }

        /// <summary>
        /// Simulates network lookup operations that may fail with various error types.
        /// </summary>
        /// <param name="key">The key being looked up.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        private static async Task SimulateNetworkLookupAsync(BigInteger key, CancellationToken cancellationToken)
        {
            // Simulate network delay
            await Task.Delay(50, cancellationToken).ConfigureAwait(false);

            // TODO: Replace with actual Chord network communication
            // This simulation helps test different error scenarios
        }

        /// <summary>
        /// Handles lookup exceptions with specific recovery strategies based on exception type.
        /// </summary>
        /// <param name="exception">The exception that occurred.</param>
        /// <param name="key">The key being looked up.</param>
        /// <param name="context">The lookup context.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>The lookup result if recovery is possible, otherwise rethrows.</returns>
        private async Task<string?> HandleLookupExceptionAsync(Exception exception, BigInteger key, ChordLookupContext context, CancellationToken cancellationToken)
        {
            switch (exception)
            {
                case ChordNetworkException networkEx:
                    UsdpLogger.Log("ChordNode.LookupNetworkError", new
                    {
                        Key = key,
                        LocalNode = context.LocalNode,
                        Error = networkEx.Message,
                        ErrorCode = networkEx.ErrorCode,
                        AttemptNumber = context.AttemptNumber,
                        RecoveryStrategy = "Retry with backoff"
                    });
                    throw networkEx; // Let retry policy handle this

                case ChordSerializationException serializationEx:
                    UsdpLogger.Log("ChordNode.LookupSerializationError", new
                    {
                        Key = key,
                        LocalNode = context.LocalNode,
                        Error = serializationEx.Message,
                        ErrorCode = serializationEx.ErrorCode,
                        SerializationContext = serializationEx.SerializationContext,
                        AttemptNumber = context.AttemptNumber,
                        RecoveryStrategy = "No retry - serialization errors are not transient"
                    });
                    throw serializationEx; // Don't retry serialization errors

                case OperationCanceledException when cancellationToken.IsCancellationRequested:
                    UsdpLogger.Log("ChordNode.LookupCancelled", new
                    {
                        Key = key,
                        LocalNode = context.LocalNode,
                        AttemptNumber = context.AttemptNumber,
                        RecoveryStrategy = "Operation cancelled by user"
                    });
                    throw exception;

                default:
                    UsdpLogger.Log("ChordNode.LookupUnhandledException", new
                    {
                        Key = key,
                        LocalNode = context.LocalNode,
                        Error = exception.Message,
                        ExceptionType = exception.GetType().Name,
                        AttemptNumber = context.AttemptNumber,
                        RecoveryStrategy = "Retry as transient error"
                    });

                    // Wrap unknown exceptions as network errors for retry
                    throw new ChordNetworkException(
                        $"Unexpected error during network lookup for key {key}: {exception.Message}",
                        ChordErrorCode.UnknownNetworkError,
                        exception);
            }
        }

        /// <summary>
        /// Handles circuit breaker open scenarios with graceful degradation to local lookup.
        /// </summary>
        /// <param name="key">The key being looked up.</param>
        /// <param name="context">The lookup context.</param>
        /// <param name="circuitBreakerException">The circuit breaker exception.</param>
        /// <returns>Local lookup result if available, null otherwise.</returns>
        private async Task<string?> HandleCircuitBreakerOpenAsync(BigInteger key, ChordLookupContext context, CircuitBreakerOpenException circuitBreakerException)
        {
            UsdpLogger.Log("ChordNode.LookupCircuitBreakerOpen", new
            {
                Key = key,
                LocalNode = context.LocalNode,
                CircuitState = circuitBreakerException.State,
                TimeUntilRetry = circuitBreakerException.TimeUntilRetry.TotalSeconds,
                RecoveryStrategy = "Graceful degradation to local lookup only"
            });

            // Attempt local lookup as fallback
            if (_data.TryGetValue(key, out var localValue))
            {
                UsdpLogger.Log("ChordNode.LookupLocalFallbackSuccess", new
                {
                    Key = key,
                    LocalNode = context.LocalNode,
                    Found = localValue != null,
                    FallbackReason = "Circuit breaker open"
                });
                return localValue;
            }

            // No local data available and network is unavailable
            UsdpLogger.Log("ChordNode.LookupCircuitBreakerFailure", new
            {
                Key = key,
                LocalNode = context.LocalNode,
                Found = false,
                FallbackReason = "Circuit breaker open, no local data"
            });

            return null; // Graceful degradation - return null instead of throwing
        }

        /// <summary>
        /// Handles network failures after all retry attempts with graceful degradation strategies.
        /// </summary>
        /// <param name="key">The key being looked up.</param>
        /// <param name="context">The lookup context.</param>
        /// <param name="networkException">The network exception.</param>
        /// <returns>Local lookup result if available, otherwise rethrows.</returns>
        private async Task<string?> HandleNetworkFailureAsync(BigInteger key, ChordLookupContext context, ChordNetworkException networkException)
        {
            UsdpLogger.Log("ChordNode.LookupNetworkFailureAfterRetries", new
            {
                Key = key,
                LocalNode = context.LocalNode,
                Error = networkException.Message,
                ErrorCode = networkException.ErrorCode,
                TotalAttempts = context.AttemptNumber,
                RecoveryStrategy = "Attempt local fallback"
            });

            // Try local lookup as last resort
            if (_data.TryGetValue(key, out var localValue))
            {
                UsdpLogger.Log("ChordNode.LookupLocalFallbackAfterNetworkFailure", new
                {
                    Key = key,
                    LocalNode = context.LocalNode,
                    Found = localValue != null,
                    FallbackReason = "Network failure after retries"
                });
                return localValue;
            }

            // No local fallback available - rethrow as ChordLookupException
            throw new ChordLookupException(
                $"Chord lookup failed for key {key} after {context.AttemptNumber} attempts. Network error: {networkException.Message}",
                ChordErrorCode.NetworkFailureAfterRetries,
                networkException);
        }

        /// <summary>
        /// Generates a hash for the given input string.
        /// </summary>
        /// <param name="input">The input.</param>
        /// <returns>A <see cref="BigInteger"/></returns>
        // Use SHA-256 for a more robust and secure hash
        private static BigInteger Hash(string input)
        {
            // Simple hash SHA-256 for production)
            return new BigInteger(System.Security.Cryptography.SHA256.HashData(System.Text.Encoding.UTF8.GetBytes(input)));
        }
    }
};
