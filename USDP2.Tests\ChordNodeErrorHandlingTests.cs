using System;
using System.Numerics;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using USDP2.RetryPolicy;

namespace USDP2.Tests
{
    /// <summary>
    /// Tests for enhanced error handling in ChordNode operations.
    /// Validates that ChordNode properly handles different types of exceptions
    /// with appropriate recovery strategies and granular error information.
    /// </summary>
    [TestClass]
    public class ChordNodeErrorHandlingTests
    {
        private ChordNode _chordNode = null!;
        private const string TestAddress = "127.0.0.1";
        private const int TestPort = 8080;
        private readonly BigInteger _testKey = new(12345);

        [TestInitialize]
        public void Setup()
        {
            _chordNode = new ChordNode(TestAddress, TestPort);
        }

        #region Exception Type Tests

        [TestMethod]
        public void ChordExceptions_HaveCorrectErrorCodes()
        {
            // Test ChordNetworkException
            var networkEx = new ChordNetworkException("Network error", ChordErrorCode.NetworkTimeout);
            Assert.AreEqual(ChordErrorCode.NetworkTimeout, networkEx.ErrorCode);
            Assert.AreEqual("Network error", networkEx.Message);

            // Test ChordSerializationException
            var serializationEx = new ChordSerializationException("Serialization error", SerializationErrorCode.InvalidFormat);
            Assert.AreEqual(SerializationErrorCode.InvalidFormat, serializationEx.SerializationErrorCode);
            Assert.AreEqual("Serialization error", serializationEx.Message);

            // Test ChordLookupException
            var lookupEx = new ChordLookupException("Lookup error", ChordErrorCode.LookupFailure);
            Assert.AreEqual(ChordErrorCode.LookupFailure, lookupEx.ErrorCode);
            Assert.AreEqual("Lookup error", lookupEx.Message);
        }

        [TestMethod]
        public void ChordLookupContext_TracksAttemptsCorrectly()
        {
            // Arrange
            var context = new ChordLookupContext(_testKey, "test-node");

            // Assert initial state
            Assert.AreEqual(_testKey, context.Key);
            Assert.AreEqual("test-node", context.LocalNode);
            Assert.AreEqual(0, context.AttemptNumber);
            Assert.IsTrue(context.ElapsedTime >= TimeSpan.Zero);

            // Act
            context.IncrementAttempt();
            context.IncrementAttempt();

            // Assert
            Assert.AreEqual(2, context.AttemptNumber);
        }

        #endregion

        #region Retry Condition Tests

        [TestMethod]
        public void ChordRetryCondition_HandlesChordNetworkExceptions()
        {
            // Arrange
            var retryCondition = new ChordRetryCondition();

            // Test retryable network errors
            var timeoutEx = new ChordNetworkException("Timeout", ChordErrorCode.NetworkTimeout);
            var connectivityEx = new ChordNetworkException("Connectivity", ChordErrorCode.NetworkConnectivity);
            var nodeUnavailableEx = new ChordNetworkException("Node unavailable", ChordErrorCode.NodeUnavailable);

            var timeoutContext = new RetryContext(1, timeoutEx, TimeSpan.FromSeconds(1));
            var connectivityContext = new RetryContext(1, connectivityEx, TimeSpan.FromSeconds(1));
            var nodeUnavailableContext = new RetryContext(1, nodeUnavailableEx, TimeSpan.FromSeconds(1));

            // Act & Assert
            Assert.AreEqual(RetryDecision.Retry, retryCondition.ShouldRetry(timeoutContext));
            Assert.AreEqual(RetryDecision.Retry, retryCondition.ShouldRetry(connectivityContext));
            Assert.AreEqual(RetryDecision.Retry, retryCondition.ShouldRetry(nodeUnavailableContext));

            // Test non-retryable network errors
            var alreadyRetriedEx = new ChordNetworkException("Already retried", ChordErrorCode.NetworkFailureAfterRetries);
            var alreadyRetriedContext = new RetryContext(1, alreadyRetriedEx, TimeSpan.FromSeconds(1));

            Assert.AreEqual(RetryDecision.DoNotRetry, retryCondition.ShouldRetry(alreadyRetriedContext));
        }

        [TestMethod]
        public void ChordRetryCondition_HandlesSerializationExceptions()
        {
            // Arrange
            var retryCondition = new ChordRetryCondition();
            var serializationEx = new ChordSerializationException("Serialization failed", SerializationErrorCode.InvalidFormat);
            var context = new RetryContext(1, serializationEx, TimeSpan.FromSeconds(1));

            // Act & Assert - Serialization errors should not be retried
            Assert.AreEqual(RetryDecision.DoNotRetry, retryCondition.ShouldRetry(context));
        }

        [TestMethod]
        public void ChordRetryCondition_HandlesCircuitBreakerExceptions()
        {
            // Arrange
            var retryCondition = new ChordRetryCondition();
            var circuitBreakerEx = new CircuitBreakerOpenException(CircuitBreakerState.Open, TimeSpan.FromSeconds(30));
            var context = new RetryContext(1, circuitBreakerEx, TimeSpan.FromSeconds(1));

            // Act & Assert - Circuit breaker exceptions should not be retried
            Assert.AreEqual(RetryDecision.DoNotRetry, retryCondition.ShouldRetry(context));
        }

        [TestMethod]
        public void ChordRetryCondition_HandlesLookupExceptions()
        {
            // Arrange
            var retryCondition = new ChordRetryCondition();

            // Test retryable lookup errors
            var lookupFailureEx = new ChordLookupException("Lookup failed", ChordErrorCode.LookupFailure);
            var ringInconsistencyEx = new ChordLookupException("Ring inconsistent", ChordErrorCode.RingInconsistency);

            var lookupFailureContext = new RetryContext(1, lookupFailureEx, TimeSpan.FromSeconds(1));
            var ringInconsistencyContext = new RetryContext(1, ringInconsistencyEx, TimeSpan.FromSeconds(1));

            // Act & Assert
            Assert.AreEqual(RetryDecision.Retry, retryCondition.ShouldRetry(lookupFailureContext));
            Assert.AreEqual(RetryDecision.Retry, retryCondition.ShouldRetry(ringInconsistencyContext));

            // Test non-retryable lookup errors
            var unexpectedEx = new ChordLookupException("Unexpected error", ChordErrorCode.UnexpectedError);
            var unexpectedContext = new RetryContext(1, unexpectedEx, TimeSpan.FromSeconds(1));

            Assert.AreEqual(RetryDecision.DoNotRetry, retryCondition.ShouldRetry(unexpectedContext));
        }

        #endregion

        #region Lookup Operation Tests

        [TestMethod]
        public async Task LookupAsync_WithLocalData_ReturnsValueWithoutNetworkCall()
        {
            // Arrange
            var testValue = "test-value";
            await _chordNode.StoreAsync(_testKey, testValue);

            // Act
            var result = await _chordNode.LookupAsync(_testKey);

            // Assert
            Assert.AreEqual(testValue, result);
        }

        [TestMethod]
        public async Task LookupAsync_WithoutLocalData_ReturnsNull()
        {
            // Act
            var result = await _chordNode.LookupAsync(_testKey);

            // Assert
            Assert.IsNull(result);
        }

        [TestMethod]
        public async Task LookupAsync_WithCancellation_ThrowsOperationCanceledException()
        {
            // Arrange
            using var cts = new CancellationTokenSource();
            cts.Cancel();

            // Act & Assert
            await Assert.ThrowsExceptionAsync<OperationCanceledException>(
                () => _chordNode.LookupAsync(_testKey));
        }

        #endregion

        #region Error Handling Integration Tests

        [TestMethod]
        public async Task LookupAsync_HandlesUnexpectedExceptions()
        {
            // This test verifies that unexpected exceptions are properly wrapped
            // and logged with appropriate context information.
            // Since the current implementation is simulated, we test the structure
            // rather than actual network failures.

            try
            {
                var result = await _chordNode.LookupAsync(_testKey);
                // Should complete successfully in simulation
                Assert.IsNull(result); // No data stored, should return null
            }
            catch (ChordLookupException ex)
            {
                // If an exception occurs, it should be properly wrapped
                Assert.IsNotNull(ex.ErrorCode);
                Assert.IsNotNull(ex.Message);
            }
            catch (Exception ex)
            {
                Assert.Fail($"Unexpected exception type: {ex.GetType().Name}. Should be wrapped in ChordLookupException.");
            }
        }

        [TestMethod]
        public void RetryPolicyExecutor_CreateChordRetryPolicy_ReturnsValidPolicy()
        {
            // Act
            var retryPolicy = RetryPolicyExecutor.CreateChordRetryPolicy();

            // Assert
            Assert.IsNotNull(retryPolicy);
            // The policy should be configured with ChordRetryCondition
            // This is tested indirectly through the retry behavior
        }

        #endregion

        #region Performance and Logging Tests

        [TestMethod]
        public async Task LookupAsync_LogsDetailedInformation()
        {
            // This test verifies that the enhanced error handling provides
            // detailed logging information for debugging and monitoring.

            // Arrange
            var testValue = "test-value-for-logging";
            await _chordNode.StoreAsync(_testKey, testValue);

            // Act
            var result = await _chordNode.LookupAsync(_testKey);

            // Assert
            Assert.AreEqual(testValue, result);
            // Logging verification would require a test logger implementation
            // For now, we verify the operation completes successfully
        }

        [TestMethod]
        public async Task LookupAsync_PerformanceWithLocalData()
        {
            // Arrange
            var testValue = "performance-test-value";
            await _chordNode.StoreAsync(_testKey, testValue);

            // Act
            var startTime = DateTime.UtcNow;
            var result = await _chordNode.LookupAsync(_testKey);
            var endTime = DateTime.UtcNow;

            // Assert
            Assert.AreEqual(testValue, result);
            var duration = endTime - startTime;
            Assert.IsTrue(duration < TimeSpan.FromMilliseconds(100),
                $"Local lookup took too long: {duration.TotalMilliseconds}ms");
        }

        #endregion
    }
}
