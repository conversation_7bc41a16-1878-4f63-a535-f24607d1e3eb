using System;
using System.Collections.Generic;
using System.Linq;

namespace USDP2
{
    /// <summary>
    /// Defines the types of security events that can be tracked and monitored.
    /// </summary>
    public enum SecurityEventType
    {
        /// <summary>
        /// Rate limit exceeded for an endpoint.
        /// </summary>
        RateLimitExceeded,

        /// <summary>
        /// Message size exceeds allowed limits.
        /// </summary>
        OversizedMessage,

        /// <summary>
        /// Invalid or malformed data received.
        /// </summary>
        InvalidData,

        /// <summary>
        /// Suspicious binary patterns detected.
        /// </summary>
        SuspiciousBinaryPattern,

        /// <summary>
        /// Unknown or unsupported message type.
        /// </summary>
        UnknownMessageType,

        /// <summary>
        /// Validation failure during deserialization.
        /// </summary>
        ValidationFailure,

        /// <summary>
        /// Suspicious message size patterns detected.
        /// </summary>
        SuspiciousMessageSizePattern,

        /// <summary>
        /// Excessive nesting depth in data structures.
        /// </summary>
        ExcessiveNesting,

        /// <summary>
        /// Authentication failure or invalid credentials.
        /// </summary>
        AuthenticationFailure,

        /// <summary>
        /// Potential scanning or reconnaissance activity.
        /// </summary>
        ScanningActivity,

        /// <summary>
        /// Coordinated attack detected across multiple endpoints.
        /// </summary>
        CoordinatedAttack,

        /// <summary>
        /// Endpoint has been blocked due to security concerns.
        /// </summary>
        EndpointBlocked,

        /// <summary>
        /// General security violation that doesn't fit other categories.
        /// </summary>
        SecurityViolation
    }

    /// <summary>
    /// Defines the severity levels for security events.
    /// </summary>
    public enum SecurityEventSeverity
    {
        /// <summary>
        /// Low severity - informational events that may indicate normal variations.
        /// </summary>
        Low,

        /// <summary>
        /// Medium severity - events that warrant monitoring but don't require immediate action.
        /// </summary>
        Medium,

        /// <summary>
        /// High severity - events that indicate potential security issues requiring attention.
        /// </summary>
        High,

        /// <summary>
        /// Critical severity - events that indicate active attacks or serious security breaches.
        /// </summary>
        Critical
    }

    /// <summary>
    /// Represents a security event with all relevant details.
    /// </summary>
    public class SecurityEvent
    {
        /// <summary>
        /// Gets or sets the type of security event.
        /// </summary>
        public SecurityEventType EventType { get; set; }

        /// <summary>
        /// Gets or sets the remote IP address involved in the event.
        /// </summary>
        public string RemoteAddress { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the remote port number involved in the event.
        /// </summary>
        public int RemotePort { get; set; }

        /// <summary>
        /// Gets or sets additional details about the event.
        /// </summary>
        public string Details { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the severity level of the event.
        /// </summary>
        public SecurityEventSeverity Severity { get; set; }

        /// <summary>
        /// Gets or sets the timestamp when the event occurred.
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// Returns a string representation of the security event.
        /// </summary>
        /// <returns>A string describing the security event.</returns>
        public override string ToString()
        {
            return $"[{Timestamp:yyyy-MM-dd HH:mm:ss}] {Severity} - {EventType}: {Details} " +
                   $"(From: {RemoteAddress}:{RemotePort})";
        }
    }

    /// <summary>
    /// Represents comprehensive security statistics for monitoring and reporting.
    /// </summary>
    public class SecurityStatistics
    {
        /// <summary>
        /// Gets or sets the total number of tracked endpoints.
        /// </summary>
        public int TotalEndpoints { get; set; }

        /// <summary>
        /// Gets or sets the number of endpoints flagged as suspicious.
        /// </summary>
        public int SuspiciousEndpoints { get; set; }

        /// <summary>
        /// Gets or sets the number of endpoints currently blocked.
        /// </summary>
        public int BlockedEndpoints { get; set; }

        /// <summary>
        /// Gets or sets the count of recent security events.
        /// </summary>
        public int RecentEventsCount { get; set; }

        /// <summary>
        /// Gets or sets the breakdown of events by type.
        /// </summary>
        public Dictionary<SecurityEventType, int> EventsByType { get; set; } = new();

        /// <summary>
        /// Gets or sets the breakdown of events by severity.
        /// </summary>
        public Dictionary<SecurityEventSeverity, int> EventsBySeverity { get; set; } = new();

        /// <summary>
        /// Gets or sets the list of top suspicious endpoints.
        /// </summary>
        public List<SuspiciousEndpointInfo> TopSuspiciousEndpoints { get; set; } = new();

        /// <summary>
        /// Gets the overall security health score (0-100).
        /// </summary>
        public int SecurityHealthScore
        {
            get
            {
                if (TotalEndpoints == 0)
                    return 100;

                var suspiciousRatio = (double)SuspiciousEndpoints / TotalEndpoints;
                var blockedRatio = (double)BlockedEndpoints / TotalEndpoints;
                var criticalEvents = EventsBySeverity.GetValueOrDefault(SecurityEventSeverity.Critical, 0);
                
                var score = 100;
                score -= (int)(suspiciousRatio * 30); // Suspicious endpoints reduce score
                score -= (int)(blockedRatio * 50);    // Blocked endpoints reduce score more
                score -= Math.Min(criticalEvents * 5, 20); // Critical events reduce score
                
                return Math.Max(0, score);
            }
        }

        /// <summary>
        /// Returns a string representation of the security statistics.
        /// </summary>
        /// <returns>A string describing the security statistics.</returns>
        public override string ToString()
        {
            return $"Security Health Score: {SecurityHealthScore}/100, " +
                   $"Endpoints: {TotalEndpoints} (Suspicious: {SuspiciousEndpoints}, Blocked: {BlockedEndpoints}), " +
                   $"Recent Events: {RecentEventsCount}";
        }
    }

    /// <summary>
    /// Represents information about a suspicious endpoint.
    /// </summary>
    public class SuspiciousEndpointInfo
    {
        /// <summary>
        /// Gets or sets the endpoint key (IP:Port).
        /// </summary>
        public string EndpointKey { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the threat score for this endpoint.
        /// </summary>
        public double ThreatScore { get; set; }

        /// <summary>
        /// Gets or sets the total number of events from this endpoint.
        /// </summary>
        public int EventCount { get; set; }

        /// <summary>
        /// Gets or sets the timestamp of the last activity from this endpoint.
        /// </summary>
        public DateTime LastActivity { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this endpoint is currently blocked.
        /// </summary>
        public bool IsBlocked { get; set; }

        /// <summary>
        /// Returns a string representation of the suspicious endpoint information.
        /// </summary>
        /// <returns>A string describing the suspicious endpoint.</returns>
        public override string ToString()
        {
            return $"{EndpointKey}: Threat Score {ThreatScore:F2}, Events: {EventCount}, " +
                   $"Last Activity: {LastActivity:yyyy-MM-dd HH:mm:ss}, Blocked: {IsBlocked}";
        }
    }
}
