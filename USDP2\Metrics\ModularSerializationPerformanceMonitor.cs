using System;
using System.Diagnostics;

namespace USDP2.Metrics
{
    /// <summary>
    /// Modular serialization performance monitor that uses the centralized metrics system.
    /// This replaces the static SerializationPerformanceMonitor with a cleaner, more testable approach.
    /// </summary>
    public class ModularSerializationPerformanceMonitor
    {
        private readonly SerializationMetricsCollector _metricsCollector;
        private readonly bool _isEnabled;
        private readonly double _samplingRate;

        /// <summary>
        /// Initializes a new instance of the <see cref="ModularSerializationPerformanceMonitor"/> class.
        /// </summary>
        /// <param name="metricsCollector">The metrics collector to use. If null, uses the default from MetricsManager.</param>
        /// <param name="isEnabled">Whether performance monitoring is enabled.</param>
        /// <param name="samplingRate">The sampling rate for performance measurements (0.0 to 1.0).</param>
        public ModularSerializationPerformanceMonitor(
            SerializationMetricsCollector? metricsCollector = null,
            bool isEnabled = true,
            double samplingRate = 1.0)
        {
            _metricsCollector = metricsCollector ?? 
                MetricsManager.Instance.GetCollector<SerializationMetricsCollector>("Serialization") ??
                new SerializationMetricsCollector();
            _isEnabled = isEnabled;
            _samplingRate = Math.Clamp(samplingRate, 0.0, 1.0);
        }

        /// <summary>
        /// Gets whether performance monitoring is enabled.
        /// </summary>
        public bool IsEnabled => _isEnabled;

        /// <summary>
        /// Gets the current sampling rate.
        /// </summary>
        public double SamplingRate => _samplingRate;

        /// <summary>
        /// Records a serialization operation.
        /// </summary>
        /// <param name="typeName">The name of the type being serialized.</param>
        /// <param name="format">The serialization format (JSON, CBOR, etc.).</param>
        /// <param name="elapsedMilliseconds">The time taken in milliseconds.</param>
        /// <param name="sizeInBytes">The size of the serialized data in bytes.</param>
        /// <param name="isError">Whether the operation resulted in an error.</param>
        /// <param name="isCacheHit">Whether this was served from cache.</param>
        public void RecordSerialization(string typeName, string format, long elapsedMilliseconds, 
            long sizeInBytes, bool isError = false, bool isCacheHit = false)
        {
            if (!ShouldSample()) return;

            var duration = TimeSpan.FromMilliseconds(elapsedMilliseconds);
            _metricsCollector.RecordSerialization(typeName, format, duration, sizeInBytes, isError, isCacheHit);
        }

        /// <summary>
        /// Records a deserialization operation.
        /// </summary>
        /// <param name="typeName">The name of the type being deserialized.</param>
        /// <param name="format">The serialization format (JSON, CBOR, etc.).</param>
        /// <param name="elapsedMilliseconds">The time taken in milliseconds.</param>
        /// <param name="sizeInBytes">The size of the serialized data in bytes.</param>
        /// <param name="isError">Whether the operation resulted in an error.</param>
        /// <param name="isCacheHit">Whether this was served from cache.</param>
        public void RecordDeserialization(string typeName, string format, long elapsedMilliseconds, 
            long sizeInBytes, bool isError = false, bool isCacheHit = false)
        {
            if (!ShouldSample()) return;

            var duration = TimeSpan.FromMilliseconds(elapsedMilliseconds);
            _metricsCollector.RecordDeserialization(typeName, format, duration, sizeInBytes, isError, isCacheHit);
        }

        /// <summary>
        /// Creates a performance measurement wrapper for a serialization function.
        /// </summary>
        /// <typeparam name="T">The type being serialized.</typeparam>
        /// <typeparam name="TResult">The result type of the serialization.</typeparam>
        /// <param name="serializeFunc">The serialization function to measure.</param>
        /// <param name="getSizeFunc">A function to get the size of the serialized data.</param>
        /// <param name="format">The serialization format.</param>
        /// <returns>A wrapped function that measures performance.</returns>
        public Func<T, TResult> MeasureSerialization<T, TResult>(
            Func<T, TResult> serializeFunc,
            Func<TResult, long> getSizeFunc,
            string format = "Unknown")
            where T : class
        {
            return obj =>
            {
                if (!ShouldSample())
                {
                    return serializeFunc(obj);
                }

                var stopwatch = Stopwatch.StartNew();
                var typeName = typeof(T).Name;
                bool isError = false;
                TResult result = default!;

                try
                {
                    result = serializeFunc(obj);
                    return result;
                }
                catch
                {
                    isError = true;
                    throw;
                }
                finally
                {
                    stopwatch.Stop();
                    var size = !isError && result != null ? getSizeFunc(result) : 0;
                    RecordSerialization(typeName, format, stopwatch.ElapsedMilliseconds, size, isError);
                }
            };
        }

        /// <summary>
        /// Creates a performance measurement wrapper for a deserialization function.
        /// </summary>
        /// <typeparam name="T">The type being deserialized.</typeparam>
        /// <typeparam name="TInput">The input type for deserialization.</typeparam>
        /// <param name="deserializeFunc">The deserialization function to measure.</param>
        /// <param name="getSizeFunc">A function to get the size of the input data.</param>
        /// <param name="format">The serialization format.</param>
        /// <returns>A wrapped function that measures performance.</returns>
        public Func<TInput, T> MeasureDeserialization<T, TInput>(
            Func<TInput, T> deserializeFunc,
            Func<TInput, long> getSizeFunc,
            string format = "Unknown")
            where T : class
        {
            return input =>
            {
                if (!ShouldSample())
                {
                    return deserializeFunc(input);
                }

                var stopwatch = Stopwatch.StartNew();
                var typeName = typeof(T).Name;
                bool isError = false;

                try
                {
                    return deserializeFunc(input);
                }
                catch
                {
                    isError = true;
                    throw;
                }
                finally
                {
                    stopwatch.Stop();
                    var size = getSizeFunc(input);
                    RecordDeserialization(typeName, format, stopwatch.ElapsedMilliseconds, size, isError);
                }
            };
        }

        /// <summary>
        /// Gets a performance report for all serialization operations.
        /// </summary>
        /// <returns>A performance summary.</returns>
        public SerializationPerformanceSummary GetPerformanceReport()
        {
            return _metricsCollector.GetPerformanceSummary();
        }

        /// <summary>
        /// Gets a detailed performance report as JSON.
        /// </summary>
        /// <returns>A JSON string containing the detailed performance report.</returns>
        public string GetDetailedPerformanceReport()
        {
            var snapshot = _metricsCollector.GetSnapshot();
            return System.Text.Json.JsonSerializer.Serialize(snapshot, new System.Text.Json.JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase
            });
        }

        /// <summary>
        /// Clears all performance metrics.
        /// </summary>
        public void ClearMetrics()
        {
            _metricsCollector.Reset();
        }

        /// <summary>
        /// Records cache statistics.
        /// </summary>
        /// <param name="format">The cache format.</param>
        /// <param name="cacheSize">Current cache size.</param>
        /// <param name="maxCacheSize">Maximum cache size.</param>
        /// <param name="hitRate">Cache hit rate as a percentage.</param>
        /// <param name="evictions">Number of cache evictions.</param>
        public void RecordCacheStatistics(string format, int cacheSize, int maxCacheSize, 
            double hitRate, long evictions)
        {
            _metricsCollector.RecordCacheStatistics(format, cacheSize, maxCacheSize, hitRate, evictions);
        }

        /// <summary>
        /// Records pool statistics.
        /// </summary>
        /// <param name="poolName">The name of the pool.</param>
        /// <param name="poolSize">Current pool size.</param>
        /// <param name="maxPoolSize">Maximum pool size.</param>
        /// <param name="borrowCount">Number of objects borrowed from pool.</param>
        /// <param name="returnCount">Number of objects returned to pool.</param>
        public void RecordPoolStatistics(string poolName, int poolSize, int maxPoolSize, 
            long borrowCount, long returnCount)
        {
            _metricsCollector.RecordPoolStatistics(poolName, poolSize, maxPoolSize, borrowCount, returnCount);
        }

        /// <summary>
        /// Determines whether to sample this operation based on the sampling rate.
        /// </summary>
        /// <returns>True if the operation should be sampled, false otherwise.</returns>
        private bool ShouldSample()
        {
            return _isEnabled && (_samplingRate >= 1.0 || Random.Shared.NextDouble() <= _samplingRate);
        }
    }

    /// <summary>
    /// Static facade for backward compatibility with existing SerializationPerformanceMonitor usage.
    /// This provides a migration path while encouraging use of the modular approach.
    /// </summary>
    public static class SerializationPerformanceMonitorFacade
    {
        private static readonly Lazy<ModularSerializationPerformanceMonitor> _instance = 
            new(() => new ModularSerializationPerformanceMonitor());

        /// <summary>
        /// Gets whether performance monitoring is enabled.
        /// </summary>
        public static bool IsEnabled => _instance.Value.IsEnabled;

        /// <summary>
        /// Gets the current sampling rate.
        /// </summary>
        public static double SamplingRate => _instance.Value.SamplingRate;

        /// <summary>
        /// Records a serialization operation.
        /// </summary>
        /// <param name="typeName">The name of the type being serialized.</param>
        /// <param name="elapsedMilliseconds">The time taken in milliseconds.</param>
        /// <param name="sizeInBytes">The size of the serialized data in bytes.</param>
        /// <param name="isError">Whether the operation resulted in an error.</param>
        public static void RecordSerialization(string typeName, long elapsedMilliseconds, long sizeInBytes, bool isError = false)
        {
            _instance.Value.RecordSerialization(typeName, "Unknown", elapsedMilliseconds, sizeInBytes, isError);
        }

        /// <summary>
        /// Records a deserialization operation.
        /// </summary>
        /// <param name="typeName">The name of the type being deserialized.</param>
        /// <param name="elapsedMilliseconds">The time taken in milliseconds.</param>
        /// <param name="sizeInBytes">The size of the serialized data in bytes.</param>
        /// <param name="isError">Whether the operation resulted in an error.</param>
        public static void RecordDeserialization(string typeName, long elapsedMilliseconds, long sizeInBytes, bool isError = false)
        {
            _instance.Value.RecordDeserialization(typeName, "Unknown", elapsedMilliseconds, sizeInBytes, isError);
        }

        /// <summary>
        /// Gets a performance report for all serialization operations.
        /// </summary>
        /// <returns>A JSON string containing the performance report.</returns>
        public static string GetPerformanceReport()
        {
            return _instance.Value.GetDetailedPerformanceReport();
        }

        /// <summary>
        /// Clears all performance metrics.
        /// </summary>
        public static void ClearMetrics()
        {
            _instance.Value.ClearMetrics();
        }
    }
}
