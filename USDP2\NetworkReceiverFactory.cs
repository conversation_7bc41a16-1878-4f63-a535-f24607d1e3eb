using System;

namespace USDP2
{
    /// <summary>
    /// The network receiver factory.
    /// </summary>
    public static class NetworkReceiverFactory
    {
        /// <summary>
        /// Creates the receiver.
        /// </summary>
        /// <param name="scope">The scope.</param>
        /// <param name="port">The port number to listen on. Must be between 1 and 65535.</param>
        /// <param name="multicastAddress">The multicast address. Must be a valid IP address if provided.</param>
        /// <param name="config">The USDP configuration instance. If null, uses UsdpConfiguration.Instance.</param>
        /// <exception cref="ArgumentOutOfRangeException">Thrown when the scope is invalid or port is outside valid range.</exception>
        /// <exception cref="ArgumentException">Thrown when the multicast address is invalid.</exception>
        /// <returns>An <see cref="INetworkReceiver"/></returns>
        public static INetworkReceiver CreateReceiver(NetworkScope scope, int port, string? multicastAddress = null, UsdpConfiguration? config = null)
        {
            // Validate network parameters before creating receiver
            InputValidator.ValidatePort(port, nameof(port));

            if (!string.IsNullOrEmpty(multicastAddress))
            {
                InputValidator.ValidateIpAddress(multicastAddress, nameof(multicastAddress));
            }

            try
            {
                INetworkReceiver receiver = scope switch
                {
                    NetworkScope.Local => new UdpNetworkReceiver(port, isMulticast: multicastAddress != null, multicastAddress, config),
                    NetworkScope.Global => new HttpNetworkReceiver(port), // or new TcpNetworkReceiver(port)
                    _ => throw new ArgumentOutOfRangeException(nameof(scope), scope, null)
                };
                Diagnostics.Log("NetworkReceiverCreated", new { Scope = scope, Port = port, MulticastAddress = multicastAddress });
                return receiver;
            }
            catch (Exception ex)
            {
                Diagnostics.Log("NetworkReceiverCreationError", new { Scope = scope, Port = port, MulticastAddress = multicastAddress, Error = ex.Message });
                throw;
            }
        }
    }
}