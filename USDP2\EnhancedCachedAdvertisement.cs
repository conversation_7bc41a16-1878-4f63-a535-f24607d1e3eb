using System;

namespace USDP2
{
    /// <summary>
    /// Enhanced cached advertisement with access tracking and memory estimation.
    /// Extends the basic cached advertisement with detailed metrics for cache management.
    /// </summary>
    public sealed class EnhancedCachedAdvertisement
    {
        private int _accessCount;
        private DateTimeOffset _lastAccessedAt;
        private readonly object _lockObject = new object();

        /// <summary>
        /// Gets the service advertisement.
        /// </summary>
        public ServiceAdvertisement Advertisement { get; }

        /// <summary>
        /// Gets the expiration time for this cached entry.
        /// </summary>
        public DateTimeOffset? Expiry { get; }

        /// <summary>
        /// Gets the time when this entry was created.
        /// </summary>
        public DateTimeOffset CreatedAt { get; }

        /// <summary>
        /// Gets the time when this entry was last accessed.
        /// Thread-safe property that can be read without locking.
        /// </summary>
        public DateTimeOffset LastAccessedAt
        {
            get
            {
                lock (_lockObject)
                {
                    return _lastAccessedAt;
                }
            }
        }

        /// <summary>
        /// Gets the number of times this entry has been accessed.
        /// Thread-safe property that can be read without locking.
        /// </summary>
        public int AccessCount
        {
            get
            {
                lock (_lockObject)
                {
                    return _accessCount;
                }
            }
        }

        /// <summary>
        /// Gets the estimated memory usage of this cached entry in bytes.
        /// This includes the advertisement data, metadata, and tracking overhead.
        /// </summary>
        public long EstimatedMemorySize { get; }

        /// <summary>
        /// Gets the age of this cached entry.
        /// </summary>
        public TimeSpan Age => DateTimeOffset.UtcNow - CreatedAt;

        /// <summary>
        /// Gets the time since this entry was last accessed.
        /// </summary>
        public TimeSpan TimeSinceLastAccess => DateTimeOffset.UtcNow - LastAccessedAt;

        /// <summary>
        /// Initializes a new instance of the EnhancedCachedAdvertisement class.
        /// </summary>
        /// <param name="advertisement">The service advertisement to cache.</param>
        /// <param name="expiry">The expiration time for this entry.</param>
        public EnhancedCachedAdvertisement(ServiceAdvertisement advertisement, DateTimeOffset? expiry)
        {
            Advertisement = advertisement ?? throw new ArgumentNullException(nameof(advertisement));
            Expiry = expiry;
            CreatedAt = DateTimeOffset.UtcNow;
            _lastAccessedAt = CreatedAt;
            _accessCount = 1; // Initial creation counts as first access
            EstimatedMemorySize = CalculateEstimatedSize(advertisement);
        }

        /// <summary>
        /// Marks this entry as accessed, updating access count and timestamp.
        /// This method is thread-safe.
        /// </summary>
        public void MarkAccessed()
        {
            lock (_lockObject)
            {
                _accessCount++;
                _lastAccessedAt = DateTimeOffset.UtcNow;
            }
        }

        /// <summary>
        /// Determines if this cached entry has expired.
        /// </summary>
        /// <param name="currentTime">The current time to compare against.</param>
        /// <returns>True if the entry has expired, false otherwise.</returns>
        public bool IsExpired(DateTimeOffset currentTime)
        {
            return Expiry.HasValue && Expiry.Value <= currentTime;
        }

        /// <summary>
        /// Determines if this cached entry has expired using the current UTC time.
        /// </summary>
        /// <returns>True if the entry has expired, false otherwise.</returns>
        public bool IsExpired()
        {
            return IsExpired(DateTimeOffset.UtcNow);
        }

        /// <summary>
        /// Gets access statistics for this cached entry.
        /// This method is thread-safe and returns a snapshot of current values.
        /// </summary>
        /// <returns>A tuple containing access count, last accessed time, and creation time.</returns>
        public (int AccessCount, DateTimeOffset LastAccessedAt, DateTimeOffset CreatedAt) GetAccessStatistics()
        {
            lock (_lockObject)
            {
                return (_accessCount, _lastAccessedAt, CreatedAt);
            }
        }

        /// <summary>
        /// Calculates the estimated memory size of a service advertisement.
        /// This includes the advertisement object, its properties, and metadata.
        /// </summary>
        /// <param name="advertisement">The service advertisement to measure.</param>
        /// <returns>The estimated memory size in bytes.</returns>
        private static long CalculateEstimatedSize(ServiceAdvertisement advertisement)
        {
            long size = 0;

            // Base object overhead (approximate)
            size += 64; // Object header and basic fields

            // ServiceIdentifier size
            if (advertisement.ServiceId != null)
            {
                size += 32; // Object overhead
                size += (advertisement.ServiceId.Namespace?.Length ?? 0) * 2; // UTF-16 encoding
                size += (advertisement.ServiceId.Name?.Length ?? 0) * 2;
                size += 16; // Guid
            }

            // TransportEndpoint size
            if (advertisement.Endpoint != null)
            {
                size += 32; // Object overhead
                size += (advertisement.Endpoint.Protocol?.Length ?? 0) * 2;
                size += (advertisement.Endpoint.Address?.Length ?? 0) * 2;
                size += (advertisement.Endpoint.Security?.Length ?? 0) * 2;
                size += 4; // Port (int)
            }

            // Metadata size
            if (advertisement.Metadata != null)
            {
                size += 32; // Dictionary overhead
                foreach (var kvp in advertisement.Metadata)
                {
                    size += 32; // KeyValuePair overhead
                    size += (kvp.Key?.Length ?? 0) * 2; // Key string
                    
                    // Estimate value size based on type
                    if (kvp.Value != null)
                    {
                        size += kvp.Value switch
                        {
                            string str => str.Length * 2,
                            int => 4,
                            long => 8,
                            double => 8,
                            bool => 1,
                            DateTime => 8,
                            DateTimeOffset => 16,
                            _ => 32 // Default estimate for complex objects
                        };
                    }
                }
            }

            // Signature size
            if (advertisement.Signature != null)
            {
                size += advertisement.Signature.Length;
            }

            // Additional overhead for caching structures
            size += 64; // EnhancedCachedAdvertisement overhead
            size += 16; // DateTimeOffset fields
            size += 4;  // Access count
            size += 8;  // Lock object reference

            return size;
        }

        /// <summary>
        /// Returns a string representation of this cached entry.
        /// </summary>
        /// <returns>A formatted string with key information about the cached entry.</returns>
        public override string ToString()
        {
            var stats = GetAccessStatistics();
            var expired = IsExpired() ? " (EXPIRED)" : "";
            return $"CachedEntry[{Advertisement.ServiceId}]: " +
                   $"Accessed {stats.AccessCount} times, " +
                   $"Last: {stats.LastAccessedAt:HH:mm:ss}, " +
                   $"Size: {EstimatedMemorySize} bytes{expired}";
        }
    }
}
