using System;
using System.Collections.Generic;
using System.Diagnostics;

namespace USDP2.Metrics
{
    /// <summary>
    /// Specialized metrics collector for general performance monitoring.
    /// Tracks operation latencies, throughput, and system performance indicators.
    /// </summary>
    public class PerformanceMetricsCollector : BaseMetricsCollector
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="PerformanceMetricsCollector"/> class.
        /// </summary>
        public PerformanceMetricsCollector() : base("Performance", MetricsCategory.Performance)
        {
        }

        /// <summary>
        /// Records a query operation with latency measurement.
        /// </summary>
        /// <param name="queryType">The type of query performed.</param>
        /// <param name="latency">The query latency.</param>
        /// <param name="resultCount">The number of results returned.</param>
        /// <param name="isSuccess">Whether the query was successful.</param>
        public void RecordQuery(string queryType, TimeSpan latency, int resultCount, bool isSuccess = true)
        {
            var tags = new Dictionary<string, string>
            {
                ["query_type"] = queryType,
                ["result"] = isSuccess ? "success" : "error"
            };

            RecordCounter("queries_total", 1, tags);
            RecordTiming("query_latency", latency, tags);
            
            if (isSuccess)
            {
                RecordHistogram("query_result_count", resultCount, tags);
            }

            // Record latency percentiles for monitoring
            RecordHistogram("query_latency_ms", latency.TotalMilliseconds, tags);
        }

        /// <summary>
        /// Records an operation with timing and success/failure tracking.
        /// </summary>
        /// <param name="operationName">The name of the operation.</param>
        /// <param name="component">The component performing the operation.</param>
        /// <param name="duration">The operation duration.</param>
        /// <param name="isSuccess">Whether the operation was successful.</param>
        /// <param name="errorType">The type of error if operation failed.</param>
        public void RecordOperation(string operationName, string component, TimeSpan duration, 
            bool isSuccess = true, string? errorType = null)
        {
            var tags = new Dictionary<string, string>
            {
                ["operation"] = operationName,
                ["component"] = component,
                ["result"] = isSuccess ? "success" : "error"
            };

            if (!isSuccess && !string.IsNullOrEmpty(errorType))
            {
                tags["error_type"] = errorType;
            }

            RecordCounter("operations_total", 1, tags);
            RecordTiming("operation_duration", duration, tags);

            // Track operations per second
            RecordCounter("operations_per_second", 1, new Dictionary<string, string>
            {
                ["operation"] = operationName,
                ["component"] = component
            });
        }

        /// <summary>
        /// Records throughput metrics for data processing operations.
        /// </summary>
        /// <param name="operationType">The type of operation.</param>
        /// <param name="itemsProcessed">The number of items processed.</param>
        /// <param name="duration">The time taken to process the items.</param>
        /// <param name="bytesProcessed">The number of bytes processed (optional).</param>
        public void RecordThroughput(string operationType, long itemsProcessed, TimeSpan duration, 
            long bytesProcessed = 0)
        {
            var tags = new Dictionary<string, string> { ["operation_type"] = operationType };

            var itemsPerSecond = itemsProcessed / duration.TotalSeconds;
            RecordGauge("items_per_second", itemsPerSecond, tags);
            RecordHistogram("items_processed", itemsProcessed, tags);

            if (bytesProcessed > 0)
            {
                var bytesPerSecond = bytesProcessed / duration.TotalSeconds;
                var mbPerSecond = bytesPerSecond / (1024.0 * 1024.0);
                RecordGauge("bytes_per_second", bytesPerSecond, tags);
                RecordGauge("mb_per_second", mbPerSecond, tags);
            }
        }

        /// <summary>
        /// Records system resource usage metrics.
        /// </summary>
        /// <param name="component">The component being monitored.</param>
        public void RecordSystemMetrics(string component)
        {
            var tags = new Dictionary<string, string> { ["component"] = component };

            try
            {
                // Memory metrics
                var totalMemory = GC.GetTotalMemory(false);
                RecordGauge("memory_usage_bytes", totalMemory, tags);

                // GC metrics
                var gen0Collections = GC.CollectionCount(0);
                var gen1Collections = GC.CollectionCount(1);
                var gen2Collections = GC.CollectionCount(2);

                RecordGauge("gc_gen0_collections", gen0Collections, tags);
                RecordGauge("gc_gen1_collections", gen1Collections, tags);
                RecordGauge("gc_gen2_collections", gen2Collections, tags);

                // Process metrics
                using var process = Process.GetCurrentProcess();
                RecordGauge("process_working_set_bytes", process.WorkingSet64, tags);
                RecordGauge("process_private_memory_bytes", process.PrivateMemorySize64, tags);
                RecordGauge("process_virtual_memory_bytes", process.VirtualMemorySize64, tags);
                RecordGauge("process_thread_count", process.Threads.Count, tags);
                RecordGauge("process_handle_count", process.HandleCount, tags);
            }
            catch (Exception ex)
            {
                // Log error but don't throw - metrics collection should be non-intrusive
                SetError($"Failed to collect system metrics for {component}: {ex.Message}");
            }
        }

        /// <summary>
        /// Records cache performance metrics.
        /// </summary>
        /// <param name="cacheName">The name of the cache.</param>
        /// <param name="hitCount">The number of cache hits.</param>
        /// <param name="missCount">The number of cache misses.</param>
        /// <param name="evictionCount">The number of cache evictions.</param>
        /// <param name="currentSize">The current cache size.</param>
        /// <param name="maxSize">The maximum cache size.</param>
        public void RecordCacheMetrics(string cacheName, long hitCount, long missCount, 
            long evictionCount, int currentSize, int maxSize)
        {
            var tags = new Dictionary<string, string> { ["cache"] = cacheName };

            RecordCounter("cache_hits_total", hitCount, tags);
            RecordCounter("cache_misses_total", missCount, tags);
            RecordCounter("cache_evictions_total", evictionCount, tags);
            RecordGauge("cache_size", currentSize, tags);
            RecordGauge("cache_max_size", maxSize, tags);

            // Calculate derived metrics
            var totalRequests = hitCount + missCount;
            if (totalRequests > 0)
            {
                var hitRate = (double)hitCount / totalRequests * 100;
                RecordGauge("cache_hit_rate_percent", hitRate, tags);
            }

            var utilization = maxSize > 0 ? (double)currentSize / maxSize * 100 : 0;
            RecordGauge("cache_utilization_percent", utilization, tags);
        }

        /// <summary>
        /// Records connection pool metrics.
        /// </summary>
        /// <param name="poolName">The name of the connection pool.</param>
        /// <param name="activeConnections">The number of active connections.</param>
        /// <param name="idleConnections">The number of idle connections.</param>
        /// <param name="maxConnections">The maximum number of connections.</param>
        /// <param name="connectionRequests">The total number of connection requests.</param>
        /// <param name="connectionTimeouts">The number of connection timeouts.</param>
        public void RecordConnectionPoolMetrics(string poolName, int activeConnections, 
            int idleConnections, int maxConnections, long connectionRequests, long connectionTimeouts)
        {
            var tags = new Dictionary<string, string> { ["pool"] = poolName };

            RecordGauge("pool_active_connections", activeConnections, tags);
            RecordGauge("pool_idle_connections", idleConnections, tags);
            RecordGauge("pool_max_connections", maxConnections, tags);
            RecordCounter("pool_connection_requests_total", connectionRequests, tags);
            RecordCounter("pool_connection_timeouts_total", connectionTimeouts, tags);

            // Calculate derived metrics
            var totalConnections = activeConnections + idleConnections;
            var utilization = maxConnections > 0 ? (double)totalConnections / maxConnections * 100 : 0;
            RecordGauge("pool_utilization_percent", utilization, tags);

            if (connectionRequests > 0)
            {
                var timeoutRate = (double)connectionTimeouts / connectionRequests * 100;
                RecordGauge("pool_timeout_rate_percent", timeoutRate, tags);
            }
        }

        /// <summary>
        /// Gets a performance summary with key metrics.
        /// </summary>
        /// <returns>A summary of performance metrics.</returns>
        public PerformanceSummary GetPerformanceSummary()
        {
            var snapshot = GetSnapshot();
            var summary = new PerformanceSummary();

            // Calculate query metrics
            foreach (var timing in snapshot.Timings.Values)
            {
                if (timing.Name == "query_latency" && timing.Count > 0)
                {
                    summary.AverageQueryLatency = timing.AverageDuration;
                    summary.P95QueryLatency = timing.GetPercentileDuration(95);
                    summary.P99QueryLatency = timing.GetPercentileDuration(99);
                }
            }

            // Calculate operation counts
            foreach (var counter in snapshot.Counters.Values)
            {
                if (counter.Name == "queries_total")
                    summary.TotalQueries += counter.Value;
                else if (counter.Name == "operations_total")
                    summary.TotalOperations += counter.Value;
            }

            // Calculate system metrics
            foreach (var gauge in snapshot.Gauges.Values)
            {
                if (gauge.Name == "memory_usage_bytes")
                    summary.CurrentMemoryUsage = (long)gauge.Value;
                else if (gauge.Name == "process_thread_count")
                    summary.ThreadCount = (int)gauge.Value;
            }

            return summary;
        }
    }

    /// <summary>
    /// Summary of performance metrics.
    /// </summary>
    public class PerformanceSummary
    {
        /// <summary>
        /// Gets or sets the total number of queries performed.
        /// </summary>
        public long TotalQueries { get; set; }

        /// <summary>
        /// Gets or sets the total number of operations performed.
        /// </summary>
        public long TotalOperations { get; set; }

        /// <summary>
        /// Gets or sets the average query latency.
        /// </summary>
        public TimeSpan AverageQueryLatency { get; set; }

        /// <summary>
        /// Gets or sets the 95th percentile query latency.
        /// </summary>
        public TimeSpan P95QueryLatency { get; set; }

        /// <summary>
        /// Gets or sets the 99th percentile query latency.
        /// </summary>
        public TimeSpan P99QueryLatency { get; set; }

        /// <summary>
        /// Gets or sets the current memory usage in bytes.
        /// </summary>
        public long CurrentMemoryUsage { get; set; }

        /// <summary>
        /// Gets or sets the current thread count.
        /// </summary>
        public int ThreadCount { get; set; }
    }
}
