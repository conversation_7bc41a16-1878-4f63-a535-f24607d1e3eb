// USDP Pluggable Serializer Note:
// To use a custom serializer, implement the IUSDPSerializer interface and register it at startup:
//
// Example:
// public class MyCustomSerializer : IUSDPSerializer
// {
//     public string SerializeToJson<T>(T obj) where T : class { /* Custom logic */ }
//     public T? DeserializeFromJson<T>(string json) where T : class { /* Custom logic */ }
//     public byte[] SerializeToCbor<T>(T obj) where T : class { /* Custom logic */ }
//     public T? DeserializeFromCbor<T>(byte[] cbor) where T : class { /* Custom logic */ }
// }
//
// Register your serializer (typically at app startup):
// USDPSerializer.SetSerializer(new MyCustomSerializer());
//
// All protocol messages will now use your serializer for ToJson, ToCbor, etc.
// Example usage:
// Serialize for network transmission
// byte[] cbor = serviceAdvertisement.ToCbor();
// Deserialize after receiving  
// var adv = ServiceAdvertisement.FromCbor(cbor);
using USDP2;

namespace USDP2
{
    public interface IUSDPSerializer
    {
        /// <summary>
        /// Serializes an object to a JSON string.
        /// </summary>
        /// <typeparam name="T">The type of the object to serialize.</typeparam>
        /// <param name="obj">The object to serialize.</param>
        /// <returns>A <see cref="SerializationResult{String}"/> containing the JSON string or error information.</returns>
        SerializationResult<string> SerializeToJson<T>(T obj) where T : class;

        /// <summary>
        /// Deserializes an object from a JSON string.
        /// </summary>
        /// <typeparam name="T">The type to deserialize to.</typeparam>
        /// <param name="json">The JSON string to deserialize.</param>
        /// <returns>A <see cref="SerializationResult{T}"/> containing the deserialized object or error information.</returns>
        SerializationResult<T> DeserializeFromJson<T>(string json) where T : class;

        /// <summary>
        /// Serializes an object to CBOR binary format.
        /// </summary>
        /// <typeparam name="T">The type of the object to serialize.</typeparam>
        /// <param name="obj">The object to serialize.</param>
        /// <returns>A <see cref="SerializationResult{T}"/> containing the CBOR binary data or error information.</returns>
        SerializationResult<byte[]> SerializeToCbor<T>(T obj) where T : class;

        /// <summary>
        /// Deserializes an object from CBOR binary data.
        /// </summary>
        /// <typeparam name="T">The type to deserialize to.</typeparam>
        /// <param name="cbor">The CBOR binary data to deserialize.</param>
        /// <returns>A <see cref="SerializationResult{T}"/> containing the deserialized object or error information.</returns>
        SerializationResult<T> DeserializeFromCbor<T>(byte[] cbor) where T : class;

        /// <summary>
        /// Serializes an object to a JSON string using the legacy approach (for backward compatibility).
        /// </summary>
        /// <typeparam name="T">The type of the object to serialize.</typeparam>
        /// <param name="obj">The object to serialize.</param>
        /// <returns>A JSON string representation of the object, or an empty string if serialization fails.</returns>
        string SerializeToJsonLegacy<T>(T obj) where T : class;

        /// <summary>
        /// Deserializes an object from a JSON string using the legacy approach (for backward compatibility).
        /// </summary>
        /// <typeparam name="T">The type to deserialize to.</typeparam>
        /// <param name="json">The JSON string to deserialize.</param>
        /// <returns>The deserialized object, or null if deserialization fails.</returns>
        T? DeserializeFromJsonLegacy<T>(string json) where T : class;

        /// <summary>
        /// Serializes an object to CBOR binary format using the legacy approach (for backward compatibility).
        /// </summary>
        /// <typeparam name="T">The type of the object to serialize.</typeparam>
        /// <param name="obj">The object to serialize.</param>
        /// <returns>A byte array containing the CBOR representation of the object, or an empty array if serialization fails.</returns>
        byte[] SerializeToCborLegacy<T>(T obj) where T : class;

        /// <summary>
        /// Deserializes an object from CBOR binary data using the legacy approach (for backward compatibility).
        /// </summary>
        /// <typeparam name="T">The type to deserialize to.</typeparam>
        /// <param name="cbor">The CBOR binary data to deserialize.</param>
        /// <returns>The deserialized object, or null if deserialization fails.</returns>
        T? DeserializeFromCborLegacy<T>(byte[] cbor) where T : class;
    }

}