using System;
using System.Threading;
using System.Threading.Tasks;

namespace USDP2
{
    /// <summary>
    /// Represents the current state of a circuit breaker.
    /// </summary>
    public enum CircuitBreakerState
    {
        /// <summary>Circuit is closed - operations are allowed to proceed normally.</summary>
        Closed,

        /// <summary>Circuit is open - operations are blocked and fail fast.</summary>
        Open,

        /// <summary>Circuit is half-open - limited operations are allowed to test if the service has recovered.</summary>
        HalfOpen
    }

    /// <summary>
    /// Exception thrown when a circuit breaker is open and operations are blocked.
    /// </summary>
    public class CircuitBreakerOpenException : Exception
    {
        public CircuitBreakerState State { get; }
        public TimeSpan TimeUntilRetry { get; }

        public CircuitBreakerOpenException(CircuitBreakerState state, TimeSpan timeUntilRetry)
            : base($"Circuit breaker is {state}. Time until retry: {timeUntilRetry}")
        {
            State = state;
            TimeUntilRetry = timeUntilRetry;
        }
    }

    /// <summary>
    /// Configuration settings for a circuit breaker.
    /// </summary>
    public class CircuitBreakerOptions
    {
        /// <summary>
        /// Number of consecutive failures required to open the circuit.
        /// Default: 5 failures.
        /// </summary>
        public int FailureThreshold { get; set; } = 5;

        /// <summary>
        /// Time window for counting failures.
        /// Default: 60 seconds.
        /// </summary>
        public TimeSpan FailureTimeWindow { get; set; } = TimeSpan.FromSeconds(60);

        /// <summary>
        /// Duration to keep the circuit open before transitioning to half-open.
        /// Default: 30 seconds.
        /// </summary>
        public TimeSpan OpenTimeout { get; set; } = TimeSpan.FromSeconds(30);

        /// <summary>
        /// Number of successful operations required in half-open state to close the circuit.
        /// Default: 3 successes.
        /// </summary>
        public int SuccessThreshold { get; set; } = 3;

        /// <summary>
        /// Timeout for individual operations.
        /// Default: 10 seconds.
        /// </summary>
        public TimeSpan OperationTimeout { get; set; } = TimeSpan.FromSeconds(10);

        /// <summary>
        /// Function to determine if an exception should be counted as a failure.
        /// Default: All exceptions except OperationCanceledException are failures.
        /// </summary>
        public Func<Exception, bool> ShouldHandleException { get; set; } = ex => ex is not OperationCanceledException;
    }

    /// <summary>
    /// Circuit breaker statistics for monitoring and diagnostics.
    /// </summary>
    public class CircuitBreakerStatistics
    {
        public CircuitBreakerState State { get; set; }
        public int FailureCount { get; set; }
        public int SuccessCount { get; set; }
        public DateTime LastFailureTime { get; set; }
        public DateTime LastSuccessTime { get; set; }
        public DateTime StateChangedTime { get; set; }
        public TimeSpan TimeUntilRetry { get; set; }
        public long TotalOperations { get; set; }
        public long TotalFailures { get; set; }
        public long TotalSuccesses { get; set; }
        public double FailureRate => TotalOperations > 0 ? (double)TotalFailures / TotalOperations : 0;
    }

    /// <summary>
    /// Implements the circuit breaker pattern for network operations.
    ///
    /// The circuit breaker prevents cascading failures by monitoring operation success/failure
    /// rates and temporarily blocking operations when failure thresholds are exceeded.
    ///
    /// <para><strong>States:</strong></para>
    /// <list type="bullet">
    /// <item><description><strong>Closed:</strong> Normal operation, all requests allowed</description></item>
    /// <item><description><strong>Open:</strong> Failure threshold exceeded, requests fail fast</description></item>
    /// <item><description><strong>Half-Open:</strong> Testing recovery, limited requests allowed</description></item>
    /// </list>
    ///
    /// <para><strong>Benefits:</strong></para>
    /// <list type="bullet">
    /// <item><description>Prevents cascading failures in distributed systems</description></item>
    /// <item><description>Provides fast failure response during outages</description></item>
    /// <item><description>Allows automatic recovery testing</description></item>
    /// <item><description>Improves system resilience and user experience</description></item>
    /// </list>
    /// </summary>
    public class CircuitBreaker : ICircuitBreaker
    {
        private readonly CircuitBreakerOptions _options;
        private readonly object _lock = new object();
        private readonly string _name;

        private CircuitBreakerState _state = CircuitBreakerState.Closed;
        private int _failureCount;
        private int _successCount;
        private DateTime _lastFailureTime = DateTime.MinValue;
        private DateTime _lastSuccessTime = DateTime.MinValue;
        private DateTime _stateChangedTime = DateTime.UtcNow;
        private long _totalOperations;
        private long _totalFailures;
        private long _totalSuccesses;

        /// <summary>
        /// Initializes a new instance of the CircuitBreaker class.
        /// </summary>
        /// <param name="options">Configuration options for the circuit breaker.</param>
        /// <param name="name">Optional name of the circuit breaker for logging and identification.</param>
        public CircuitBreaker(CircuitBreakerOptions options, string? name = null)
        {
            _options = options ?? throw new ArgumentNullException(nameof(options));
            _name = name ?? "UnnamedCircuitBreaker";
        }

        /// <summary>
        /// Initializes a new instance of the CircuitBreaker class (legacy constructor).
        /// </summary>
        /// <param name="name">Name of the circuit breaker for logging and identification.</param>
        /// <param name="options">Configuration options for the circuit breaker.</param>
        [Obsolete("Use constructor with CircuitBreakerOptions as first parameter for consistency with factory pattern.")]
        public CircuitBreaker(string name, CircuitBreakerOptions? options = null)
        {
            _name = name ?? throw new ArgumentNullException(nameof(name));
            _options = options ?? new CircuitBreakerOptions();
        }

        /// <summary>
        /// Gets the current state of the circuit breaker.
        /// </summary>
        public CircuitBreakerState State
        {
            get
            {
                lock (_lock)
                {
                    UpdateStateIfNeeded();
                    return _state;
                }
            }
        }

        /// <summary>
        /// Gets comprehensive statistics about the circuit breaker.
        /// </summary>
        public CircuitBreakerStatistics GetStatistics()
        {
            lock (_lock)
            {
                UpdateStateIfNeeded();

                var timeUntilRetry = _state == CircuitBreakerState.Open
                    ? _stateChangedTime.Add(_options.OpenTimeout) - DateTime.UtcNow
                    : TimeSpan.Zero;

                return new CircuitBreakerStatistics
                {
                    State = _state,
                    FailureCount = _failureCount,
                    SuccessCount = _successCount,
                    LastFailureTime = _lastFailureTime,
                    LastSuccessTime = _lastSuccessTime,
                    StateChangedTime = _stateChangedTime,
                    TimeUntilRetry = timeUntilRetry > TimeSpan.Zero ? timeUntilRetry : TimeSpan.Zero,
                    TotalOperations = _totalOperations,
                    TotalFailures = _totalFailures,
                    TotalSuccesses = _totalSuccesses
                };
            }
        }

        /// <summary>
        /// Executes an operation through the circuit breaker with automatic failure handling.
        /// </summary>
        /// <typeparam name="T">The return type of the operation.</typeparam>
        /// <param name="operation">The operation to execute.</param>
        /// <param name="cancellationToken">Cancellation token for the operation.</param>
        /// <returns>The result of the operation.</returns>
        /// <exception cref="CircuitBreakerOpenException">Thrown when the circuit is open.</exception>
        public async Task<T> ExecuteAsync<T>(Func<CancellationToken, Task<T>> operation, CancellationToken cancellationToken = default)
        {
            // Check if operation is allowed
            if (!IsOperationAllowed())
            {
                var stats = GetStatistics();
                UsdpLogger.Log($"CircuitBreaker.{_name}.Blocked", new
                {
                    State = stats.State,
                    TimeUntilRetry = stats.TimeUntilRetry.TotalSeconds,
                    FailureCount = stats.FailureCount
                });

                throw new CircuitBreakerOpenException(stats.State, stats.TimeUntilRetry);
            }

            var operationStart = DateTime.UtcNow;

            try
            {
                // Execute with timeout
                using var timeoutCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
                timeoutCts.CancelAfter(_options.OperationTimeout);

                var result = await operation(timeoutCts.Token);

                // Record success
                RecordSuccess(DateTime.UtcNow - operationStart);
                return result;
            }
            catch (Exception ex)
            {
                // Record failure if it should be handled
                if (_options.ShouldHandleException(ex))
                {
                    RecordFailure(ex, DateTime.UtcNow - operationStart);
                }
                throw;
            }
        }

        /// <summary>
        /// Executes an operation through the circuit breaker (void return).
        /// </summary>
        /// <param name="operation">The operation to execute.</param>
        /// <param name="cancellationToken">Cancellation token for the operation.</param>
        /// <exception cref="CircuitBreakerOpenException">Thrown when the circuit is open.</exception>
        public async Task ExecuteAsync(Func<CancellationToken, Task> operation, CancellationToken cancellationToken = default)
        {
            await ExecuteAsync(async ct =>
            {
                await operation(ct);
                return true; // Dummy return value
            }, cancellationToken);
        }

        /// <summary>
        /// Checks if an operation is currently allowed based on circuit breaker state.
        /// </summary>
        private bool IsOperationAllowed()
        {
            lock (_lock)
            {
                UpdateStateIfNeeded();
                return _state != CircuitBreakerState.Open;
            }
        }

        /// <summary>
        /// Records a successful operation and updates circuit breaker state.
        /// </summary>
        private void RecordSuccess(TimeSpan duration)
        {
            lock (_lock)
            {
                _totalOperations++;
                _totalSuccesses++;
                _lastSuccessTime = DateTime.UtcNow;

                UsdpLogger.Log($"CircuitBreaker.{_name}.Success", new
                {
                    State = _state,
                    Duration = duration.TotalMilliseconds,
                    SuccessCount = _successCount + 1,
                    TotalSuccesses = _totalSuccesses
                });

                if (_state == CircuitBreakerState.HalfOpen)
                {
                    _successCount++;
                    if (_successCount >= _options.SuccessThreshold)
                    {
                        TransitionTo(CircuitBreakerState.Closed);
                    }
                }
                else if (_state == CircuitBreakerState.Closed)
                {
                    // Reset failure count on success in closed state
                    _failureCount = 0;
                }
            }
        }

        /// <summary>
        /// Records a failed operation and updates circuit breaker state.
        /// </summary>
        private void RecordFailure(Exception exception, TimeSpan duration)
        {
            lock (_lock)
            {
                _totalOperations++;
                _totalFailures++;
                _lastFailureTime = DateTime.UtcNow;
                _failureCount++;

                UsdpLogger.Log($"CircuitBreaker.{_name}.Failure", new
                {
                    State = _state,
                    Duration = duration.TotalMilliseconds,
                    FailureCount = _failureCount,
                    TotalFailures = _totalFailures,
                    ExceptionType = exception.GetType().Name,
                    ExceptionMessage = exception.Message
                });

                if (_state == CircuitBreakerState.HalfOpen)
                {
                    // Any failure in half-open state opens the circuit
                    TransitionTo(CircuitBreakerState.Open);
                }
                else if (_state == CircuitBreakerState.Closed && _failureCount >= _options.FailureThreshold)
                {
                    // Too many failures in closed state opens the circuit
                    TransitionTo(CircuitBreakerState.Open);
                }
            }
        }

        /// <summary>
        /// Updates the circuit breaker state based on current conditions.
        /// </summary>
        private void UpdateStateIfNeeded()
        {
            if (_state == CircuitBreakerState.Open)
            {
                var timeSinceOpened = DateTime.UtcNow - _stateChangedTime;
                if (timeSinceOpened >= _options.OpenTimeout)
                {
                    TransitionTo(CircuitBreakerState.HalfOpen);
                }
            }
        }

        /// <summary>
        /// Transitions the circuit breaker to a new state.
        /// </summary>
        private void TransitionTo(CircuitBreakerState newState)
        {
            var oldState = _state;
            _state = newState;
            _stateChangedTime = DateTime.UtcNow;

            // Reset counters based on new state
            switch (newState)
            {
                case CircuitBreakerState.Closed:
                    _failureCount = 0;
                    _successCount = 0;
                    break;
                case CircuitBreakerState.HalfOpen:
                    _successCount = 0;
                    break;
                case CircuitBreakerState.Open:
                    _successCount = 0;
                    break;
            }

            UsdpLogger.Log($"CircuitBreaker.{_name}.StateChanged", new
            {
                OldState = oldState,
                NewState = newState,
                FailureCount = _failureCount,
                TotalFailures = _totalFailures,
                TotalOperations = _totalOperations,
                FailureRate = _totalOperations > 0 ? (double)_totalFailures / _totalOperations : 0
            });
        }
    }
}
