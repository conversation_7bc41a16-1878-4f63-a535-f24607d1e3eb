using System;
using System.Collections.Concurrent;

namespace USDP2
{
    /// <summary>
    /// Default implementation of ICircuitBreakerFactory that provides thread-safe circuit breaker creation and management.
    /// This factory ensures that circuit breakers are properly configured and reused across the application.
    /// </summary>
    public class CircuitBreakerFactory : ICircuitBreakerFactory
    {
        private readonly ConcurrentDictionary<string, ICircuitBreaker> _circuitBreakers;

        /// <summary>
        /// Initializes a new instance of the <see cref="CircuitBreakerFactory"/> class.
        /// </summary>
        public CircuitBreakerFactory()
        {
            _circuitBreakers = new ConcurrentDictionary<string, ICircuitBreaker>();
        }

        /// <inheritdoc />
        public ICircuitBreaker Create(string name, CircuitBreakerOptions options)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new ArgumentException("Circuit breaker name cannot be null or empty.", nameof(name));

            if (options == null)
                throw new ArgumentNullException(nameof(options));

            // Create a new circuit breaker instance
            var circuitBreaker = new CircuitBreaker(options, name);

            // Store it in the dictionary (this will replace any existing one with the same name)
            _circuitBreakers.AddOrUpdate(name, circuitBreaker, (key, existing) => circuitBreaker);

            return circuitBreaker;
        }

        /// <inheritdoc />
        public ICircuitBreaker GetOrCreate(string name, CircuitBreakerOptions options)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new ArgumentException("Circuit breaker name cannot be null or empty.", nameof(name));

            if (options == null)
                throw new ArgumentNullException(nameof(options));

            return _circuitBreakers.GetOrAdd(name, _ => new CircuitBreaker(options, name));
        }

        /// <inheritdoc />
        public bool Exists(string name)
        {
            if (string.IsNullOrWhiteSpace(name))
                return false;

            return _circuitBreakers.ContainsKey(name);
        }

        /// <inheritdoc />
        public bool Remove(string name)
        {
            if (string.IsNullOrWhiteSpace(name))
                return false;

            return _circuitBreakers.TryRemove(name, out _);
        }

        /// <summary>
        /// Gets the current count of managed circuit breakers.
        /// </summary>
        public int Count => _circuitBreakers.Count;

        /// <summary>
        /// Gets all circuit breaker names currently managed by this factory.
        /// </summary>
        /// <returns>An array of circuit breaker names.</returns>
        public string[] GetAllNames()
        {
            return _circuitBreakers.Keys.ToArray();
        }

        /// <summary>
        /// Clears all circuit breakers from the factory.
        /// This method should be used with caution as it will remove all existing circuit breakers.
        /// </summary>
        public void Clear()
        {
            _circuitBreakers.Clear();
        }
    }
}
