using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace USDP2
{
    /// <summary>
    /// Example demonstrating the centralized configuration management system.
    /// 
    /// This example shows how to:
    /// - Use the ConfigurationFactory to create components without constructor parameters
    /// - Load environment-specific configurations
    /// - Validate configuration at startup
    /// - Handle configuration errors gracefully
    /// </summary>
    public static class ConfigurationExample
    {
        /// <summary>
        /// Demonstrates the old way of creating components with manual parameter passing.
        /// This approach is error-prone and requires passing configuration through multiple layers.
        /// </summary>
        public static void OldApproach()
        {
            Console.WriteLine("=== OLD APPROACH (Parameter Passing) ===");

            // Old way: Manual parameter passing through constructors
            var multicastAddress = "***************";
            var multicastPort = 5353;
            var domain = "example.com";
            var token = "my-dns-token";

            // Create network components manually
            var sender = new UdpNetworkSender(UsdpConfiguration.Instance);
            var receiver = new UdpNetworkReceiver(multicastPort, true, multicastAddress, UsdpConfiguration.Instance);

            // Create LocalDirectory with manual parameters
            var localDirectory = new LocalDirectory(sender, receiver, multicastAddress, multicastPort);

            // Create DynamicDnsClient with manual parameters
            var dnsClient = new DynamicDnsClient(domain, token);

            Console.WriteLine($"Created LocalDirectory with {multicastAddress}:{multicastPort}");
            Console.WriteLine($"Created DynamicDnsClient with domain: {domain}");
            Console.WriteLine("Problems: Hard-coded values, parameter passing, no environment support\n");

            // Cleanup
            localDirectory.Dispose();
            dnsClient.Dispose();
        }

        /// <summary>
        /// Demonstrates the new centralized configuration approach.
        /// This approach eliminates parameter passing and supports environment-specific configurations.
        /// </summary>
        public static async Task NewApproachAsync()
        {
            Console.WriteLine("=== NEW APPROACH (Centralized Configuration) ===");

            try
            {
                // Step 1: Load environment-specific configuration
                await LoadEnvironmentConfigurationAsync();

                // Step 2: Validate configuration at startup
                ValidateConfigurationAtStartup();

                // Step 3: Create components using factory methods (no parameters needed!)
                CreateComponentsWithFactory();

                // Step 4: Demonstrate environment-specific behavior
                await DemonstrateEnvironmentSpecificBehaviorAsync();

                Console.WriteLine("✅ New approach completed successfully!\n");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Configuration error: {ex.Message}\n");
            }
        }

        /// <summary>
        /// Loads environment-specific configuration files and environment variables.
        /// </summary>
        private static async Task LoadEnvironmentConfigurationAsync()
        {
            Console.WriteLine("📁 Loading environment-specific configuration...");

            // Detect current environment
            var environment = UsdpConfiguration.Instance.Environment;
            Console.WriteLine($"   Detected environment: {environment}");

            // Load configuration using the environment loader
            var configLoader = new EnvironmentConfigurationLoader(
                baseConfigPath: ".",
                environment: environment);

            await configLoader.LoadConfigurationAsync();

            Console.WriteLine("   ✅ Configuration loaded from:");
            Console.WriteLine("      - Base configuration (usdp_config.json)");
            Console.WriteLine($"      - Environment configuration (usdp_config.{environment.ToString().ToLowerInvariant()}.json)");
            Console.WriteLine("      - Local overrides (usdp_config.local.json)");
            Console.WriteLine("      - Environment variables (USDP_*)");
        }

        /// <summary>
        /// Validates configuration at startup to catch errors early.
        /// </summary>
        private static void ValidateConfigurationAtStartup()
        {
            Console.WriteLine("🔍 Validating configuration...");

            try
            {
                // Validate configuration and get detailed results
                var validationResults = ValidateConfiguration.ValidateAll();

                var errors = validationResults.Where(r => r.IsError).ToList();
                var warnings = validationResults.Where(r => r.Severity == ValidationSeverity.Warning).ToList();

                if (errors.Count > 0)
                {
                    Console.WriteLine($"   ❌ Found {errors.Count} configuration error(s):");
                    foreach (var error in errors)
                    {
                        Console.WriteLine($"      - {error.PropertyName}: {error.Message}");
                    }
                    throw new InvalidOperationException("Configuration validation failed");
                }

                if (warnings.Count > 0)
                {
                    Console.WriteLine($"   ⚠️  Found {warnings.Count} configuration warning(s):");
                    foreach (var warning in warnings)
                    {
                        Console.WriteLine($"      - {warning.PropertyName}: {warning.Message}");
                    }
                }

                Console.WriteLine("   ✅ Configuration validation passed");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ Configuration validation failed: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Creates components using the factory pattern without manual parameter passing.
        /// </summary>
        private static void CreateComponentsWithFactory()
        {
            Console.WriteLine("🏭 Creating components with factory methods...");

            // Create LocalDirectory using factory (no parameters needed!)
            using var localDirectory = ConfigurationFactory.CreateLocalDirectory();
            Console.WriteLine("   ✅ LocalDirectory created with configuration defaults");

            // Create DynamicDnsClient using factory (no parameters needed!)
            // Note: This will use environment variables or configuration defaults
            try
            {
                using var dnsClient = DynamicDnsClient.Create();
                Console.WriteLine("   ✅ DynamicDnsClient created with configuration defaults");
            }
            catch (InvalidOperationException ex)
            {
                Console.WriteLine($"   ⚠️  DynamicDnsClient creation skipped: {ex.Message}");
            }

            // Create network components using factory
            var sender = ConfigurationFactory.CreateNetworkSender(NetworkScope.Local);
            var receiver = ConfigurationFactory.CreateNetworkReceiver(NetworkScope.Local);
            Console.WriteLine("   ✅ Network components created with configuration defaults");

            // Dispose network components if they implement IDisposable
            if (sender is IDisposable disposableSender)
                disposableSender.Dispose();
            if (receiver is IDisposable disposableReceiver)
                disposableReceiver.Dispose();

            // Create service advertisement using factory
            var serviceAd = ConfigurationFactory.CreateServiceAdvertisement();
            Console.WriteLine($"   ✅ ServiceAdvertisement created: {serviceAd.ServiceId.FullName}");
        }

        /// <summary>
        /// Demonstrates how configuration behaves differently in different environments.
        /// </summary>
        private static async Task DemonstrateEnvironmentSpecificBehaviorAsync()
        {
            Console.WriteLine("🌍 Demonstrating environment-specific behavior...");

            var config = UsdpConfiguration.Instance;

            Console.WriteLine($"   Environment: {config.Environment}");
            Console.WriteLine($"   HTTPS Enabled: {config.UseHttps}");
            Console.WriteLine($"   Authentication Required: {config.RequireAuthentication}");
            Console.WriteLine($"   Security Protocol: {config.DefaultSecurity}");
            Console.WriteLine($"   Log Level: {config.MinimumLogLevel}");
            Console.WriteLine($"   Retry Policies: {config.EnableRetryPolicies}");

            // Show how to override for different environments
            Console.WriteLine("\n   🔄 Testing different environment configurations:");

            foreach (var env in Enum.GetValues<UsdpConfiguration.DeploymentEnvironment>())
            {
                // Create a copy of the current configuration and apply environment defaults
                var testConfig = UsdpConfiguration.Instance;
                var originalEnv = testConfig.Environment;
                testConfig.Environment = env;
                testConfig.ApplyEnvironmentDefaults();

                Console.WriteLine($"      {env}: HTTPS={testConfig.UseHttps}, Auth={testConfig.RequireAuthentication}, Security={testConfig.DefaultSecurity}");

                // Restore original environment
                testConfig.Environment = originalEnv;
                testConfig.ApplyEnvironmentDefaults();
            }
        }

        /// <summary>
        /// Demonstrates configuration validation for different environments.
        /// </summary>
        public static void DemonstrateConfigurationValidation()
        {
            Console.WriteLine("=== CONFIGURATION VALIDATION DEMO ===");

            foreach (var environment in Enum.GetValues<UsdpConfiguration.DeploymentEnvironment>())
            {
                Console.WriteLine($"\n🔍 Validating {environment} environment configuration:");

                var issues = ConfigurationFactory.ValidateConfiguration(environment);

                if (issues.Count == 0)
                {
                    Console.WriteLine("   ✅ No issues found");
                }
                else
                {
                    Console.WriteLine($"   ⚠️  Found {issues.Count} issue(s):");
                    foreach (var issue in issues)
                    {
                        Console.WriteLine($"      - {issue}");
                    }
                }
            }
        }

        /// <summary>
        /// Demonstrates creating configuration files for different environments.
        /// </summary>
        public static async Task CreateConfigurationFilesAsync()
        {
            Console.WriteLine("=== CREATING CONFIGURATION FILES ===");

            var outputPath = "./config";

            try
            {
                await EnvironmentConfigurationLoader.CreateEnvironmentConfigurationFilesAsync(outputPath);

                Console.WriteLine($"✅ Configuration files created in: {outputPath}");
                Console.WriteLine("   Files created:");
                Console.WriteLine("   - usdp_config.json (base configuration)");
                Console.WriteLine("   - usdp_config.development.json");
                Console.WriteLine("   - usdp_config.testing.json");
                Console.WriteLine("   - usdp_config.staging.json");
                Console.WriteLine("   - usdp_config.production.json");
                Console.WriteLine("   - usdp_config.local.json.template");
                Console.WriteLine("\n   Copy usdp_config.local.json.template to usdp_config.local.json and customize as needed.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Failed to create configuration files: {ex.Message}");
            }
        }

        /// <summary>
        /// Main demonstration method that shows the complete configuration management system.
        /// </summary>
        public static async Task RunCompleteDemo()
        {
            Console.WriteLine("🚀 USDP2 Configuration Management System Demo");
            Console.WriteLine("=".PadRight(50, '='));

            // Show the old approach
            OldApproach();

            // Show the new approach
            await NewApproachAsync();

            // Demonstrate validation
            DemonstrateConfigurationValidation();

            // Create configuration files
            await CreateConfigurationFilesAsync();

            Console.WriteLine("🎉 Demo completed! The new configuration system provides:");
            Console.WriteLine("   ✅ Centralized configuration management");
            Console.WriteLine("   ✅ Environment-specific configurations");
            Console.WriteLine("   ✅ Elimination of constructor parameter passing");
            Console.WriteLine("   ✅ Comprehensive validation at startup");
            Console.WriteLine("   ✅ Clear error messages for misconfigurations");
            Console.WriteLine("   ✅ Support for configuration files and environment variables");
        }
    }
}
