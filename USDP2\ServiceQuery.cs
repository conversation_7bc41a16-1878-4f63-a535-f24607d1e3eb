using System.Collections.Generic;

namespace USDP2
{
    /// <summary>
    /// Represents a service query message with filter criteria.
    /// </summary>
    public sealed class ServiceQuery
    {
        /// <summary>
        /// Gets or sets the sid filter.
        /// </summary>
        /// <value>A <see cref="string"/></value>
        public string? <PERSON><PERSON><PERSON><PERSON> { get; set; }
        /// <summary>
        /// Gets or sets the metadata filter used to match service advertisements.
        /// </summary>
        /// <value>A dictionary of key-value pairs to match against service metadata.</value>
        public Dictionary<string, string>? MetadataFilter { get; set; }

        // Delegated serialization/deserialization
        /// <summary>
        /// Converts to JSON string representation.
        /// </summary>
        /// <returns>A JSON string representation of this service query.</returns>
        public string ToJson() => USDPSerializer.ToJson(this);

        /// <summary>
        /// Converts to JSON string representation with detailed error information.
        /// </summary>
        /// <returns>A <see cref="SerializationResult{String}"/> containing the JSON string or error information.</returns>
        public SerializationResult<string> ToJsonWithResult() => USDPSerializer.ToJsonWithResult(this);

        /// <summary>
        /// Converts to CBOR binary representation.
        /// </summary>
        /// <returns>A byte array containing the CBOR representation of this service query.</returns>
        public byte[] ToCbor() => USDPSerializer.ToCbor(this);

        /// <summary>
        /// Converts to CBOR binary representation with detailed error information.
        /// </summary>
        /// <returns>A <see cref="SerializationResult{T}"/> containing the CBOR binary data or error information.</returns>
        public SerializationResult<byte[]> ToCborWithResult() => USDPSerializer.ToCborWithResult(this);

        /// <summary>
        /// Deserializes a service query from a JSON string.
        /// </summary>
        /// <param name="json">The JSON string to deserialize.</param>
        /// <returns>A <see cref="ServiceQuery"/> object, or null if deserialization fails.</returns>
        public static ServiceQuery? FromJson(string json) => USDPSerializer.FromJson<ServiceQuery>(json);

        /// <summary>
        /// Deserializes a service query from a JSON string with detailed error information.
        /// </summary>
        /// <param name="json">The JSON string to deserialize.</param>
        /// <returns>A <see cref="SerializationResult{ServiceQuery}"/> containing the deserialized object or error information.</returns>
        public static SerializationResult<ServiceQuery> FromJsonWithResult(string json) =>
            USDPSerializer.FromJsonWithResult<ServiceQuery>(json);

        /// <summary>
        /// Deserializes a service query from CBOR binary data.
        /// </summary>
        /// <param name="cbor">The CBOR binary data to deserialize.</param>
        /// <returns>A <see cref="ServiceQuery"/> object, or null if deserialization fails.</returns>
        public static ServiceQuery? FromCbor(byte[] cbor) => USDPSerializer.FromCbor<ServiceQuery>(cbor);

        /// <summary>
        /// Deserializes a service query from CBOR binary data with detailed error information.
        /// </summary>
        /// <param name="cbor">The CBOR binary data to deserialize.</param>
        /// <returns>A <see cref="SerializationResult{ServiceQuery}"/> containing the deserialized object or error information.</returns>
        public static SerializationResult<ServiceQuery> FromCborWithResult(byte[] cbor) =>
            USDPSerializer.FromCborWithResult<ServiceQuery>(cbor);
    }
}