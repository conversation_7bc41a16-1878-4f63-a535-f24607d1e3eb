using System.Threading;
using System.Threading.Tasks;

// Example usage
// For console applications
//await ExampleIntegration.RunAsync(scope, port, multicastAddress, ReceiverStartMode.Await, cancellationToken);
// For background services
//await ExampleIntegration.RunAsync(scope, port, multicastAddress, ReceiverStartMode.FireAndForget, cancellationToken);
// For GUI or large services
//await ExampleIntegration.RunAsync(scope, port, multicastAddress, ReceiverStartMode.ThreadPool, cancellationToken);

namespace USDP2
{
    /// <summary>
    /// The receiver start modes.
    /// </summary>
    public enum ReceiverStartMode
    {
        /// <summary>
        /// Waits for receiver to start completely.
        /// </summary>
        Await,
        /// <summary>
        /// Starts receiver without waiting for completion.
        /// </summary>
        FireAndForget,
        /// <summary>
        /// Starts receiver on thread pool.
        /// </summary>
        ThreadPool
    }
    public static class ExampleIntegration
    {
        /// <summary>
        /// Runs an example integration that demonstrates USDP service discovery functionality.
        /// Creates network sender/receiver components and starts a directory node with configurable receiver modes.
        /// </summary>
        /// <param name="scope">The network scope (Local or Global) that determines the type of network components to create.</param>
        /// <param name="port">The port number to listen on for incoming network traffic. Must be between 1 and 65535.</param>
        /// <param name="multicastAddress">The multicast address to use for local network scope. Can be null for global scope. Must be a valid IP address if provided.</param>
        /// <param name="cancellationToken">The cancellation token to stop the operation gracefully.</param>
        /// <param name="receiverMode">The mode for starting the receiver (Await, FireAndForget, or ThreadPool).</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        /// <exception cref="ArgumentOutOfRangeException">Thrown when the port is outside the valid range (1-65535).</exception>
        /// <exception cref="ArgumentException">Thrown when the multicast address is invalid or when multicast address is required for local scope but not provided.</exception>
        public static async Task RunAsync(
            NetworkScope scope,
            int port,
            string? multicastAddress = null,
            ReceiverStartMode receiverMode = ReceiverStartMode.Await,
            CancellationToken cancellationToken = default)
        {
            // Validate network parameters before attempting any network operations
            InputValidator.ValidatePort(port, nameof(port));

            // Validate multicast address if provided
            if (!string.IsNullOrEmpty(multicastAddress))
            {
                InputValidator.ValidateIpAddress(multicastAddress, nameof(multicastAddress));

                // Additional validation for multicast address range
                if (System.Net.IPAddress.TryParse(multicastAddress, out var addr))
                {
                    var bytes = addr.GetAddressBytes();
                    if (bytes.Length == 4) // IPv4
                    {
                        // IPv4 multicast range: ********* to ***************
                        if (bytes[0] < 224 || bytes[0] > 239)
                        {
                            throw new ArgumentException(
                                "IPv4 multicast address must be in the range ********* to ***************.",
                                nameof(multicastAddress));
                        }
                    }
                    else if (bytes.Length == 16) // IPv6
                    {
                        // IPv6 multicast addresses start with FF
                        if (bytes[0] != 0xFF)
                        {
                            throw new ArgumentException(
                                "IPv6 multicast address must start with FF.",
                                nameof(multicastAddress));
                        }
                    }
                }
            }

            // Validate that multicast address is provided for local scope when needed
            if (scope == NetworkScope.Local && string.IsNullOrEmpty(multicastAddress))
            {
                // Note: This is a warning rather than an error as some local configurations might work without multicast
                Diagnostics.Log("ExampleIntegration.Warning", new
                {
                    Message = "Local scope typically requires a multicast address for proper service discovery",
                    Scope = scope,
                    Port = port,
                    Recommendation = "Consider providing a multicast address like '***************'"
                });
            }

            var sender = NetworkSenderFactory.CreateSender(scope);
            var receiver = NetworkReceiverFactory.CreateReceiver(scope, port, multicastAddress);

            var directoryNode = new DirectoryNode(sender, receiver);

            switch (receiverMode)
            {
                case ReceiverStartMode.Await:
                    // Await the receive loop so RunAsync doesn't exit immediately
                    await directoryNode.StartAsync(cancellationToken);
                    break;

                case ReceiverStartMode.FireAndForget:
                    // Start receiving in the background (fire-and-forget)
                    _ = directoryNode.StartAsync(cancellationToken); // intentionally not awaited
                    break;

                case ReceiverStartMode.ThreadPool:
                    // Explicitly run on the thread pool (fire-and-forget)
                    _ = Task.Run(() => directoryNode.StartAsync(cancellationToken), cancellationToken);
                    break;
            }
        }
    }
}
