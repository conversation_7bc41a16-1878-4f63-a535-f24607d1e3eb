using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;

namespace USDP2
{
    /// <summary>
    /// Tracks and analyzes security events to detect attack patterns and provide
    /// comprehensive security monitoring for the USDP2 system.
    /// 
    /// This class provides centralized security event tracking with pattern analysis,
    /// threat detection, and automated response capabilities.
    /// </summary>
    public class SecurityEventTracker : IDisposable
    {
        private readonly ConcurrentDictionary<string, EndpointSecurityProfile> _endpointProfiles = new();
        private readonly ConcurrentQueue<SecurityEvent> _recentEvents = new();
        private readonly Timer _analysisTimer;
        private readonly UsdpConfiguration _config;
        private readonly object _analysisLock = new object();
        private bool _disposed = false;

        /// <summary>
        /// Initializes a new instance of the SecurityEventTracker class.
        /// </summary>
        /// <param name="config">The USDP configuration instance. If null, uses the singleton instance.</param>
        public SecurityEventTracker(UsdpConfiguration? config = null)
        {
            _config = config ?? UsdpConfiguration.Instance;

            // Start periodic analysis timer
            _analysisTimer = new Timer(PerformSecurityAnalysis, null,
                TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
        }

        /// <summary>
        /// Records a security event for analysis and monitoring.
        /// </summary>
        /// <param name="eventType">The type of security event.</param>
        /// <param name="remoteAddress">The remote IP address involved.</param>
        /// <param name="remotePort">The remote port number.</param>
        /// <param name="details">Additional details about the event.</param>
        /// <param name="severity">The severity level of the event.</param>
        public void RecordEvent(SecurityEventType eventType, string remoteAddress, int remotePort,
            string details, SecurityEventSeverity severity = SecurityEventSeverity.Medium)
        {
            var securityEvent = new SecurityEvent
            {
                EventType = eventType,
                RemoteAddress = remoteAddress,
                RemotePort = remotePort,
                Details = details,
                Severity = severity,
                Timestamp = DateTime.UtcNow
            };

            _recentEvents.Enqueue(securityEvent);

            // Update endpoint profile
            var endpointKey = $"{remoteAddress}:{remotePort}";
            var profile = _endpointProfiles.GetOrAdd(endpointKey,
                key => new EndpointSecurityProfile(remoteAddress, remotePort));

            profile.RecordEvent(securityEvent);

            // Log the security event
            UsdpLogger.Log("SecurityEvent.Recorded", new
            {
                EventType = eventType.ToString(),
                RemoteAddress = remoteAddress,
                RemotePort = remotePort,
                Details = details,
                Severity = severity.ToString(),
                Timestamp = securityEvent.Timestamp
            });

            // Check for immediate threats that require urgent attention
            if (severity == SecurityEventSeverity.Critical)
            {
                CheckForImmediateThreats(profile);
            }
        }

        /// <summary>
        /// Gets security statistics for monitoring and reporting.
        /// </summary>
        /// <returns>A comprehensive security statistics object.</returns>
        public SecurityStatistics GetSecurityStatistics()
        {
            var now = DateTime.UtcNow;
            var recentEvents = GetRecentEvents(TimeSpan.FromHours(1));

            var stats = new SecurityStatistics
            {
                TotalEndpoints = _endpointProfiles.Count,
                SuspiciousEndpoints = _endpointProfiles.Values.Count(p => p.IsSuspicious),
                BlockedEndpoints = _endpointProfiles.Values.Count(p => p.IsBlocked),
                RecentEventsCount = recentEvents.Count,
                EventsByType = recentEvents.GroupBy(e => e.EventType)
                    .ToDictionary(g => g.Key, g => g.Count()),
                EventsBySeverity = recentEvents.GroupBy(e => e.Severity)
                    .ToDictionary(g => g.Key, g => g.Count()),
                TopSuspiciousEndpoints = _endpointProfiles.Values
                    .Where(p => p.IsSuspicious)
                    .OrderByDescending(p => p.ThreatScore)
                    .Take(10)
                    .Select(p => new SuspiciousEndpointInfo
                    {
                        EndpointKey = $"{p.RemoteAddress}:{p.RemotePort}",
                        ThreatScore = p.ThreatScore,
                        EventCount = p.EventCount,
                        LastActivity = p.LastActivity,
                        IsBlocked = p.IsBlocked
                    })
                    .ToList()
            };

            return stats;
        }

        /// <summary>
        /// Gets recent security events within the specified time window.
        /// </summary>
        /// <param name="timeWindow">The time window to retrieve events for.</param>
        /// <returns>A list of recent security events.</returns>
        public List<SecurityEvent> GetRecentEvents(TimeSpan timeWindow)
        {
            var cutoff = DateTime.UtcNow - timeWindow;
            return _recentEvents.Where(e => e.Timestamp >= cutoff).ToList();
        }

        /// <summary>
        /// Checks if an endpoint is currently blocked due to security concerns.
        /// </summary>
        /// <param name="remoteAddress">The remote IP address.</param>
        /// <param name="remotePort">The remote port number.</param>
        /// <returns>True if the endpoint is blocked, false otherwise.</returns>
        public bool IsEndpointBlocked(string remoteAddress, int remotePort)
        {
            var endpointKey = $"{remoteAddress}:{remotePort}";
            return _endpointProfiles.TryGetValue(endpointKey, out var profile) && profile.IsBlocked;
        }

        /// <summary>
        /// Manually blocks an endpoint for security reasons.
        /// </summary>
        /// <param name="remoteAddress">The remote IP address to block.</param>
        /// <param name="remotePort">The remote port number to block.</param>
        /// <param name="reason">The reason for blocking.</param>
        /// <param name="duration">The duration to block for. If null, blocks indefinitely.</param>
        public void BlockEndpoint(string remoteAddress, int remotePort, string reason, TimeSpan? duration = null)
        {
            var endpointKey = $"{remoteAddress}:{remotePort}";
            var profile = _endpointProfiles.GetOrAdd(endpointKey,
                key => new EndpointSecurityProfile(remoteAddress, remotePort));

            profile.Block(reason, duration);

            UsdpLogger.Log("SecurityEvent.EndpointBlocked", new
            {
                RemoteAddress = remoteAddress,
                RemotePort = remotePort,
                Reason = reason,
                Duration = duration?.TotalMinutes,
                Severity = "Critical"
            });
        }

        /// <summary>
        /// Manually unblocks an endpoint.
        /// </summary>
        /// <param name="remoteAddress">The remote IP address to unblock.</param>
        /// <param name="remotePort">The remote port number to unblock.</param>
        public void UnblockEndpoint(string remoteAddress, int remotePort)
        {
            var endpointKey = $"{remoteAddress}:{remotePort}";
            if (_endpointProfiles.TryGetValue(endpointKey, out var profile))
            {
                profile.Unblock();

                UsdpLogger.Log("SecurityEvent.EndpointUnblocked", new
                {
                    RemoteAddress = remoteAddress,
                    RemotePort = remotePort,
                    Severity = "Info"
                });
            }
        }

        /// <summary>
        /// Performs periodic security analysis to detect patterns and threats.
        /// </summary>
        private void PerformSecurityAnalysis(object? state)
        {
            if (_disposed)
                return;

            lock (_analysisLock)
            {
                if (_disposed)
                    return;

                try
                {
                    // Clean up old events
                    CleanupOldEvents();

                    // Analyze endpoint patterns
                    AnalyzeEndpointPatterns();

                    // Check for coordinated attacks
                    CheckForCoordinatedAttacks();

                    // Update threat scores
                    UpdateThreatScores();
                }
                catch (Exception ex)
                {
                    UsdpLogger.Log("SecurityEventTracker.AnalysisError", new
                    {
                        Error = ex.Message,
                        Severity = "Error"
                    });
                }
            }
        }

        /// <summary>
        /// Checks for immediate threats that require urgent attention.
        /// </summary>
        private void CheckForImmediateThreats(EndpointSecurityProfile profile)
        {
            // Check for rapid-fire attacks
            var recentCriticalEvents = profile.GetRecentEvents(TimeSpan.FromMinutes(1))
                .Count(e => e.Severity == SecurityEventSeverity.Critical);

            if (recentCriticalEvents >= 5)
            {
                profile.Block("Rapid critical security events detected", TimeSpan.FromHours(1));

                UsdpLogger.Log("SecurityEvent.ImmediateThreatDetected", new
                {
                    RemoteAddress = profile.RemoteAddress,
                    RemotePort = profile.RemotePort,
                    CriticalEventsCount = recentCriticalEvents,
                    Action = "Blocked",
                    Severity = "Critical"
                });
            }
        }

        /// <summary>
        /// Cleans up old events to prevent memory leaks.
        /// </summary>
        private void CleanupOldEvents()
        {
            var cutoff = DateTime.UtcNow - TimeSpan.FromHours(24);
            var eventsToRemove = new List<SecurityEvent>();

            // Remove events older than 24 hours
            while (_recentEvents.TryPeek(out var oldestEvent) && oldestEvent.Timestamp < cutoff)
            {
                _recentEvents.TryDequeue(out _);
            }

            // Clean up endpoint profiles
            var expiredProfiles = _endpointProfiles.Values
                .Where(p => DateTime.UtcNow - p.LastActivity > TimeSpan.FromHours(6) && !p.IsBlocked)
                .ToList();

            foreach (var profile in expiredProfiles)
            {
                var key = $"{profile.RemoteAddress}:{profile.RemotePort}";
                _endpointProfiles.TryRemove(key, out _);
            }
        }

        /// <summary>
        /// Analyzes endpoint patterns for suspicious behavior.
        /// </summary>
        private void AnalyzeEndpointPatterns()
        {
            foreach (var profile in _endpointProfiles.Values)
            {
                profile.AnalyzePatterns();
            }
        }

        /// <summary>
        /// Checks for coordinated attacks across multiple endpoints.
        /// </summary>
        private void CheckForCoordinatedAttacks()
        {
            var recentEvents = GetRecentEvents(TimeSpan.FromMinutes(5));
            var eventsByType = recentEvents.GroupBy(e => e.EventType);

            foreach (var group in eventsByType)
            {
                if (group.Count() >= 10) // 10 or more similar events in 5 minutes
                {
                    var uniqueEndpoints = group.Select(e => $"{e.RemoteAddress}:{e.RemotePort}").Distinct().Count();

                    if (uniqueEndpoints >= 3) // From 3 or more different endpoints
                    {
                        UsdpLogger.Log("SecurityEvent.CoordinatedAttackDetected", new
                        {
                            EventType = group.Key.ToString(),
                            EventCount = group.Count(),
                            UniqueEndpoints = uniqueEndpoints,
                            TimeWindow = "5 minutes",
                            Severity = "Critical"
                        });
                    }
                }
            }
        }

        /// <summary>
        /// Updates threat scores for all endpoints.
        /// </summary>
        private void UpdateThreatScores()
        {
            foreach (var profile in _endpointProfiles.Values)
            {
                profile.UpdateThreatScore();
            }
        }

        /// <summary>
        /// Disposes the security event tracker and releases resources.
        /// </summary>
        public void Dispose()
        {
            if (_disposed)
                return;

            _disposed = true;
            _analysisTimer?.Dispose();
            _endpointProfiles.Clear();
        }
    }

    /// <summary>
    /// Represents a security profile for a specific endpoint, tracking behavior patterns
    /// and threat indicators over time.
    /// </summary>
    internal class EndpointSecurityProfile
    {
        private readonly List<SecurityEvent> _events = new();
        private readonly object _lock = new object();

        public string RemoteAddress { get; }
        public int RemotePort { get; }
        public DateTime LastActivity { get; private set; } = DateTime.UtcNow;
        public int EventCount => _events.Count;
        public double ThreatScore { get; private set; }
        public bool IsSuspicious => ThreatScore > 50;
        public bool IsBlocked { get; private set; }
        public DateTime? BlockedUntil { get; private set; }
        public string? BlockReason { get; private set; }

        public EndpointSecurityProfile(string remoteAddress, int remotePort)
        {
            RemoteAddress = remoteAddress;
            RemotePort = remotePort;
        }

        public void RecordEvent(SecurityEvent securityEvent)
        {
            lock (_lock)
            {
                _events.Add(securityEvent);
                LastActivity = securityEvent.Timestamp;

                // Remove old events to prevent memory leaks
                var cutoff = DateTime.UtcNow - TimeSpan.FromHours(6);
                _events.RemoveAll(e => e.Timestamp < cutoff);
            }
        }

        public List<SecurityEvent> GetRecentEvents(TimeSpan timeWindow)
        {
            lock (_lock)
            {
                var cutoff = DateTime.UtcNow - timeWindow;
                return _events.Where(e => e.Timestamp >= cutoff).ToList();
            }
        }

        public void Block(string reason, TimeSpan? duration = null)
        {
            lock (_lock)
            {
                IsBlocked = true;
                BlockReason = reason;
                BlockedUntil = duration.HasValue ? DateTime.UtcNow.Add(duration.Value) : null;
            }
        }

        public void Unblock()
        {
            lock (_lock)
            {
                IsBlocked = false;
                BlockReason = null;
                BlockedUntil = null;
            }
        }

        public void AnalyzePatterns()
        {
            lock (_lock)
            {
                // Check if block has expired
                if (IsBlocked && BlockedUntil.HasValue && DateTime.UtcNow > BlockedUntil.Value)
                {
                    Unblock();
                }

                // Analyze recent events for patterns
                var recentEvents = GetRecentEvents(TimeSpan.FromHours(1));

                // Check for rapid event generation
                if (recentEvents.Count > 50)
                {
                    ThreatScore = Math.Min(100, ThreatScore + 20);
                }

                // Check for high severity events
                var criticalEvents = recentEvents.Count(e => e.Severity == SecurityEventSeverity.Critical);
                if (criticalEvents > 3)
                {
                    ThreatScore = Math.Min(100, ThreatScore + 30);
                }

                // Check for diverse attack types
                var uniqueEventTypes = recentEvents.Select(e => e.EventType).Distinct().Count();
                if (uniqueEventTypes > 5)
                {
                    ThreatScore = Math.Min(100, ThreatScore + 15);
                }
            }
        }

        public void UpdateThreatScore()
        {
            lock (_lock)
            {
                // Gradually decay threat score over time if no recent activity
                var timeSinceLastActivity = DateTime.UtcNow - LastActivity;
                if (timeSinceLastActivity > TimeSpan.FromHours(1))
                {
                    var decayFactor = Math.Min(1.0, timeSinceLastActivity.TotalHours / 24.0);
                    ThreatScore = Math.Max(0, ThreatScore * (1.0 - decayFactor * 0.1));
                }
            }
        }
    }
}
