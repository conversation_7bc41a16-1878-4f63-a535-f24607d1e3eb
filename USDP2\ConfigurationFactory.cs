using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace USDP2
{
    /// <summary>
    /// Factory class for creating and configuring USDP2 components with centralized configuration management.
    /// 
    /// This factory eliminates the need for passing configuration parameters through constructors
    /// by providing pre-configured instances that automatically use the centralized UsdpConfiguration.
    /// It supports environment-specific configurations and provides a clean API for component creation.
    /// 
    /// Benefits:
    /// - Eliminates parameter passing through multiple constructor layers
    /// - Centralizes all configuration in UsdpConfiguration
    /// - Supports environment-specific defaults
    /// - Provides consistent component initialization
    /// - Simplifies testing with configuration overrides
    /// </summary>
    public static class ConfigurationFactory
    {
        #region Network Component Creation

        /// <summary>
        /// Creates a LocalDirectory instance with configuration from UsdpConfiguration.
        /// Eliminates the need to pass multicastAddress and multicastPort parameters.
        /// Automatically applies security level-appropriate settings.
        /// </summary>
        /// <param name="config">Optional configuration override. If null, uses UsdpConfiguration.Instance.</param>
        /// <returns>A configured LocalDirectory instance.</returns>
        public static LocalDirectory CreateLocalDirectory(UsdpConfiguration? config = null)
        {
            var configuration = config ?? UsdpConfiguration.Instance;

            // Apply security level configuration
            ApplySecurityLevelConfiguration(configuration);

            var sender = CreateNetworkSender(NetworkScope.Local, configuration);
            var receiver = CreateNetworkReceiver(NetworkScope.Local, configuration);

            return new LocalDirectory(
                sender,
                receiver,
                configuration.DefaultMulticastAddress,
                configuration.DefaultMulticastPort);
        }

        /// <summary>
        /// Creates a LocalDirectory instance with a specific security level.
        /// This method allows overriding the default security level for specific use cases.
        /// </summary>
        /// <param name="securityLevel">The security level to apply.</param>
        /// <param name="config">Optional configuration override. If null, uses UsdpConfiguration.Instance.</param>
        /// <returns>A configured LocalDirectory instance with the specified security level.</returns>
        public static LocalDirectory CreateLocalDirectoryWithSecurity(
            UsdpConfiguration.SecurityLevel securityLevel,
            UsdpConfiguration? config = null)
        {
            var configuration = config ?? UsdpConfiguration.Instance;

            // Override security level
            configuration.Security = securityLevel;
            configuration.ApplySecurityLevelDefaults();

            return CreateLocalDirectory(configuration);
        }

        /// <summary>
        /// Creates a DynamicDnsClient instance with configuration from UsdpConfiguration.
        /// Eliminates the need to pass domain and token parameters.
        /// Automatically applies security level-appropriate settings.
        /// </summary>
        /// <param name="domain">DNS domain to update. If null, uses configuration default.</param>
        /// <param name="token">Authentication token. If null, attempts to load from secure configuration.</param>
        /// <param name="config">Optional configuration override. If null, uses UsdpConfiguration.Instance.</param>
        /// <returns>A configured DynamicDnsClient instance.</returns>
        public static DynamicDnsClient CreateDynamicDnsClient(
            string? domain = null,
            string? token = null,
            UsdpConfiguration? config = null)
        {
            var configuration = config ?? UsdpConfiguration.Instance;

            // Apply security level configuration
            ApplySecurityLevelConfiguration(configuration);

            // Use provided values or fall back to configuration defaults
            var effectiveDomain = domain ?? configuration.DefaultDnsDomain ?? "example.com";
            var effectiveToken = token ?? LoadDnsTokenFromConfiguration(configuration);

            return new DynamicDnsClient(effectiveDomain, effectiveToken);
        }

        /// <summary>
        /// Creates a DynamicDnsClient instance with a specific security level.
        /// </summary>
        /// <param name="securityLevel">The security level to apply.</param>
        /// <param name="domain">DNS domain to update.</param>
        /// <param name="token">Authentication token.</param>
        /// <param name="config">Optional configuration override.</param>
        /// <returns>A configured DynamicDnsClient instance with the specified security level.</returns>
        public static DynamicDnsClient CreateDynamicDnsClientWithSecurity(
            UsdpConfiguration.SecurityLevel securityLevel,
            string? domain = null,
            string? token = null,
            UsdpConfiguration? config = null)
        {
            var configuration = config ?? UsdpConfiguration.Instance;

            // Override security level
            configuration.Security = securityLevel;
            configuration.ApplySecurityLevelDefaults();

            return CreateDynamicDnsClient(domain, token, configuration);
        }

        /// <summary>
        /// Creates a network sender based on scope and configuration.
        /// Automatically applies security level-appropriate settings.
        /// </summary>
        /// <param name="scope">Network scope (Local or Global).</param>
        /// <param name="config">Optional configuration override.</param>
        /// <returns>A configured network sender.</returns>
        public static INetworkSender CreateNetworkSender(NetworkScope scope, UsdpConfiguration? config = null)
        {
            var configuration = config ?? UsdpConfiguration.Instance;

            // Apply security level configuration
            ApplySecurityLevelConfiguration(configuration);

            return NetworkSenderFactory.CreateSender(scope, configuration);
        }

        /// <summary>
        /// Creates a network sender with a specific security level.
        /// </summary>
        /// <param name="scope">Network scope (Local or Global).</param>
        /// <param name="securityLevel">The security level to apply.</param>
        /// <param name="config">Optional configuration override.</param>
        /// <returns>A configured network sender with the specified security level.</returns>
        public static INetworkSender CreateNetworkSenderWithSecurity(
            NetworkScope scope,
            UsdpConfiguration.SecurityLevel securityLevel,
            UsdpConfiguration? config = null)
        {
            var configuration = config ?? UsdpConfiguration.Instance;

            // Override security level
            configuration.Security = securityLevel;
            configuration.ApplySecurityLevelDefaults();

            return CreateNetworkSender(scope, configuration);
        }

        /// <summary>
        /// Creates a network receiver based on scope and configuration.
        /// Automatically applies security level-appropriate settings.
        /// </summary>
        /// <param name="scope">Network scope (Local or Global).</param>
        /// <param name="config">Optional configuration override.</param>
        /// <returns>A configured network receiver.</returns>
        public static INetworkReceiver CreateNetworkReceiver(NetworkScope scope, UsdpConfiguration? config = null)
        {
            var configuration = config ?? UsdpConfiguration.Instance;

            // Apply security level configuration
            ApplySecurityLevelConfiguration(configuration);

            return scope switch
            {
                NetworkScope.Local => new UdpNetworkReceiver(
                    configuration.DefaultMulticastPort,
                    isMulticast: true,
                    configuration.DefaultMulticastAddress,
                    configuration),
                NetworkScope.Global => new HttpNetworkReceiver(
                    configuration.UseHttps ? configuration.DefaultHttpsPort : configuration.DefaultHttpPort),
                _ => throw new ArgumentOutOfRangeException(nameof(scope), scope, null)
            };
        }

        #endregion

        #region Configuration Loading and Management

        /// <summary>
        /// Loads configuration from a file and applies it to the global UsdpConfiguration instance.
        /// </summary>
        /// <param name="configFilePath">Path to the configuration file. If null, uses default path.</param>
        /// <param name="environment">Target environment for configuration. If null, auto-detects.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        public static async Task LoadConfigurationAsync(
            string? configFilePath = null,
            UsdpConfiguration.DeploymentEnvironment? environment = null)
        {
            var config = UsdpConfiguration.Instance;

            // Set environment if specified
            if (environment.HasValue)
            {
                config.Environment = environment.Value;
            }

            // Apply environment defaults first
            config.ApplyEnvironmentDefaults();

            // Load from file if specified
            if (!string.IsNullOrEmpty(configFilePath) && File.Exists(configFilePath))
            {
                var configProvider = new ConfigurationProvider(configFilePath);
                await configProvider.LoadAsync();
                await configProvider.ApplyToUsdpConfigurationAsync();
            }
        }

        /// <summary>
        /// Creates a configuration provider for advanced configuration scenarios.
        /// </summary>
        /// <param name="configPath">Path to configuration file.</param>
        /// <param name="keyBackend">Key management backend for secure values.</param>
        /// <returns>A configured ConfigurationProvider instance.</returns>
        public static ConfigurationProvider CreateConfigurationProvider(
            string? configPath = null,
            KeyManagementBackend keyBackend = KeyManagementBackend.WindowsDPAPI)
        {
            var effectivePath = configPath ?? UsdpConfiguration.Instance.DefaultConfigFileName;
            return new ConfigurationProvider(effectivePath, keyBackend);
        }

        #endregion

        #region Service Creation

        /// <summary>
        /// Creates a ServiceAdvertisement with default values from configuration.
        /// </summary>
        /// <param name="serviceType">Service type. If null, uses configuration default.</param>
        /// <param name="serviceInstance">Service instance. If null, uses configuration default.</param>
        /// <param name="config">Optional configuration override.</param>
        /// <returns>A configured ServiceAdvertisement instance.</returns>
        public static ServiceAdvertisement CreateServiceAdvertisement(
            string? serviceType = null,
            string? serviceInstance = null,
            UsdpConfiguration? config = null)
        {
            var configuration = config ?? UsdpConfiguration.Instance;

            var serviceId = new ServiceIdentifier(
                serviceType ?? configuration.DefaultServiceType,
                serviceInstance ?? configuration.DefaultServiceInstance);

            var endpoint = new TransportEndpoint
            {
                Protocol = configuration.DefaultProtocol,
                Address = configuration.DefaultServiceAddress,
                Port = configuration.DefaultServicePort,
                Security = configuration.DefaultSecurity
            };

            return new ServiceAdvertisement
            {
                ServiceId = serviceId,
                Endpoint = endpoint,
                Ttl = configuration.DefaultTtl ?? TimeSpan.FromMinutes(5),
                Metadata = new Dictionary<string, object>
                {
                    ["location"] = configuration.DefaultMetadataLocation,
                    ["environment"] = configuration.Environment.ToString().ToLowerInvariant()
                }
            };
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Applies security level configuration to the provided configuration instance.
        /// This method ensures that security settings are properly applied to components.
        /// </summary>
        /// <param name="config">The configuration instance to apply security settings to.</param>
        private static void ApplySecurityLevelConfiguration(UsdpConfiguration config)
        {
            // Apply security level defaults if not already applied
            config.ApplySecurityLevelDefaults();

            // Apply security level to components (PLACEHOLDER: requires component integration)
            SecurityConfigurationManager.ApplySecurityLevelToComponents(config.Security, config);
        }

        /// <summary>
        /// Attempts to load DNS token from secure configuration.
        /// </summary>
        private static string LoadDnsTokenFromConfiguration(UsdpConfiguration config)
        {
            try
            {
                // Try to load from environment variable first
                var envToken = Environment.GetEnvironmentVariable("USDP_DNS_TOKEN");
                if (!string.IsNullOrEmpty(envToken))
                {
                    return envToken;
                }

                // Fall back to a default or empty string
                return "default-token-change-me";
            }
            catch
            {
                return "default-token-change-me";
            }
        }

        /// <summary>
        /// Validates that the current configuration is suitable for the target environment.
        /// </summary>
        /// <param name="environment">Target environment to validate against.</param>
        /// <param name="config">Configuration to validate. If null, uses global instance.</param>
        /// <returns>List of validation warnings or errors.</returns>
        public static List<string> ValidateConfiguration(
            UsdpConfiguration.DeploymentEnvironment environment,
            UsdpConfiguration? config = null)
        {
            var configuration = config ?? UsdpConfiguration.Instance;
            var issues = new List<string>();

            switch (environment)
            {
                case UsdpConfiguration.DeploymentEnvironment.Production:
                    if (!configuration.RequireAuthentication)
                        issues.Add("Authentication should be required in production");
                    if (configuration.DefaultSecurity == "none")
                        issues.Add("Security should not be 'none' in production");
                    if (!configuration.UseHttps)
                        issues.Add("HTTPS should be enabled in production");
                    if (configuration.MinimumLogLevel < LogLevel.Information)
                        issues.Add("Log level should be Information or higher in production");
                    break;

                case UsdpConfiguration.DeploymentEnvironment.Staging:
                    if (!configuration.UseHttps)
                        issues.Add("HTTPS should be enabled in staging");
                    break;

                case UsdpConfiguration.DeploymentEnvironment.Development:
                    // Development environment is more permissive
                    break;
            }

            return issues;
        }

        /// <summary>
        /// Creates a complete USDP2 system configuration with a specific security level.
        /// This method provides a one-stop factory for creating all components with
        /// consistent security settings.
        /// </summary>
        /// <param name="securityLevel">The security level to apply to all components.</param>
        /// <param name="config">Optional base configuration to override.</param>
        /// <returns>A tuple containing all major USDP2 components configured with the specified security level.</returns>
        public static (LocalDirectory LocalDirectory, DynamicDnsClient? DnsClient, INetworkSender LocalSender, INetworkSender GlobalSender)
            CreateSecureUsdpSystem(UsdpConfiguration.SecurityLevel securityLevel, UsdpConfiguration? config = null)
        {
            var configuration = config ?? UsdpConfiguration.Instance;

            // Apply security level
            configuration.Security = securityLevel;
            configuration.ApplySecurityLevelDefaults();

            // Create components with consistent security settings
            var localDirectory = CreateLocalDirectoryWithSecurity(securityLevel, configuration);
            var localSender = CreateNetworkSenderWithSecurity(NetworkScope.Local, securityLevel, configuration);
            var globalSender = CreateNetworkSenderWithSecurity(NetworkScope.Global, securityLevel, configuration);

            DynamicDnsClient? dnsClient = null;
            try
            {
                dnsClient = CreateDynamicDnsClientWithSecurity(securityLevel, config: configuration);
            }
            catch (InvalidOperationException)
            {
                // DNS client creation failed (likely missing configuration) - this is acceptable
                UsdpLogger.Log("ConfigurationFactory.CreateSecureUsdpSystem", new
                {
                    Message = "DynamicDnsClient creation skipped due to missing configuration",
                    SecurityLevel = securityLevel.ToString()
                });
            }

            return (localDirectory, dnsClient, localSender, globalSender);
        }

        /// <summary>
        /// Gets security configuration recommendations for a specific security level.
        /// </summary>
        /// <param name="securityLevel">The security level to get recommendations for.</param>
        /// <returns>A dictionary containing security recommendations and requirements.</returns>
        public static Dictionary<string, object> GetSecurityLevelRecommendations(UsdpConfiguration.SecurityLevel securityLevel)
        {
            return SecurityConfigurationManager.GetSecurityRecommendations(securityLevel);
        }

        /// <summary>
        /// Validates that the current configuration meets the requirements for a specific security level.
        /// </summary>
        /// <param name="securityLevel">The target security level to validate against.</param>
        /// <param name="config">Optional configuration to validate. If null, uses UsdpConfiguration.Instance.</param>
        /// <returns>A list of validation issues or an empty list if the configuration is valid.</returns>
        public static List<string> ValidateSecurityLevelConfiguration(
            UsdpConfiguration.SecurityLevel securityLevel,
            UsdpConfiguration? config = null)
        {
            var configuration = config ?? UsdpConfiguration.Instance;
            return SecurityConfigurationManager.ValidateSecurityConfiguration(securityLevel, configuration);
        }

        #endregion
    }
}
