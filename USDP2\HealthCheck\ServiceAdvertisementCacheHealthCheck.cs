using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace USDP2.HealthCheck
{
    /// <summary>
    /// Health check for ServiceAdvertisementCache that monitors cache hit rates,
    /// memory usage, and overall cache performance.
    /// </summary>
    public class ServiceAdvertisementCacheHealthCheck : HealthCheckBase
    {
        private readonly ServiceAdvertisementCache _cache;
        private readonly HealthCheckOptions _options;

        /// <summary>
        /// Initializes a new instance of the <see cref="ServiceAdvertisementCacheHealthCheck"/> class.
        /// </summary>
        /// <param name="cache">The ServiceAdvertisementCache to monitor.</param>
        /// <param name="options">The health check options.</param>
        public ServiceAdvertisementCacheHealthCheck(ServiceAdvertisementCache cache, HealthCheckOptions? options = null)
            : base("ServiceAdvertisementCache", "Monitors cache hit rates, memory usage, and performance", options?.Timeout)
        {
            _cache = cache ?? throw new ArgumentNullException(nameof(cache));
            _options = options ?? new HealthCheckOptions();
        }

        /// <summary>
        /// Performs the ServiceAdvertisementCache health check.
        /// </summary>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>A <see cref="Task{HealthCheckResult}"/> representing the health check result.</returns>
        protected override async Task<HealthCheckResult> PerformHealthCheckAsync(CancellationToken cancellationToken)
        {
            var stopwatch = Stopwatch.StartNew();
            var healthData = new Dictionary<string, object>();
            var issues = new List<string>();

            try
            {
                // Check 1: Test cache basic functionality
                await CheckCacheBasicFunctionalityAsync(healthData, issues, cancellationToken).ConfigureAwait(false);

                // Check 2: Monitor cache performance
                await CheckCachePerformanceAsync(healthData, issues, cancellationToken).ConfigureAwait(false);

                // Check 3: Test cache hit rates
                await CheckCacheHitRatesAsync(healthData, issues, cancellationToken).ConfigureAwait(false);

                // Check 4: Monitor memory usage
                await CheckMemoryUsageAsync(healthData, issues, cancellationToken).ConfigureAwait(false);

                // Check 5: Test cache expiration and cleanup
                await CheckCacheExpirationAsync(healthData, issues, cancellationToken).ConfigureAwait(false);

                // Check 6: Get enhanced cache statistics
                await CheckCacheStatisticsAsync(healthData, issues, cancellationToken).ConfigureAwait(false);

                stopwatch.Stop();

                // Determine overall health status
                var status = DetermineHealthStatus(issues, healthData);
                var description = CreateHealthDescription(status, issues);

                var result = new HealthCheckResult(status, description, stopwatch.Elapsed, null, healthData);

                if (_options.LogResults)
                {
                    LogResult(result);
                }

                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                healthData["exception"] = ex.Message;
                healthData["stackTrace"] = ex.StackTrace ?? string.Empty;

                var result = HealthCheckResult.Unhealthy($"ServiceAdvertisementCache health check failed: {ex.Message}", stopwatch.Elapsed, ex, healthData);

                if (_options.LogResults)
                {
                    LogResult(result);
                }

                return result;
            }
        }

        /// <summary>
        /// Checks basic cache functionality.
        /// </summary>
        private async Task CheckCacheBasicFunctionalityAsync(Dictionary<string, object> healthData, List<string> issues, CancellationToken cancellationToken)
        {
            try
            {
                // Test adding and retrieving services
                var testServiceId = new ServiceIdentifier("health-check", "cache-test");
                var testEndpoint = new TransportEndpoint
                {
                    Address = "127.0.0.1",
                    Port = 8080,
                    Protocol = "http"
                };
                var testAdvertisement = new ServiceAdvertisement(testServiceId, testEndpoint);

                // Add to cache
                _cache.AddOrUpdate(testAdvertisement);

                // Try to retrieve
                var retrievedServices = _cache.GetActiveAdvertisements();
                var cacheWorking = retrievedServices.Any(s => s.ServiceId.ToString() == testServiceId.ToString());

                healthData["cacheBasicFunctionalityWorking"] = cacheWorking;

                if (!cacheWorking)
                {
                    issues.Add("Cache basic functionality test failed - unable to store and retrieve services");
                }

                // Test heartbeat functionality
                _cache.ReceiveHeartbeat(testServiceId);
                healthData["heartbeatFunctionalityWorking"] = true;

                await Task.Delay(1, cancellationToken).ConfigureAwait(false); // Minimal async operation
            }
            catch (Exception ex)
            {
                healthData["cacheBasicFunctionalityError"] = ex.Message;
                issues.Add($"Cache basic functionality check failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Checks cache performance metrics.
        /// </summary>
        private async Task CheckCachePerformanceAsync(Dictionary<string, object> healthData, List<string> issues, CancellationToken cancellationToken)
        {
            var stopwatch = Stopwatch.StartNew();
            const int testOperations = 100;
            var successfulOperations = 0;

            try
            {
                // Test cache performance with multiple operations
                for (int i = 0; i < testOperations; i++)
                {
                    try
                    {
                        var serviceId = new ServiceIdentifier("health-check", $"perf-test-{i}");
                        var endpoint = new TransportEndpoint
                        {
                            Address = $"192.168.1.{100 + (i % 155)}",
                            Port = 8000 + i,
                            Protocol = "tcp"
                        };
                        var advertisement = new ServiceAdvertisement(serviceId, endpoint);

                        // Add to cache
                        _cache.AddOrUpdate(advertisement);

                        // Retrieve from cache
                        var services = _cache.GetActiveAdvertisements().Where(s => s.ServiceId.ToString() == serviceId.ToString());

                        if (services.Any())
                            successfulOperations++;
                    }
                    catch
                    {
                        // Continue with other operations
                    }

                    if (cancellationToken.IsCancellationRequested)
                        break;
                }

                stopwatch.Stop();

                var operationsPerSecond = testOperations / stopwatch.Elapsed.TotalSeconds;
                var successRate = (double)successfulOperations / testOperations * 100;

                healthData["cachePerformanceTestOperations"] = testOperations;
                healthData["cachePerformanceSuccessfulOperations"] = successfulOperations;
                healthData["cachePerformanceDuration"] = stopwatch.ElapsedMilliseconds;
                healthData["cachePerformanceOperationsPerSecond"] = operationsPerSecond;
                healthData["cachePerformanceSuccessRate"] = successRate;

                if (operationsPerSecond < 1000) // Less than 1000 operations per second
                {
                    issues.Add($"Cache performance is slow: {operationsPerSecond:F0} operations/second");
                }

                if (successRate < 95) // Less than 95% success rate
                {
                    issues.Add($"Cache performance has high failure rate: {successRate:F1}% success rate");
                }
            }
            catch (Exception ex)
            {
                healthData["cachePerformanceError"] = ex.Message;
                issues.Add($"Cache performance check failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Checks cache hit rates by testing repeated access patterns.
        /// </summary>
        private async Task CheckCacheHitRatesAsync(Dictionary<string, object> healthData, List<string> issues, CancellationToken cancellationToken)
        {
            try
            {
                // Create test services
                var testServices = new List<ServiceAdvertisement>();
                for (int i = 0; i < 10; i++)
                {
                    var serviceId = new ServiceIdentifier("health-check", $"hit-rate-test-{i}");
                    var endpoint = new TransportEndpoint
                    {
                        Address = $"10.0.0.{i + 1}",
                        Port = 9000 + i,
                        Protocol = "udp"
                    };
                    var advertisement = new ServiceAdvertisement(serviceId, endpoint);
                    testServices.Add(advertisement);
                    _cache.AddOrUpdate(advertisement);
                }

                // Test repeated access
                var totalAccesses = 0;
                var successfulAccesses = 0;
                var stopwatch = Stopwatch.StartNew();

                for (int round = 0; round < 5; round++)
                {
                    foreach (var service in testServices)
                    {
                        totalAccesses++;
                        var retrieved = _cache.GetActiveAdvertisements().Where(s => s.ServiceId.ToString() == service.ServiceId.ToString());
                        if (retrieved.Any())
                            successfulAccesses++;

                        if (cancellationToken.IsCancellationRequested)
                            break;
                    }

                    if (cancellationToken.IsCancellationRequested)
                        break;
                }

                stopwatch.Stop();

                var hitRate = totalAccesses > 0 ? (double)successfulAccesses / totalAccesses * 100 : 0;

                healthData["cacheHitRateTestAccesses"] = totalAccesses;
                healthData["cacheHitRateSuccessfulAccesses"] = successfulAccesses;
                healthData["cacheHitRate"] = hitRate;
                healthData["cacheHitRateTestDuration"] = stopwatch.ElapsedMilliseconds;

                if (hitRate < 90) // Less than 90% hit rate
                {
                    issues.Add($"Low cache hit rate: {hitRate:F1}%");
                }

                await Task.Delay(1, cancellationToken).ConfigureAwait(false); // Minimal async operation
            }
            catch (Exception ex)
            {
                healthData["cacheHitRateError"] = ex.Message;
                issues.Add($"Cache hit rate check failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Checks memory usage and cache size.
        /// </summary>
        private async Task CheckMemoryUsageAsync(Dictionary<string, object> healthData, List<string> issues, CancellationToken cancellationToken)
        {
            try
            {
                var process = Process.GetCurrentProcess();
                var memoryBefore = process.WorkingSet64;

                // Add a significant number of test entries to measure memory impact
                var testEntries = 1000;
                for (int i = 0; i < testEntries; i++)
                {
                    var serviceId = new ServiceIdentifier("health-check", $"memory-test-{i}");
                    var endpoint = new TransportEndpoint
                    {
                        Address = $"172.16.{i / 256}.{i % 256}",
                        Port = 10000 + i,
                        Protocol = "tcp"
                    };
                    var advertisement = new ServiceAdvertisement(serviceId, endpoint);
                    _cache.AddOrUpdate(advertisement);

                    if (cancellationToken.IsCancellationRequested)
                        break;
                }

                // Force garbage collection to get accurate memory reading
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();

                var memoryAfter = process.WorkingSet64;
                var memoryIncrease = memoryAfter - memoryBefore;
                var memoryPerEntry = testEntries > 0 ? memoryIncrease / testEntries : 0;

                healthData["memoryUsageBeforeMB"] = memoryBefore / (1024 * 1024);
                healthData["memoryUsageAfterMB"] = memoryAfter / (1024 * 1024);
                healthData["memoryIncreaseMB"] = memoryIncrease / (1024 * 1024);
                healthData["memoryPerEntryBytes"] = memoryPerEntry;
                healthData["testEntriesAdded"] = testEntries;

                // Check if memory usage is excessive
                var memoryIncreaseMB = memoryIncrease / (1024 * 1024);
                if (memoryIncreaseMB > 100) // More than 100MB for 1000 entries
                {
                    issues.Add($"High memory usage: {memoryIncreaseMB}MB for {testEntries} entries");
                }

                if (memoryPerEntry > 10240) // More than 10KB per entry
                {
                    issues.Add($"High memory per entry: {memoryPerEntry} bytes per entry");
                }

                await Task.Delay(1, cancellationToken).ConfigureAwait(false); // Minimal async operation
            }
            catch (Exception ex)
            {
                healthData["memoryUsageError"] = ex.Message;
                issues.Add($"Memory usage check failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Checks cache expiration and cleanup functionality.
        /// </summary>
        private async Task CheckCacheExpirationAsync(Dictionary<string, object> healthData, List<string> issues, CancellationToken cancellationToken)
        {
            try
            {
                // Create a service with short TTL for testing
                var serviceId = new ServiceIdentifier("health-check", "expiration-test");
                var endpoint = new TransportEndpoint
                {
                    Address = "*************",
                    Port = 12345,
                    Protocol = "http"
                };
                var advertisement = new ServiceAdvertisement(serviceId, endpoint)
                {
                    Ttl = TimeSpan.FromMilliseconds(100) // Very short TTL for testing
                };

                // Add to cache
                _cache.AddOrUpdate(advertisement);

                // Verify it's in cache
                var servicesBeforeExpiration = _cache.GetActiveAdvertisements().Where(s => s.ServiceId.ToString() == serviceId.ToString());
                var foundBeforeExpiration = servicesBeforeExpiration.Any();

                // Wait for expiration
                await Task.Delay(200, cancellationToken).ConfigureAwait(false);

                // Check if expired services are handled properly
                var servicesAfterExpiration = _cache.GetActiveAdvertisements().Where(s => s.ServiceId.ToString() == serviceId.ToString());
                var foundAfterExpiration = servicesAfterExpiration.Any();

                healthData["expirationTestFoundBeforeExpiration"] = foundBeforeExpiration;
                healthData["expirationTestFoundAfterExpiration"] = foundAfterExpiration;
                healthData["expirationTestWorking"] = foundBeforeExpiration && !foundAfterExpiration;

                if (!foundBeforeExpiration)
                {
                    issues.Add("Cache expiration test failed - service not found before expiration");
                }

                if (foundAfterExpiration)
                {
                    issues.Add("Cache expiration test failed - expired service still found in cache");
                }
            }
            catch (Exception ex)
            {
                healthData["cacheExpirationError"] = ex.Message;
                issues.Add($"Cache expiration check failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Checks enhanced cache statistics and performance metrics.
        /// </summary>
        private async Task CheckCacheStatisticsAsync(Dictionary<string, object> healthData, List<string> issues, CancellationToken cancellationToken)
        {
            try
            {
                // Get current cache statistics
                var stats = _cache.GetStatistics();

                // Add statistics to health data
                healthData["cacheCurrentSize"] = stats.CurrentSize;
                healthData["cacheMaxSize"] = stats.MaxSize;
                healthData["cacheSizeUtilization"] = stats.SizeUtilization;
                healthData["cacheCurrentMemoryMB"] = stats.CurrentMemoryBytes / (1024 * 1024);
                healthData["cacheMaxMemoryMB"] = stats.MaxMemoryBytes / (1024 * 1024);
                healthData["cacheMemoryUtilization"] = stats.MemoryUtilization;
                healthData["cacheHitRate"] = stats.HitRate;
                healthData["cacheMissRate"] = stats.MissRate;
                healthData["cacheTotalHits"] = stats.TotalHits;
                healthData["cacheTotalMisses"] = stats.TotalMisses;
                healthData["cacheTotalAdds"] = stats.TotalAdds;
                healthData["cacheTotalEvictions"] = stats.TotalEvictions;
                healthData["cacheTtlEvictions"] = stats.TtlEvictions;
                healthData["cacheSizeEvictions"] = stats.SizeEvictions;
                healthData["cacheMemoryEvictions"] = stats.MemoryEvictions;
                healthData["cacheAverageEntrySize"] = stats.AverageEntrySize;
                healthData["cacheOperationsPerSecond"] = stats.OperationsPerSecond;
                healthData["cacheEvictionsPerHour"] = stats.EvictionsPerHour;
                healthData["cacheRuntimeHours"] = stats.Runtime.TotalHours;

                // Analyze statistics for potential issues

                // Check hit rate
                if (stats.HitRate < 70 && stats.TotalHits + stats.TotalMisses > 100)
                {
                    issues.Add($"Low cache hit rate: {stats.HitRate:F1}% (target: >70%)");
                }

                // Check memory utilization
                if (stats.MemoryUtilization > 90)
                {
                    issues.Add($"High memory utilization: {stats.MemoryUtilization:F1}% (target: <90%)");
                }
                else if (stats.MemoryUtilization > 80)
                {
                    // Warning level - not an issue but worth noting
                    healthData["memoryUtilizationWarning"] = $"Memory utilization approaching limit: {stats.MemoryUtilization:F1}%";
                }

                // Check size utilization
                if (stats.SizeUtilization > 95)
                {
                    issues.Add($"Cache size near capacity: {stats.SizeUtilization:F1}% (target: <95%)");
                }

                // Check eviction rates
                if (stats.EvictionsPerHour > 1000 && stats.Runtime.TotalHours > 1)
                {
                    issues.Add($"High eviction rate: {stats.EvictionsPerHour:F0} evictions/hour (may indicate undersized cache)");
                }

                // Check average entry size
                if (stats.AverageEntrySize > 10240) // > 10KB per entry
                {
                    issues.Add($"Large average entry size: {stats.AverageEntrySize:F0} bytes (may indicate memory inefficiency)");
                }

                // Check operations per second (if we have enough runtime)
                if (stats.Runtime.TotalMinutes > 5 && stats.OperationsPerSecond < 100)
                {
                    issues.Add($"Low operations throughput: {stats.OperationsPerSecond:F0} ops/sec (may indicate performance issues)");
                }

                // Check for excessive memory evictions
                var memoryEvictionRate = stats.TotalEvictions > 0 ? (double)stats.MemoryEvictions / stats.TotalEvictions * 100 : 0;
                if (memoryEvictionRate > 50 && stats.TotalEvictions > 10)
                {
                    issues.Add($"High memory-based eviction rate: {memoryEvictionRate:F1}% (may indicate insufficient memory limit)");
                }

                await Task.Delay(1, cancellationToken).ConfigureAwait(false); // Minimal async operation
            }
            catch (Exception ex)
            {
                healthData["cacheStatisticsError"] = ex.Message;
                issues.Add($"Cache statistics check failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Determines the overall health status based on issues found.
        /// </summary>
        private static HealthStatus DetermineHealthStatus(List<string> issues, Dictionary<string, object> healthData)
        {
            if (issues.Count == 0)
                return HealthStatus.Healthy;

            // Check for critical issues
            var criticalIssues = issues.FindAll(issue =>
                issue.Contains("basic functionality test failed") ||
                issue.Contains("expiration test failed") ||
                issue.Contains("high failure rate"));

            if (criticalIssues.Count > 0)
                return HealthStatus.Unhealthy;

            // Check for performance issues
            var performanceIssues = issues.FindAll(issue =>
                issue.Contains("slow") ||
                issue.Contains("High memory") ||
                issue.Contains("Low cache hit rate"));

            if (performanceIssues.Count > 0)
                return HealthStatus.Degraded;

            return issues.Count > 2 ? HealthStatus.Degraded : HealthStatus.Healthy;
        }

        /// <summary>
        /// Creates a health description based on the status and issues.
        /// </summary>
        private static string CreateHealthDescription(HealthStatus status, List<string> issues)
        {
            return status switch
            {
                HealthStatus.Healthy => "ServiceAdvertisementCache is healthy and functioning normally",
                HealthStatus.Degraded => $"ServiceAdvertisementCache is degraded with {issues.Count} issue(s): {string.Join("; ", issues)}",
                HealthStatus.Unhealthy => $"ServiceAdvertisementCache is unhealthy with {issues.Count} issue(s): {string.Join("; ", issues)}",
                _ => $"ServiceAdvertisementCache health status is unknown with {issues.Count} issue(s): {string.Join("; ", issues)}"
            };
        }
    }
}
