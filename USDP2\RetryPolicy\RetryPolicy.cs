using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

namespace USDP2.RetryPolicy
{
    /// <summary>
    /// Defines the result of a retry condition evaluation.
    /// </summary>
    public enum RetryDecision
    {
        /// <summary>
        /// Do not retry the operation.
        /// </summary>
        DoNotRetry,

        /// <summary>
        /// Retry the operation.
        /// </summary>
        Retry,

        /// <summary>
        /// Retry the operation immediately without delay.
        /// </summary>
        RetryImmediately
    }

    /// <summary>
    /// Represents the context of a retry attempt.
    /// </summary>
    public class RetryContext
    {
        /// <summary>
        /// Gets the current attempt number (1-based).
        /// </summary>
        public int AttemptNumber { get; }

        /// <summary>
        /// Gets the exception that caused the retry (if any).
        /// </summary>
        public Exception? Exception { get; }

        /// <summary>
        /// Gets the total elapsed time since the first attempt.
        /// </summary>
        public TimeSpan ElapsedTime { get; }

        /// <summary>
        /// Gets additional context data.
        /// </summary>
        public Dictionary<string, object> Data { get; }

        /// <summary>
        /// Initializes a new instance of the <see cref="RetryContext"/> class.
        /// </summary>
        /// <param name="attemptNumber">The current attempt number.</param>
        /// <param name="exception">The exception that caused the retry.</param>
        /// <param name="elapsedTime">The total elapsed time.</param>
        /// <param name="data">Additional context data.</param>
        public RetryContext(int attemptNumber, Exception? exception, TimeSpan elapsedTime, Dictionary<string, object>? data = null)
        {
            AttemptNumber = attemptNumber;
            Exception = exception;
            ElapsedTime = elapsedTime;
            Data = data ?? new Dictionary<string, object>();
        }
    }

    /// <summary>
    /// Defines a condition for determining whether to retry an operation.
    /// </summary>
    public interface IRetryCondition
    {
        /// <summary>
        /// Determines whether the operation should be retried based on the context.
        /// </summary>
        /// <param name="context">The retry context.</param>
        /// <returns>The retry decision.</returns>
        RetryDecision ShouldRetry(RetryContext context);
    }

    /// <summary>
    /// Defines a strategy for calculating retry delays.
    /// </summary>
    public interface IRetryDelayStrategy
    {
        /// <summary>
        /// Calculates the delay before the next retry attempt.
        /// </summary>
        /// <param name="attemptNumber">The current attempt number (1-based).</param>
        /// <param name="baseDelay">The base delay for calculations.</param>
        /// <returns>The delay before the next retry attempt.</returns>
        TimeSpan CalculateDelay(int attemptNumber, TimeSpan baseDelay);
    }

    /// <summary>
    /// Configuration options for retry policies.
    /// </summary>
    public class RetryPolicyOptions
    {
        /// <summary>
        /// Gets or sets the maximum number of retry attempts.
        /// Default: 3
        /// </summary>
        public int MaxAttempts { get; set; } = 3;

        /// <summary>
        /// Gets or sets the base delay between retry attempts.
        /// Default: 1 second
        /// </summary>
        public TimeSpan BaseDelay { get; set; } = TimeSpan.FromSeconds(1);

        /// <summary>
        /// Gets or sets the maximum delay between retry attempts.
        /// Default: 30 seconds
        /// </summary>
        public TimeSpan MaxDelay { get; set; } = TimeSpan.FromSeconds(30);

        /// <summary>
        /// Gets or sets the maximum total time for all retry attempts.
        /// Default: 2 minutes
        /// </summary>
        public TimeSpan MaxTotalTime { get; set; } = TimeSpan.FromMinutes(2);

        /// <summary>
        /// Gets or sets the retry delay strategy.
        /// Default: ExponentialBackoffStrategy
        /// </summary>
        public IRetryDelayStrategy DelayStrategy { get; set; } = new ExponentialBackoffStrategy();

        /// <summary>
        /// Gets or sets the retry conditions.
        /// Default: NetworkErrorRetryCondition
        /// </summary>
        public List<IRetryCondition> RetryConditions { get; set; } = new List<IRetryCondition> { new NetworkErrorRetryCondition() };

        /// <summary>
        /// Gets or sets whether to add jitter to retry delays to prevent thundering herd.
        /// Default: true
        /// </summary>
        public bool UseJitter { get; set; } = true;

        /// <summary>
        /// Gets or sets the jitter factor (0.0 to 1.0).
        /// Default: 0.1 (10% jitter)
        /// </summary>
        public double JitterFactor { get; set; } = 0.1;

        /// <summary>
        /// Gets or sets whether to log retry attempts.
        /// Default: true
        /// </summary>
        public bool LogRetryAttempts { get; set; } = true;
    }

    /// <summary>
    /// Exponential backoff retry delay strategy with optional jitter.
    /// </summary>
    public class ExponentialBackoffStrategy : IRetryDelayStrategy
    {
        private readonly double _multiplier;
        private readonly Random _random = new();

        /// <summary>
        /// Initializes a new instance of the <see cref="ExponentialBackoffStrategy"/> class.
        /// </summary>
        /// <param name="multiplier">The exponential multiplier. Default: 2.0</param>
        public ExponentialBackoffStrategy(double multiplier = 2.0)
        {
            _multiplier = multiplier;
        }

        /// <summary>
        /// Calculates the delay using exponential backoff.
        /// </summary>
        /// <param name="attemptNumber">The current attempt number (1-based).</param>
        /// <param name="baseDelay">The base delay for calculations.</param>
        /// <returns>The delay before the next retry attempt.</returns>
        public TimeSpan CalculateDelay(int attemptNumber, TimeSpan baseDelay)
        {
            // Calculate exponential backoff: baseDelay * multiplier^(attemptNumber-1)
            var delay = TimeSpan.FromMilliseconds(
                baseDelay.TotalMilliseconds * Math.Pow(_multiplier, attemptNumber - 1));

            return delay;
        }
    }

    /// <summary>
    /// Linear backoff retry delay strategy.
    /// </summary>
    public class LinearBackoffStrategy : IRetryDelayStrategy
    {
        /// <summary>
        /// Calculates the delay using linear backoff.
        /// </summary>
        /// <param name="attemptNumber">The current attempt number (1-based).</param>
        /// <param name="baseDelay">The base delay for calculations.</param>
        /// <returns>The delay before the next retry attempt.</returns>
        public TimeSpan CalculateDelay(int attemptNumber, TimeSpan baseDelay)
        {
            // Calculate linear backoff: baseDelay * attemptNumber
            return TimeSpan.FromMilliseconds(baseDelay.TotalMilliseconds * attemptNumber);
        }
    }

    /// <summary>
    /// Fixed delay retry strategy.
    /// </summary>
    public class FixedDelayStrategy : IRetryDelayStrategy
    {
        /// <summary>
        /// Calculates the delay using a fixed delay.
        /// </summary>
        /// <param name="attemptNumber">The current attempt number (1-based).</param>
        /// <param name="baseDelay">The base delay for calculations.</param>
        /// <returns>The delay before the next retry attempt.</returns>
        public TimeSpan CalculateDelay(int attemptNumber, TimeSpan baseDelay)
        {
            return baseDelay;
        }
    }

    /// <summary>
    /// Retry condition for network-related errors.
    /// </summary>
    public class NetworkErrorRetryCondition : IRetryCondition
    {
        /// <summary>
        /// Determines whether to retry based on network error conditions.
        /// </summary>
        /// <param name="context">The retry context.</param>
        /// <returns>The retry decision.</returns>
        public RetryDecision ShouldRetry(RetryContext context)
        {
            if (context.Exception == null)
                return RetryDecision.DoNotRetry;

            return context.Exception switch
            {
                // Retry on timeout exceptions
                TimeoutException => RetryDecision.Retry,
                TaskCanceledException when context.Exception.InnerException is TimeoutException => RetryDecision.Retry,

                // Retry on specific HTTP status codes
                HttpRequestException httpEx when IsRetryableHttpError(httpEx) => RetryDecision.Retry,

                // Retry on network connectivity issues
                System.Net.Sockets.SocketException => RetryDecision.Retry,
                System.Net.NetworkInformation.NetworkInformationException => RetryDecision.Retry,

                // Don't retry on client errors (4xx) except specific cases
                HttpRequestException httpEx when IsClientError(httpEx) => RetryDecision.DoNotRetry,

                // Don't retry on cancellation (unless timeout)
                OperationCanceledException => RetryDecision.DoNotRetry,

                // Retry on other exceptions by default
                _ => RetryDecision.Retry
            };
        }

        private static bool IsRetryableHttpError(HttpRequestException httpEx)
        {
            var message = httpEx.Message.ToLowerInvariant();

            // Retry on server errors (5xx)
            if (message.Contains("500") || message.Contains("502") || message.Contains("503") ||
                message.Contains("504") || message.Contains("internal server error") ||
                message.Contains("bad gateway") || message.Contains("service unavailable") ||
                message.Contains("gateway timeout"))
                return true;

            // Retry on specific client errors
            if (message.Contains("408") || message.Contains("request timeout") ||
                message.Contains("429") || message.Contains("too many requests"))
                return true;

            return false;
        }

        private static bool IsClientError(HttpRequestException httpEx)
        {
            var message = httpEx.Message.ToLowerInvariant();

            // Don't retry on most 4xx errors
            return message.Contains("400") || message.Contains("401") || message.Contains("403") ||
                   message.Contains("404") || message.Contains("405") || message.Contains("406") ||
                   message.Contains("bad request") || message.Contains("unauthorized") ||
                   message.Contains("forbidden") || message.Contains("not found") ||
                   message.Contains("method not allowed");
        }
    }

    /// <summary>
    /// Retry condition for Chord-specific errors.
    /// </summary>
    public class ChordRetryCondition : IRetryCondition
    {
        /// <summary>
        /// Determines whether to retry based on Chord-specific error conditions.
        /// </summary>
        /// <param name="context">The retry context.</param>
        /// <returns>The retry decision.</returns>
        public RetryDecision ShouldRetry(RetryContext context)
        {
            if (context.Exception == null)
                return RetryDecision.DoNotRetry;

            return context.Exception switch
            {
                // Chord-specific exceptions
                ChordNetworkException chordNetEx => GetChordNetworkRetryDecision(chordNetEx),
                ChordSerializationException => RetryDecision.DoNotRetry, // Serialization errors are not transient
                ChordLookupException chordLookupEx => GetChordLookupRetryDecision(chordLookupEx),

                // Circuit breaker exceptions - don't retry, let circuit breaker handle it
                CircuitBreakerOpenException => RetryDecision.DoNotRetry,

                // Use base network condition for other network errors
                _ => new NetworkErrorRetryCondition().ShouldRetry(context)
            };
        }

        /// <summary>
        /// Determines retry decision for Chord network exceptions.
        /// </summary>
        /// <param name="chordNetworkException">The Chord network exception.</param>
        /// <returns>The retry decision.</returns>
        private static RetryDecision GetChordNetworkRetryDecision(ChordNetworkException chordNetworkException)
        {
            return chordNetworkException.ErrorCode switch
            {
                ChordErrorCode.NetworkTimeout => RetryDecision.Retry,
                ChordErrorCode.NetworkConnectivity => RetryDecision.Retry,
                ChordErrorCode.NodeUnavailable => RetryDecision.Retry,
                ChordErrorCode.UnknownNetworkError => RetryDecision.Retry,
                ChordErrorCode.NetworkFailureAfterRetries => RetryDecision.DoNotRetry, // Already retried
                _ => RetryDecision.Retry // Default to retry for unknown network errors
            };
        }

        /// <summary>
        /// Determines retry decision for Chord lookup exceptions.
        /// </summary>
        /// <param name="chordLookupException">The Chord lookup exception.</param>
        /// <returns>The retry decision.</returns>
        private static RetryDecision GetChordLookupRetryDecision(ChordLookupException chordLookupException)
        {
            return chordLookupException.ErrorCode switch
            {
                ChordErrorCode.LookupFailure => RetryDecision.Retry,
                ChordErrorCode.RingInconsistency => RetryDecision.Retry,
                ChordErrorCode.NetworkFailureAfterRetries => RetryDecision.DoNotRetry, // Already retried
                ChordErrorCode.UnexpectedError => RetryDecision.DoNotRetry, // Don't retry unexpected errors
                _ => RetryDecision.Retry // Default to retry for unknown lookup errors
            };
        }
    }
}
