using System;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using USDP2.RetryPolicy;

namespace USDP2.Examples
{
    /// <summary>
    /// Demonstrates the retry policy implementation in USDP2 network components.
    /// 
    /// This example shows:
    /// - How retry policies handle transient failures automatically
    /// - Exponential backoff with jitter to prevent thundering herd
    /// - Intelligent retry conditions based on error types
    /// - Integration with DynamicDnsClient, CoapToHttpTranslator, and ChordNode
    /// - Configuration options for different retry strategies
    /// </summary>
    public static class RetryPolicyExample
    {
        /// <summary>
        /// Runs a comprehensive demonstration of the retry policy implementation.
        /// </summary>
        public static async Task RunAsync()
        {
            Console.WriteLine("=== USDP2 Retry Policy Implementation Example ===");
            Console.WriteLine();

            try
            {
                // Step 1: Configure retry policy settings
                await ConfigureRetryPolicySettingsAsync();

                // Step 2: Demonstrate basic retry policy usage
                await DemonstrateBasicRetryPolicyAsync();

                // Step 3: Demonstrate DynamicDnsClient retry behavior
                await DemonstrateDynamicDnsClientRetryAsync();

                // Step 4: Demonstrate CoapToHttpTranslator retry behavior
                await DemonstrateCoapToHttpTranslatorRetryAsync();

                // Step 5: Demonstrate ChordNode retry behavior
                await DemonstrateChordNodeRetryAsync();

                // Step 6: Show different retry strategies
                await DemonstrateRetryStrategiesAsync();

                // Step 7: Test retry conditions and error handling
                await TestRetryConditionsAsync();

                Console.WriteLine("\n=== Retry Policy Example Complete ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Example failed: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// Configures retry policy settings for demonstration.
        /// </summary>
        private static async Task ConfigureRetryPolicySettingsAsync()
        {
            Console.WriteLine("1. Configuring Retry Policy Settings");
            Console.WriteLine("====================================");

            // Configure retry policy settings for faster demonstration
            UsdpConfiguration.Instance.EnableRetryPolicies = true;
            UsdpConfiguration.Instance.NetworkRetryMaxAttempts = 3;
            UsdpConfiguration.Instance.NetworkRetryBaseDelay = TimeSpan.FromMilliseconds(500); // Shorter for demo
            UsdpConfiguration.Instance.HttpRetryMaxAttempts = 4;
            UsdpConfiguration.Instance.HttpRetryBaseDelay = TimeSpan.FromSeconds(1);
            UsdpConfiguration.Instance.LogNetworkRetryAttempts = true;
            UsdpConfiguration.Instance.LogHttpRetryAttempts = true;

            Console.WriteLine($"✓ Retry policies enabled: {UsdpConfiguration.Instance.EnableRetryPolicies}");
            Console.WriteLine($"✓ Network retry max attempts: {UsdpConfiguration.Instance.NetworkRetryMaxAttempts}");
            Console.WriteLine($"✓ Network retry base delay: {UsdpConfiguration.Instance.NetworkRetryBaseDelay.TotalMilliseconds} ms");
            Console.WriteLine($"✓ HTTP retry max attempts: {UsdpConfiguration.Instance.HttpRetryMaxAttempts}");
            Console.WriteLine($"✓ HTTP retry base delay: {UsdpConfiguration.Instance.HttpRetryBaseDelay.TotalSeconds} seconds");
            Console.WriteLine($"✓ Retry logging enabled: {UsdpConfiguration.Instance.LogHttpRetryAttempts}");
            Console.WriteLine();

            await Task.Delay(100); // Brief pause for demonstration
        }

        /// <summary>
        /// Demonstrates basic retry policy usage.
        /// </summary>
        private static async Task DemonstrateBasicRetryPolicyAsync()
        {
            Console.WriteLine("2. Basic Retry Policy Usage");
            Console.WriteLine("===========================");

            var retryPolicy = RetryPolicyExecutor.CreateNetworkRetryPolicy();

            Console.WriteLine("Testing retry policy with simulated failures...");

            var attemptCount = 0;
            try
            {
                await retryPolicy.ExecuteAsync(async ct =>
                {
                    attemptCount++;
                    Console.WriteLine($"  Attempt {attemptCount}: Simulating operation...");

                    if (attemptCount < 3)
                    {
                        throw new TimeoutException($"Simulated timeout on attempt {attemptCount}");
                    }

                    Console.WriteLine($"  ✅ Success on attempt {attemptCount}!");
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  ❌ Final failure: {ex.Message}");
            }

            Console.WriteLine("✓ Basic retry policy demonstration completed");
            Console.WriteLine();
        }

        /// <summary>
        /// Demonstrates DynamicDnsClient retry behavior.
        /// </summary>
        private static async Task DemonstrateDynamicDnsClientRetryAsync()
        {
            Console.WriteLine("3. DynamicDnsClient Retry Behavior");
            Console.WriteLine("==================================");

            using var dnsClient = new DynamicDnsClient("example.com", "test-token");

            Console.WriteLine("Testing DNS update with retry policy...");

            try
            {
                // This will fail but demonstrate retry behavior
                var result = await dnsClient.UpdateAsync(
                    "https://***************/update", // Non-routable address
                    bearerToken: "test-token");

                Console.WriteLine($"  DNS update result: {result}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  DNS update failed after retries: {ex.GetType().Name}");
            }

            Console.WriteLine("✓ DynamicDnsClient retry demonstration completed");
            Console.WriteLine();
        }

        /// <summary>
        /// Demonstrates CoapToHttpTranslator retry behavior.
        /// </summary>
        private static async Task DemonstrateCoapToHttpTranslatorRetryAsync()
        {
            Console.WriteLine("4. CoapToHttpTranslator Retry Behavior");
            Console.WriteLine("======================================");

            using var translator = new CoapToHttpTranslator();
            var testData = System.Text.Encoding.UTF8.GetBytes("Test CoAP message");

            Console.WriteLine("Testing CoAP to HTTP translation with retry policy...");

            try
            {
                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(10));
                await translator.TranslateAsync(
                    testData,
                    "coap+udp",
                    "http",
                    "**************", // Non-routable address
                    8080,
                    cts.Token);

                Console.WriteLine("  ✅ Translation successful (unexpected)");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  Translation failed after retries: {ex.GetType().Name}");
            }

            Console.WriteLine("✓ CoapToHttpTranslator retry demonstration completed");
            Console.WriteLine();
        }

        /// <summary>
        /// Demonstrates ChordNode retry behavior.
        /// </summary>
        private static async Task DemonstrateChordNodeRetryAsync()
        {
            Console.WriteLine("5. ChordNode Retry Behavior");
            Console.WriteLine("===========================");

            var chordNode = new ChordNode("127.0.0.1", 8080);

            Console.WriteLine("Testing Chord operations with retry policy...");

            try
            {
                // Test join operation
                Console.WriteLine("  Testing JoinAsync...");
                await chordNode.JoinAsync("192.168.1.100", 8080);
                Console.WriteLine("  ✅ Join operation completed");

                // Test store operation
                Console.WriteLine("  Testing StoreAsync...");
                await chordNode.StoreAsync(12345, "test-value");
                Console.WriteLine("  ✅ Store operation completed");

                // Test lookup operation
                Console.WriteLine("  Testing LookupAsync...");
                var result = await chordNode.LookupAsync(12345);
                Console.WriteLine($"  ✅ Lookup result: {result ?? "null"}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  Chord operation failed: {ex.GetType().Name}");
            }

            Console.WriteLine("✓ ChordNode retry demonstration completed");
            Console.WriteLine();
        }

        /// <summary>
        /// Demonstrates different retry strategies.
        /// </summary>
        private static async Task DemonstrateRetryStrategiesAsync()
        {
            Console.WriteLine("6. Different Retry Strategies");
            Console.WriteLine("=============================");

            // Exponential backoff
            Console.WriteLine("Testing Exponential Backoff Strategy:");
            var exponentialPolicy = RetryPolicyExecutor.CreateCustomRetryPolicy(
                maxAttempts: 3,
                baseDelay: TimeSpan.FromMilliseconds(100),
                useExponentialBackoff: true,
                useJitter: false);

            await TestRetryStrategy(exponentialPolicy, "Exponential");

            // Linear backoff
            Console.WriteLine("\nTesting Linear Backoff Strategy:");
            var linearOptions = new RetryPolicyOptions
            {
                MaxAttempts = 3,
                BaseDelay = TimeSpan.FromMilliseconds(100),
                DelayStrategy = new LinearBackoffStrategy(),
                UseJitter = false,
                LogRetryAttempts = true
            };
            var linearPolicy = new RetryPolicyExecutor(linearOptions);

            await TestRetryStrategy(linearPolicy, "Linear");

            // Fixed delay
            Console.WriteLine("\nTesting Fixed Delay Strategy:");
            var fixedOptions = new RetryPolicyOptions
            {
                MaxAttempts = 3,
                BaseDelay = TimeSpan.FromMilliseconds(200),
                DelayStrategy = new FixedDelayStrategy(),
                UseJitter = false,
                LogRetryAttempts = true
            };
            var fixedPolicy = new RetryPolicyExecutor(fixedOptions);

            await TestRetryStrategy(fixedPolicy, "Fixed");

            Console.WriteLine("✓ Retry strategies demonstration completed");
            Console.WriteLine();
        }

        /// <summary>
        /// Tests retry conditions and error handling.
        /// </summary>
        private static async Task TestRetryConditionsAsync()
        {
            Console.WriteLine("7. Testing Retry Conditions");
            Console.WriteLine("===========================");

            var retryPolicy = RetryPolicyExecutor.CreateHttpRetryPolicy();

            // Test different exception types
            var testCases = new (string Name, Exception Exception)[]
            {
                ("TimeoutException", new TimeoutException("Request timeout")),
                ("HttpRequestException (500)", new HttpRequestException("500 Internal Server Error")),
                ("HttpRequestException (404)", new HttpRequestException("404 Not Found")),
                ("HttpRequestException (429)", new HttpRequestException("429 Too Many Requests")),
                ("OperationCanceledException", new OperationCanceledException("Operation cancelled"))
            };

            foreach (var (name, exception) in testCases)
            {
                Console.WriteLine($"\nTesting {name}:");
                var attemptCount = 0;

                try
                {
                    await retryPolicy.ExecuteAsync(async ct =>
                    {
                        attemptCount++;
                        Console.WriteLine($"  Attempt {attemptCount}: Throwing {name}");
                        throw exception;
                    });
                }
                catch
                {
                    Console.WriteLine($"  Final result: {attemptCount} attempts made");
                }
            }

            Console.WriteLine("\n✓ Retry conditions testing completed");
            Console.WriteLine();
        }

        /// <summary>
        /// Helper method to test a retry strategy.
        /// </summary>
        private static async Task TestRetryStrategy(RetryPolicyExecutor policy, string strategyName)
        {
            var attemptCount = 0;
            var startTime = DateTime.UtcNow;

            try
            {
                await policy.ExecuteAsync(async ct =>
                {
                    attemptCount++;
                    var elapsed = DateTime.UtcNow - startTime;
                    Console.WriteLine($"  {strategyName} attempt {attemptCount} at {elapsed.TotalMilliseconds:F0}ms");

                    if (attemptCount < 3)
                    {
                        throw new TimeoutException($"Simulated failure {attemptCount}");
                    }
                });
            }
            catch
            {
                var totalElapsed = DateTime.UtcNow - startTime;
                Console.WriteLine($"  {strategyName} strategy completed in {totalElapsed.TotalMilliseconds:F0}ms");
            }
        }
    }
}
