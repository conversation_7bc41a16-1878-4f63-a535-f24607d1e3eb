using System;
using System.Buffers;
using System.Net;
using System.Threading;
using System.Threading.Tasks;

namespace USDP2
{
    /// <summary>
    /// The http network receiver.
    /// </summary>
    public class HttpNetworkReceiver : INetworkReceiver
    {
        /// <summary>
        /// The listener.
        /// </summary>
        private readonly HttpListener _listener;

        /// <summary>
        /// Initializes a new instance of the <see cref="HttpNetworkReceiver"/> class.
        /// </summary>
        /// <param name="port">The port to listen on. Must be between 1 and 65535.</param>
        /// <exception cref="ArgumentOutOfRangeException">Thrown when port is outside the valid range.</exception>
        public HttpNetworkReceiver(int port)
        {
            // Validate port before attempting to create HTTP listener
            InputValidator.ValidatePort(port, nameof(port));

            _listener = new HttpListener();
            var path = UsdpConfiguration.Instance.HttpEndpointPath;
            _listener.Prefixes.Add($"http://+:{port}{path}/");
        }

        /// <summary>
        /// Start the receiving asynchronously.
        /// </summary>
        /// <param name="onMessageReceived">The on message received.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>A <see cref="Task"/></returns>

        public async Task StartReceivingAsync(Func<byte[], string, int, Task> onMessageReceived, CancellationToken cancellationToken = default)
        {
            _listener.Start();
            var bufferPool = ArrayPool<byte>.Shared;

            try
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    var context = await _listener.GetContextAsync().ConfigureAwait(false);
                    var request = context.Request;
                    using var response = context.Response;

                    var buffer = bufferPool.Rent((int)request.ContentLength64);
                    try
                    {
                        int bytesRead = await request.InputStream.ReadAsync(buffer.AsMemory(0, buffer.Length), cancellationToken).ConfigureAwait(false);

                        var remoteIp = context.Request.RemoteEndPoint?.Address.ToString() ?? "";
                        var remotePort = context.Request.RemoteEndPoint?.Port ?? 0;

                        try
                        {
                            await onMessageReceived(buffer.AsSpan(0, bytesRead).ToArray(), remoteIp, remotePort).ConfigureAwait(false);
                        }
                        catch (Exception ex)
                        {
                            // Log the exception and continue
                            Diagnostics.Log("MessageProcessingError", new { RemoteAddress = remoteIp, RemotePort = remotePort, Error = ex.Message });
                        }

                        response.StatusCode = 200;
                    }
                    finally
                    {
                        bufferPool.Return(buffer);
                    }

                    response.Close();
                }
            }
            finally
            {
                _listener.Stop();
            }
        }



        /// <summary>
        /// TODO: Add Summary.
        /// </summary>
        /// <returns>A <see cref="ValueTask"/></returns>
        public ValueTask DisposeAsync()
        {
            _listener.Close();
            GC.SuppressFinalize(this);
            return ValueTask.CompletedTask;
        }
    }
}