using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace USDP2.Tests
{
    /// <summary>
    /// Tests for the enhanced ServiceAdvertisementCache functionality.
    /// </summary>
    [TestClass]
    public class ServiceAdvertisementCacheEnhancedTests
    {
        private ServiceAdvertisementCache? _cache;
        private UsdpConfiguration? _testConfig;

        [TestInitialize]
        public void Setup()
        {
            // Create test configuration with smaller limits for testing
            _testConfig = new UsdpConfiguration(forTesting: true)
            {
                ServiceCacheMaxSize = 100,
                ServiceCacheMaxMemoryBytes = 1024 * 1024, // 1MB
                ServiceCacheEvictionPolicy = "LRU",
                ServiceCacheStatisticsEnabled = true,
                ServiceCacheEventsEnabled = true,
                ServiceCacheMaintenanceInterval = TimeSpan.FromMilliseconds(100)
            };

            _cache = new ServiceAdvertisementCache(_testConfig);
        }

        [TestCleanup]
        public void Cleanup()
        {
            _cache?.Dispose();
        }

        [TestMethod]
        public void AddOrUpdate_ShouldUpdateStatistics()
        {
            // Arrange
            var serviceId = new ServiceIdentifier("test", "service1");
            var endpoint = new TransportEndpoint { Address = "127.0.0.1", Port = 8080, Protocol = "http" };
            var advertisement = new ServiceAdvertisement(serviceId, endpoint);

            // Act
            _cache!.AddOrUpdate(advertisement);
            var stats = _cache.GetStatistics();

            // Assert
            Assert.AreEqual(1, stats.CurrentSize);
            Assert.AreEqual(1, stats.TotalAdds);
            Assert.IsTrue(stats.CurrentMemoryBytes > 0);
        }

        [TestMethod]
        public void GetActiveAdvertisements_ShouldUpdateHitStatistics()
        {
            // Arrange
            var serviceId = new ServiceIdentifier("test", "service1");
            var endpoint = new TransportEndpoint { Address = "127.0.0.1", Port = 8080, Protocol = "http" };
            var advertisement = new ServiceAdvertisement(serviceId, endpoint);
            _cache!.AddOrUpdate(advertisement);

            // Act
            var services = _cache.GetActiveAdvertisements().ToList();
            var stats = _cache.GetStatistics();

            // Assert
            Assert.AreEqual(1, services.Count);
            Assert.IsTrue(stats.TotalHits > 0);
            Assert.IsTrue(stats.HitRate > 0);
        }

        [TestMethod]
        public void GetAdvertisement_WithValidId_ShouldReturnAdvertisementAndUpdateStats()
        {
            // Arrange
            var serviceId = new ServiceIdentifier("test", "service1");
            var endpoint = new TransportEndpoint { Address = "127.0.0.1", Port = 8080, Protocol = "http" };
            var advertisement = new ServiceAdvertisement(serviceId, endpoint);
            _cache!.AddOrUpdate(advertisement);

            // Act
            var retrieved = _cache.GetAdvertisement(serviceId);
            var stats = _cache.GetStatistics();

            // Assert
            Assert.IsNotNull(retrieved);
            Assert.AreEqual(serviceId.ToString(), retrieved.ServiceId.ToString());
            Assert.IsTrue(stats.TotalHits > 0);
        }

        [TestMethod]
        public void GetAdvertisement_WithInvalidId_ShouldReturnNullAndUpdateMissStats()
        {
            // Arrange
            var serviceId = new ServiceIdentifier("test", "nonexistent");

            // Act
            var retrieved = _cache!.GetAdvertisement(serviceId);
            var stats = _cache.GetStatistics();

            // Assert
            Assert.IsNull(retrieved);
            Assert.IsTrue(stats.TotalMisses > 0);
            Assert.AreEqual(0, stats.HitRate);
        }

        [TestMethod]
        public void Remove_ShouldUpdateStatistics()
        {
            // Arrange
            var serviceId = new ServiceIdentifier("test", "service1");
            var endpoint = new TransportEndpoint { Address = "127.0.0.1", Port = 8080, Protocol = "http" };
            var advertisement = new ServiceAdvertisement(serviceId, endpoint);
            _cache!.AddOrUpdate(advertisement);

            // Act
            var removed = _cache.Remove(serviceId);
            var stats = _cache.GetStatistics();

            // Assert
            Assert.IsTrue(removed);
            Assert.AreEqual(0, stats.CurrentSize);
            Assert.IsTrue(stats.TotalEvictions > 0);
        }

        [TestMethod]
        public void Clear_ShouldResetStatistics()
        {
            // Arrange
            for (int i = 0; i < 5; i++)
            {
                var serviceId = new ServiceIdentifier("test", $"service{i}");
                var endpoint = new TransportEndpoint { Address = "127.0.0.1", Port = 8080 + i, Protocol = "http" };
                var advertisement = new ServiceAdvertisement(serviceId, endpoint);
                _cache!.AddOrUpdate(advertisement);
            }

            // Act
            _cache!.Clear("Test cleanup");
            var stats = _cache.GetStatistics();

            // Assert
            Assert.AreEqual(0, stats.CurrentSize);
            Assert.AreEqual(0, stats.CurrentMemoryBytes);
        }

        [TestMethod]
        public void EvictionPolicy_LRU_ShouldEvictLeastRecentlyUsed()
        {
            // Arrange - Fill cache to capacity
            for (int i = 0; i < _testConfig!.ServiceCacheMaxSize; i++)
            {
                var serviceId = new ServiceIdentifier("test", $"service{i}");
                var endpoint = new TransportEndpoint { Address = "127.0.0.1", Port = 8080 + i, Protocol = "http" };
                var advertisement = new ServiceAdvertisement(serviceId, endpoint);
                _cache!.AddOrUpdate(advertisement);
            }

            // Access the first service to make it recently used
            var firstServiceId = new ServiceIdentifier("test", "service0");
            _cache.GetAdvertisement(firstServiceId);

            // Act - Add one more service to trigger eviction
            var newServiceId = new ServiceIdentifier("test", "newservice");
            var newEndpoint = new TransportEndpoint { Address = "127.0.0.1", Port = 9999, Protocol = "http" };
            var newAdvertisement = new ServiceAdvertisement(newServiceId, newEndpoint);
            _cache.AddOrUpdate(newAdvertisement);

            // Wait for maintenance to run
            Thread.Sleep(200);

            // Assert - First service should still be there, but some other should be evicted
            var firstServiceStillExists = _cache.GetAdvertisement(firstServiceId) != null;
            var stats = _cache.GetStatistics();

            Assert.IsTrue(firstServiceStillExists, "Recently accessed service should not be evicted");
            Assert.IsTrue(stats.TotalEvictions > 0, "Some evictions should have occurred");
        }

        [TestMethod]
        public void CacheEvents_ShouldBeRaisedWhenEnabled()
        {
            // Arrange
            var hitEventRaised = false;
            var addEventRaised = false;
            var evictEventRaised = false;

            _cache!.CacheHit += (sender, args) => hitEventRaised = true;
            _cache.EntryAdded += (sender, args) => addEventRaised = true;
            _cache.EntryEvicted += (sender, args) => evictEventRaised = true;

            var serviceId = new ServiceIdentifier("test", "service1");
            var endpoint = new TransportEndpoint { Address = "127.0.0.1", Port = 8080, Protocol = "http" };
            var advertisement = new ServiceAdvertisement(serviceId, endpoint);

            // Act
            _cache.AddOrUpdate(advertisement);
            _cache.GetAdvertisement(serviceId);
            _cache.Remove(serviceId);

            // Assert
            Assert.IsTrue(addEventRaised, "EntryAdded event should be raised");
            Assert.IsTrue(hitEventRaised, "CacheHit event should be raised");
            Assert.IsTrue(evictEventRaised, "EntryEvicted event should be raised");
        }

        [TestMethod]
        public void ExpiredEntries_ShouldBeHandledCorrectly()
        {
            // Arrange
            var serviceId = new ServiceIdentifier("test", "expiring-service");
            var endpoint = new TransportEndpoint { Address = "127.0.0.1", Port = 8080, Protocol = "http" };
            var advertisement = new ServiceAdvertisement(serviceId, endpoint)
            {
                Ttl = TimeSpan.FromMilliseconds(50) // Very short TTL
            };

            // Act
            _cache!.AddOrUpdate(advertisement);

            // Verify it's initially available
            var initialResult = _cache.GetAdvertisement(serviceId);

            // Wait for expiration
            Thread.Sleep(100);

            // Try to get expired entry
            var expiredResult = _cache.GetAdvertisement(serviceId);
            var stats = _cache.GetStatistics();

            // Assert
            Assert.IsNotNull(initialResult, "Service should be available initially");
            Assert.IsNull(expiredResult, "Expired service should not be available");
            Assert.IsTrue(stats.TotalMisses > 0, "Miss should be recorded for expired entry");
        }

        [TestMethod]
        public void Dispose_ShouldCleanupResources()
        {
            // Arrange
            var serviceId = new ServiceIdentifier("test", "service1");
            var endpoint = new TransportEndpoint { Address = "127.0.0.1", Port = 8080, Protocol = "http" };
            var advertisement = new ServiceAdvertisement(serviceId, endpoint);
            _cache!.AddOrUpdate(advertisement);

            // Act
            _cache.Dispose();

            // Assert - Should throw ObjectDisposedException
            Assert.ThrowsException<ObjectDisposedException>(() => _cache.GetStatistics());
            Assert.ThrowsException<ObjectDisposedException>(() => _cache.AddOrUpdate(advertisement));
        }
    }
}
