using System;
using System.Formats.Cbor;
using System.IO;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace USDP2
{
    /// <summary>
    /// The default USDP serializer implementation that uses System.Text.Json for JSON and PeterO.Cbor for CBOR.
    /// </summary>
    public class DefaultUSDPSerializer : IUSDPSerializer
    {
        private readonly JsonSerializerOptions _jsonOptions;

        /// <summary>
        /// Initializes a new instance of the <see cref="DefaultUSDPSerializer"/> class.
        /// </summary>
        public DefaultUSDPSerializer()
        {
            // Configure JSON serialization options
            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
                WriteIndented = false,
                Converters = {
                    new JsonStringEnumConverter(),
                    new ObjectDictionaryConverter()
                }
            };
        }

        /// <summary>
        /// Serializes an object to a JSON string.
        /// </summary>
        /// <typeparam name="T">The type of the object to serialize.</typeparam>
        /// <param name="obj">The object to serialize.</param>
        /// <returns>A <see cref="SerializationResult{String}"/> containing the JSON string or error information.</returns>
        public SerializationResult<string> SerializeToJson<T>(T obj) where T : class
        {
            if (obj == null)
            {
                var result = SerializationResult<string>.NullInput();
                Diagnostics.Log("JsonSerializationError", new { Type = typeof(T).Name, Error = result.ErrorMessage });
                return result;
            }

            try
            {
                string json = JsonSerializer.Serialize(obj, _jsonOptions);
                return SerializationResult<string>.Success(json);
            }
            catch (JsonException ex)
            {
                var result = SerializationResult<string>.InvalidFormat(ex.Message, ex);
                Diagnostics.Log("JsonSerializationError", new { Type = typeof(T).Name, Error = result.ErrorMessage });
                return result;
            }
            catch (Exception ex)
            {
                var result = SerializationResult<string>.UnknownError(ex.Message, ex);
                Diagnostics.Log("JsonSerializationError", new { Type = typeof(T).Name, Error = result.ErrorMessage });
                return result;
            }
        }

        /// <summary>
        /// Deserializes an object from a JSON string.
        /// </summary>
        /// <typeparam name="T">The type to deserialize to.</typeparam>
        /// <param name="json">The JSON string to deserialize.</param>
        /// <returns>A <see cref="SerializationResult{T}"/> containing the deserialized object or error information.</returns>
        public SerializationResult<T> DeserializeFromJson<T>(string json) where T : class
        {
            if (string.IsNullOrEmpty(json))
            {
                var result = SerializationResult<T>.NullInput();
                Diagnostics.Log("JsonDeserializationError", new { Type = typeof(T).Name, Error = result.ErrorMessage });
                return result;
            }

            try
            {
                T? value = JsonSerializer.Deserialize<T>(json, _jsonOptions);
                if (value == null)
                {
                    var result = SerializationResult<T>.InvalidFormat("Deserialization returned null");
                    Diagnostics.Log("JsonDeserializationError", new { Type = typeof(T).Name, Error = result.ErrorMessage });
                    return result;
                }
                return SerializationResult<T>.Success(value);
            }
            catch (JsonException ex)
            {
                var result = SerializationResult<T>.InvalidFormat(ex.Message, ex);
                Diagnostics.Log("JsonDeserializationError", new { Type = typeof(T).Name, Error = result.ErrorMessage });
                return result;
            }
            catch (Exception ex)
            {
                var result = SerializationResult<T>.UnknownError(ex.Message, ex);
                Diagnostics.Log("JsonDeserializationError", new { Type = typeof(T).Name, Error = result.ErrorMessage });
                return result;
            }
        }

        /// <summary>
        /// Serializes an object to CBOR binary format.
        /// </summary>
        /// <typeparam name="T">The type of the object to serialize.</typeparam>
        /// <param name="obj">The object to serialize.</param>
        /// <returns>A <see cref="SerializationResult{T}"/> containing the CBOR binary data or error information.</returns>
        public SerializationResult<byte[]> SerializeToCbor<T>(T obj) where T : class
        {
            if (obj == null)
            {
                var result = SerializationResult<byte[]>.NullInput();
                Diagnostics.Log("CborSerializationError", new { Type = typeof(T).Name, Error = result.ErrorMessage });
                return result;
            }

            try
            {
                // First serialize to JSON, then convert to CBOR
                // This ensures consistent serialization behavior between JSON and CBOR
                string json = JsonSerializer.Serialize(obj, _jsonOptions);
                using var jsonDocument = JsonDocument.Parse(json);

                var writer = new CborWriter();
                WriteCborFromJsonElement(writer, jsonDocument.RootElement);
                byte[] cbor = writer.Encode();

                return SerializationResult<byte[]>.Success(cbor);
            }
            catch (Exception ex)
            {
                var result = SerializationResult<byte[]>.UnknownError(ex.Message, ex);
                Diagnostics.Log("CborSerializationError", new { Type = typeof(T).Name, Error = result.ErrorMessage });
                return result;
            }
        }

        /// <summary>
        /// Deserializes an object from CBOR binary data.
        /// </summary>
        /// <typeparam name="T">The type to deserialize to.</typeparam>
        /// <param name="cbor">The CBOR binary data to deserialize.</param>
        /// <returns>A <see cref="SerializationResult{T}"/> containing the deserialized object or error information.</returns>
        public SerializationResult<T> DeserializeFromCbor<T>(byte[] cbor) where T : class
        {
            if (cbor == null || cbor.Length == 0)
            {
                var result = SerializationResult<T>.NullInput();
                Diagnostics.Log("CborDeserializationError", new { Type = typeof(T).Name, Error = result.ErrorMessage });
                return result;
            }

            try
            {
                // Convert CBOR to JSON, then deserialize using JSON deserializer
                // This ensures consistent deserialization behavior between JSON and CBOR
                var reader = new CborReader(cbor);
                using var jsonDocument = ReadJsonFromCbor(reader);
                string json = JsonSerializer.Serialize(jsonDocument, _jsonOptions);

                T? value = JsonSerializer.Deserialize<T>(json, _jsonOptions);
                if (value == null)
                {
                    var result = SerializationResult<T>.InvalidFormat("CBOR deserialization returned null");
                    Diagnostics.Log("CborDeserializationError", new { Type = typeof(T).Name, Error = result.ErrorMessage });
                    return result;
                }
                return SerializationResult<T>.Success(value);
            }
            catch (Exception ex)
            {
                var result = SerializationResult<T>.InvalidFormat(ex.Message, ex);
                Diagnostics.Log("CborDeserializationError", new { Type = typeof(T).Name, Error = result.ErrorMessage });
                return result;
            }
        }

        /// <summary>
        /// Serializes an object to a JSON string using the legacy approach (for backward compatibility).
        /// </summary>
        /// <typeparam name="T">The type of the object to serialize.</typeparam>
        /// <param name="obj">The object to serialize.</param>
        /// <returns>A JSON string representation of the object, or an empty string if serialization fails.</returns>
        public string SerializeToJsonLegacy<T>(T obj) where T : class
        {
            if (obj == null)
            {
                Diagnostics.Log("JsonSerializationError", new { Type = typeof(T).Name, Error = "Object is null" });
                return string.Empty;
            }

            try
            {
                return JsonSerializer.Serialize(obj, _jsonOptions);
            }
            catch (Exception ex)
            {
                Diagnostics.Log("JsonSerializationError", new { Type = typeof(T).Name, Error = ex.Message });
                return string.Empty;
            }
        }

        /// <summary>
        /// Deserializes an object from a JSON string using the legacy approach (for backward compatibility).
        /// </summary>
        /// <typeparam name="T">The type to deserialize to.</typeparam>
        /// <param name="json">The JSON string to deserialize.</param>
        /// <returns>The deserialized object, or null if deserialization fails.</returns>
        public T? DeserializeFromJsonLegacy<T>(string json) where T : class
        {
            if (string.IsNullOrEmpty(json))
            {
                Diagnostics.Log("JsonDeserializationError", new { Type = typeof(T).Name, Error = "JSON string is null or empty" });
                return null;
            }

            try
            {
                return JsonSerializer.Deserialize<T>(json, _jsonOptions);
            }
            catch (Exception ex)
            {
                Diagnostics.Log("JsonDeserializationError", new { Type = typeof(T).Name, Error = ex.Message });
                return null;
            }
        }

        /// <summary>
        /// Serializes an object to CBOR binary format using the legacy approach (for backward compatibility).
        /// </summary>
        /// <typeparam name="T">The type of the object to serialize.</typeparam>
        /// <param name="obj">The object to serialize.</param>
        /// <returns>A byte array containing the CBOR representation of the object, or an empty array if serialization fails.</returns>
        public byte[] SerializeToCborLegacy<T>(T obj) where T : class
        {
            if (obj == null)
            {
                Diagnostics.Log("CborSerializationError", new { Type = typeof(T).Name, Error = "Object is null" });
                return Array.Empty<byte>();
            }

            try
            {
                string json = JsonSerializer.Serialize(obj, _jsonOptions);
                using var jsonDocument = JsonDocument.Parse(json);

                var writer = new CborWriter();
                WriteCborFromJsonElement(writer, jsonDocument.RootElement);
                return writer.Encode();
            }
            catch (Exception ex)
            {
                Diagnostics.Log("CborSerializationError", new { Type = typeof(T).Name, Error = ex.Message });
                return Array.Empty<byte>();
            }
        }

        /// <summary>
        /// Deserializes an object from CBOR binary data using the legacy approach (for backward compatibility).
        /// </summary>
        /// <typeparam name="T">The type to deserialize to.</typeparam>
        /// <param name="cbor">The CBOR binary data to deserialize.</param>
        /// <returns>The deserialized object, or null if deserialization fails.</returns>
        public T? DeserializeFromCborLegacy<T>(byte[] cbor) where T : class
        {
            if (cbor == null || cbor.Length == 0)
            {
                Diagnostics.Log("CborDeserializationError", new { Type = typeof(T).Name, Error = "CBOR data is null or empty" });
                return null;
            }

            try
            {
                var reader = new CborReader(cbor);
                using var jsonDocument = ReadJsonFromCbor(reader);
                string json = JsonSerializer.Serialize(jsonDocument, _jsonOptions);
                return JsonSerializer.Deserialize<T>(json, _jsonOptions);
            }
            catch (Exception ex)
            {
                Diagnostics.Log("CborDeserializationError", new { Type = typeof(T).Name, Error = ex.Message });
                return null;
            }
        }

        /// <summary>
        /// Writes a JsonElement to a CborWriter.
        /// </summary>
        /// <param name="writer">The CBOR writer.</param>
        /// <param name="element">The JSON element to write.</param>
        private static void WriteCborFromJsonElement(CborWriter writer, JsonElement element)
        {
            switch (element.ValueKind)
            {
                case JsonValueKind.Object:
                    writer.WriteStartMap(element.EnumerateObject().Count());
                    foreach (var property in element.EnumerateObject())
                    {
                        writer.WriteTextString(property.Name);
                        WriteCborFromJsonElement(writer, property.Value);
                    }
                    writer.WriteEndMap();
                    break;

                case JsonValueKind.Array:
                    writer.WriteStartArray(element.GetArrayLength());
                    foreach (var item in element.EnumerateArray())
                    {
                        WriteCborFromJsonElement(writer, item);
                    }
                    writer.WriteEndArray();
                    break;

                case JsonValueKind.String:
                    writer.WriteTextString(element.GetString()!);
                    break;

                case JsonValueKind.Number:
                    if (element.TryGetInt64(out long longValue))
                    {
                        writer.WriteInt64(longValue);
                    }
                    else
                    {
                        writer.WriteDouble(element.GetDouble());
                    }
                    break;

                case JsonValueKind.True:
                    writer.WriteBoolean(true);
                    break;

                case JsonValueKind.False:
                    writer.WriteBoolean(false);
                    break;

                case JsonValueKind.Null:
                    writer.WriteNull();
                    break;

                default:
                    throw new ArgumentException($"Unsupported JSON value kind: {element.ValueKind}");
            }
        }

        /// <summary>
        /// Reads CBOR data and converts it to a JsonDocument.
        /// </summary>
        /// <param name="reader">The CBOR reader.</param>
        /// <returns>A JsonDocument representing the CBOR data.</returns>
        private static JsonDocument ReadJsonFromCbor(CborReader reader)
        {
            using var stream = new MemoryStream();
            using var jsonWriter = new Utf8JsonWriter(stream);

            WriteJsonFromCbor(jsonWriter, reader);
            jsonWriter.Flush();

            stream.Position = 0;
            return JsonDocument.Parse(stream);
        }

        /// <summary>
        /// Writes CBOR data to a Utf8JsonWriter.
        /// </summary>
        /// <param name="writer">The JSON writer.</param>
        /// <param name="reader">The CBOR reader.</param>
        private static void WriteJsonFromCbor(Utf8JsonWriter writer, CborReader reader)
        {
            switch (reader.PeekState())
            {
                case CborReaderState.StartMap:
                    writer.WriteStartObject();
                    reader.ReadStartMap();
                    while (reader.PeekState() != CborReaderState.EndMap)
                    {
                        string propertyName = reader.ReadTextString();
                        writer.WritePropertyName(propertyName);
                        WriteJsonFromCbor(writer, reader);
                    }
                    reader.ReadEndMap();
                    writer.WriteEndObject();
                    break;

                case CborReaderState.StartArray:
                    writer.WriteStartArray();
                    reader.ReadStartArray();
                    while (reader.PeekState() != CborReaderState.EndArray)
                    {
                        WriteJsonFromCbor(writer, reader);
                    }
                    reader.ReadEndArray();
                    writer.WriteEndArray();
                    break;

                case CborReaderState.TextString:
                    writer.WriteStringValue(reader.ReadTextString());
                    break;

                case CborReaderState.UnsignedInteger:
                    writer.WriteNumberValue(reader.ReadUInt64());
                    break;

                case CborReaderState.NegativeInteger:
                    writer.WriteNumberValue(reader.ReadInt64());
                    break;

                case CborReaderState.SinglePrecisionFloat:
                case CborReaderState.DoublePrecisionFloat:
                    writer.WriteNumberValue(reader.ReadDouble());
                    break;

                case CborReaderState.Boolean:
                    writer.WriteBooleanValue(reader.ReadBoolean());
                    break;

                case CborReaderState.Null:
                    reader.ReadNull();
                    writer.WriteNullValue();
                    break;

                default:
                    throw new ArgumentException($"Unsupported CBOR state: {reader.PeekState()}");
            }
        }
    }

    /// <summary>
    /// Custom JSON converter for Dictionary&lt;string, object&gt; to handle JsonElement conversion.
    /// </summary>
    public class ObjectDictionaryConverter : JsonConverter<Dictionary<string, object>>
    {
        public override Dictionary<string, object> Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            var dictionary = new Dictionary<string, object>();

            if (reader.TokenType != JsonTokenType.StartObject)
            {
                throw new JsonException("Expected StartObject token");
            }

            while (reader.Read())
            {
                if (reader.TokenType == JsonTokenType.EndObject)
                {
                    return dictionary;
                }

                if (reader.TokenType != JsonTokenType.PropertyName)
                {
                    throw new JsonException("Expected PropertyName token");
                }

                string propertyName = reader.GetString()!;
                reader.Read();

                object value = reader.TokenType switch
                {
                    JsonTokenType.String => reader.GetString()!,
                    JsonTokenType.Number => reader.TryGetInt64(out long l) ? l : reader.GetDouble(),
                    JsonTokenType.True => true,
                    JsonTokenType.False => false,
                    JsonTokenType.Null => null!,
                    _ => JsonSerializer.Deserialize<object>(ref reader, options)!
                };

                dictionary[propertyName] = value;
            }

            throw new JsonException("Expected EndObject token");
        }

        public override void Write(Utf8JsonWriter writer, Dictionary<string, object> value, JsonSerializerOptions options)
        {
            writer.WriteStartObject();

            foreach (var kvp in value)
            {
                writer.WritePropertyName(kvp.Key);
                JsonSerializer.Serialize(writer, kvp.Value, options);
            }

            writer.WriteEndObject();
        }
    }
}