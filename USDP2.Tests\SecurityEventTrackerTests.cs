using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;

namespace USDP2.Tests
{
    /// <summary>
    /// Tests for the security event tracking system that monitors and analyzes security events
    /// to detect attack patterns and provide comprehensive security monitoring.
    /// 
    /// These tests verify that the security tracker correctly:
    /// - Records and categorizes security events
    /// - Calculates threat scores and detects suspicious patterns
    /// - Provides accurate security statistics
    /// - <PERSON>les endpoint blocking and unblocking
    /// - Detects coordinated attacks across multiple endpoints
    /// </summary>
    [TestClass]
    public class SecurityEventTrackerTests
    {
        private UsdpConfiguration _testConfig = null!;
        private SecurityEventTracker _securityTracker = null!;

        [TestInitialize]
        public void Setup()
        {
            _testConfig = new UsdpConfiguration(forTesting: true)
            {
                EnableEnhancedDosProtection = true,
                SuspiciousMessageSizeThreshold = 0.8
            };

            _securityTracker = new SecurityEventTracker(_testConfig);
        }

        [TestCleanup]
        public void Cleanup()
        {
            _securityTracker?.Dispose();
        }

        [TestMethod]
        public void RecordEvent_ValidEvent_ShouldStoreEvent()
        {
            // Arrange
            var remoteAddress = "*************";
            var remotePort = 12345;
            var eventType = SecurityEventType.RateLimitExceeded;
            var details = "Test rate limit exceeded";
            var severity = SecurityEventSeverity.Medium;

            // Act
            _securityTracker.RecordEvent(eventType, remoteAddress, remotePort, details, severity);

            // Assert
            var stats = _securityTracker.GetSecurityStatistics();
            Assert.AreEqual(1, stats.TotalEndpoints);
            Assert.AreEqual(1, stats.RecentEventsCount);
            Assert.IsTrue(stats.EventsByType.ContainsKey(eventType));
            Assert.AreEqual(1, stats.EventsByType[eventType]);
            Assert.IsTrue(stats.EventsBySeverity.ContainsKey(severity));
            Assert.AreEqual(1, stats.EventsBySeverity[severity]);
        }

        [TestMethod]
        public void RecordEvent_MultipleEvents_ShouldTrackCorrectly()
        {
            // Arrange
            var remoteAddress = "*************";
            var remotePort = 12345;

            // Act
            _securityTracker.RecordEvent(SecurityEventType.RateLimitExceeded, remoteAddress, remotePort, "Event 1", SecurityEventSeverity.Medium);
            _securityTracker.RecordEvent(SecurityEventType.OversizedMessage, remoteAddress, remotePort, "Event 2", SecurityEventSeverity.High);
            _securityTracker.RecordEvent(SecurityEventType.InvalidData, remoteAddress, remotePort, "Event 3", SecurityEventSeverity.Low);

            // Assert
            var stats = _securityTracker.GetSecurityStatistics();
            Assert.AreEqual(1, stats.TotalEndpoints);
            Assert.AreEqual(3, stats.RecentEventsCount);
            Assert.AreEqual(3, stats.EventsByType.Count);
            Assert.AreEqual(3, stats.EventsBySeverity.Count);
        }

        [TestMethod]
        public void RecordEvent_MultipleEndpoints_ShouldTrackSeparately()
        {
            // Arrange
            var endpoint1 = "*************";
            var endpoint2 = "*************";
            var port = 12345;

            // Act
            _securityTracker.RecordEvent(SecurityEventType.RateLimitExceeded, endpoint1, port, "Event from endpoint1", SecurityEventSeverity.Medium);
            _securityTracker.RecordEvent(SecurityEventType.OversizedMessage, endpoint2, port, "Event from endpoint2", SecurityEventSeverity.High);

            // Assert
            var stats = _securityTracker.GetSecurityStatistics();
            Assert.AreEqual(2, stats.TotalEndpoints);
            Assert.AreEqual(2, stats.RecentEventsCount);
        }

        [TestMethod]
        public void RecordEvent_CriticalEvents_ShouldTriggerImmediateThreatCheck()
        {
            // Arrange
            var remoteAddress = "*************";
            var remotePort = 12345;

            // Act - Send multiple critical events rapidly
            for (int i = 0; i < 6; i++) // More than the threshold of 5
            {
                _securityTracker.RecordEvent(SecurityEventType.SecurityViolation, remoteAddress, remotePort, 
                    $"Critical event {i}", SecurityEventSeverity.Critical);
            }

            // Assert
            Assert.IsTrue(_securityTracker.IsEndpointBlocked(remoteAddress, remotePort), 
                "Endpoint should be blocked after multiple critical events");
        }

        [TestMethod]
        public void BlockEndpoint_ValidEndpoint_ShouldBlockSuccessfully()
        {
            // Arrange
            var remoteAddress = "*************";
            var remotePort = 12345;
            var reason = "Manual block for testing";
            var duration = TimeSpan.FromMinutes(5);

            // Act
            _securityTracker.BlockEndpoint(remoteAddress, remotePort, reason, duration);

            // Assert
            Assert.IsTrue(_securityTracker.IsEndpointBlocked(remoteAddress, remotePort));
            
            var stats = _securityTracker.GetSecurityStatistics();
            Assert.AreEqual(1, stats.BlockedEndpoints);
        }

        [TestMethod]
        public void UnblockEndpoint_BlockedEndpoint_ShouldUnblockSuccessfully()
        {
            // Arrange
            var remoteAddress = "*************";
            var remotePort = 12345;
            
            _securityTracker.BlockEndpoint(remoteAddress, remotePort, "Test block", TimeSpan.FromMinutes(5));
            Assert.IsTrue(_securityTracker.IsEndpointBlocked(remoteAddress, remotePort));

            // Act
            _securityTracker.UnblockEndpoint(remoteAddress, remotePort);

            // Assert
            Assert.IsFalse(_securityTracker.IsEndpointBlocked(remoteAddress, remotePort));
            
            var stats = _securityTracker.GetSecurityStatistics();
            Assert.AreEqual(0, stats.BlockedEndpoints);
        }

        [TestMethod]
        public void IsEndpointBlocked_NonExistentEndpoint_ShouldReturnFalse()
        {
            // Arrange
            var remoteAddress = "*************";
            var remotePort = 12345;

            // Act & Assert
            Assert.IsFalse(_securityTracker.IsEndpointBlocked(remoteAddress, remotePort));
        }

        [TestMethod]
        public void GetRecentEvents_WithTimeWindow_ShouldReturnCorrectEvents()
        {
            // Arrange
            var remoteAddress = "*************";
            var remotePort = 12345;
            
            // Record events at different times
            _securityTracker.RecordEvent(SecurityEventType.RateLimitExceeded, remoteAddress, remotePort, "Old event", SecurityEventSeverity.Medium);
            
            // Act
            var recentEvents = _securityTracker.GetRecentEvents(TimeSpan.FromMinutes(1));

            // Assert
            Assert.AreEqual(1, recentEvents.Count);
            Assert.AreEqual(SecurityEventType.RateLimitExceeded, recentEvents[0].EventType);
            Assert.AreEqual("Old event", recentEvents[0].Details);
        }

        [TestMethod]
        public void GetSecurityStatistics_WithVariousEvents_ShouldCalculateCorrectHealthScore()
        {
            // Arrange
            var endpoint1 = "*************";
            var endpoint2 = "*************";
            var endpoint3 = "*************";
            var port = 12345;

            // Create a mix of events
            _securityTracker.RecordEvent(SecurityEventType.RateLimitExceeded, endpoint1, port, "Normal event", SecurityEventSeverity.Low);
            _securityTracker.RecordEvent(SecurityEventType.OversizedMessage, endpoint2, port, "Suspicious event", SecurityEventSeverity.High);
            _securityTracker.RecordEvent(SecurityEventType.SecurityViolation, endpoint3, port, "Critical event", SecurityEventSeverity.Critical);
            
            // Block one endpoint
            _securityTracker.BlockEndpoint(endpoint3, port, "Test block", null);

            // Act
            var stats = _securityTracker.GetSecurityStatistics();

            // Assert
            Assert.AreEqual(3, stats.TotalEndpoints);
            Assert.AreEqual(1, stats.BlockedEndpoints);
            Assert.IsTrue(stats.SecurityHealthScore < 100, "Health score should be reduced due to security events");
            Assert.IsTrue(stats.SecurityHealthScore >= 0, "Health score should not be negative");
        }

        [TestMethod]
        public void GetSecurityStatistics_NoEvents_ShouldReturnPerfectHealthScore()
        {
            // Act
            var stats = _securityTracker.GetSecurityStatistics();

            // Assert
            Assert.AreEqual(100, stats.SecurityHealthScore);
            Assert.AreEqual(0, stats.TotalEndpoints);
            Assert.AreEqual(0, stats.SuspiciousEndpoints);
            Assert.AreEqual(0, stats.BlockedEndpoints);
            Assert.AreEqual(0, stats.RecentEventsCount);
        }

        [TestMethod]
        public void GetSecurityStatistics_WithSuspiciousEndpoints_ShouldIdentifyThem()
        {
            // Arrange
            var suspiciousEndpoint = "*************";
            var normalEndpoint = "*************";
            var port = 12345;

            // Create many events for suspicious endpoint
            for (int i = 0; i < 10; i++)
            {
                _securityTracker.RecordEvent(SecurityEventType.ValidationFailure, suspiciousEndpoint, port, 
                    $"Suspicious event {i}", SecurityEventSeverity.High);
            }

            // Create few events for normal endpoint
            _securityTracker.RecordEvent(SecurityEventType.UnknownMessageType, normalEndpoint, port, 
                "Normal event", SecurityEventSeverity.Low);

            // Act
            var stats = _securityTracker.GetSecurityStatistics();

            // Assert
            Assert.AreEqual(2, stats.TotalEndpoints);
            Assert.IsTrue(stats.SuspiciousEndpoints > 0, "Should identify suspicious endpoints");
            Assert.IsTrue(stats.TopSuspiciousEndpoints.Count > 0, "Should list top suspicious endpoints");
            
            var topSuspicious = stats.TopSuspiciousEndpoints.First();
            Assert.IsTrue(topSuspicious.EndpointKey.Contains(suspiciousEndpoint), "Top suspicious should be the problematic endpoint");
            Assert.IsTrue(topSuspicious.ThreatScore > 0, "Threat score should be elevated");
        }

        [TestMethod]
        public async Task PerformSecurityAnalysis_WithTimeBasedEvents_ShouldDecayThreatScores()
        {
            // Arrange
            var remoteAddress = "*************";
            var remotePort = 12345;

            // Record some events to build up threat score
            for (int i = 0; i < 5; i++)
            {
                _securityTracker.RecordEvent(SecurityEventType.ValidationFailure, remoteAddress, remotePort, 
                    $"Event {i}", SecurityEventSeverity.High);
            }

            var initialStats = _securityTracker.GetSecurityStatistics();
            var initialThreatScore = initialStats.TopSuspiciousEndpoints.FirstOrDefault()?.ThreatScore ?? 0;

            // Act - Wait for analysis timer to run (this is challenging to test directly)
            // In a real scenario, threat scores would decay over time
            await Task.Delay(100); // Small delay to allow processing

            // Assert
            // Note: Testing time-based decay is challenging in unit tests
            // This test verifies the infrastructure is in place
            Assert.IsTrue(initialThreatScore >= 0, "Initial threat score should be non-negative");
        }

        [TestMethod]
        public void SecurityEventTypes_AllTypesSupported_ShouldHandleCorrectly()
        {
            // Arrange
            var remoteAddress = "*************";
            var remotePort = 12345;
            var allEventTypes = Enum.GetValues<SecurityEventType>();

            // Act - Record one event of each type
            foreach (var eventType in allEventTypes)
            {
                _securityTracker.RecordEvent(eventType, remoteAddress, remotePort, 
                    $"Test {eventType}", SecurityEventSeverity.Medium);
            }

            // Assert
            var stats = _securityTracker.GetSecurityStatistics();
            Assert.AreEqual(allEventTypes.Length, stats.RecentEventsCount);
            Assert.AreEqual(allEventTypes.Length, stats.EventsByType.Count);
            
            foreach (var eventType in allEventTypes)
            {
                Assert.IsTrue(stats.EventsByType.ContainsKey(eventType), $"Should track {eventType}");
                Assert.AreEqual(1, stats.EventsByType[eventType], $"Should have one event of type {eventType}");
            }
        }

        [TestMethod]
        public void SecurityEventSeverities_AllSeveritiesSupported_ShouldHandleCorrectly()
        {
            // Arrange
            var remoteAddress = "*************";
            var remotePort = 12345;
            var allSeverities = Enum.GetValues<SecurityEventSeverity>();

            // Act - Record one event of each severity
            int eventIndex = 0;
            foreach (var severity in allSeverities)
            {
                _securityTracker.RecordEvent(SecurityEventType.SecurityViolation, remoteAddress, remotePort, 
                    $"Test severity {severity}", severity);
                eventIndex++;
            }

            // Assert
            var stats = _securityTracker.GetSecurityStatistics();
            Assert.AreEqual(allSeverities.Length, stats.RecentEventsCount);
            Assert.AreEqual(allSeverities.Length, stats.EventsBySeverity.Count);
            
            foreach (var severity in allSeverities)
            {
                Assert.IsTrue(stats.EventsBySeverity.ContainsKey(severity), $"Should track {severity}");
                Assert.AreEqual(1, stats.EventsBySeverity[severity], $"Should have one event of severity {severity}");
            }
        }
    }
}
