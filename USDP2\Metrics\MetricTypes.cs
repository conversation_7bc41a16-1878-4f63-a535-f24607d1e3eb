using System;
using System.Collections.Generic;
using System.Linq;

namespace USDP2.Metrics
{
    /// <summary>
    /// Represents a counter metric that tracks cumulative values.
    /// </summary>
    public class CounterMetric
    {
        /// <summary>
        /// Gets or sets the metric name.
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the current value of the counter.
        /// </summary>
        public long Value { get; set; }

        /// <summary>
        /// Gets or sets the tags associated with this metric.
        /// </summary>
        public Dictionary<string, string> Tags { get; set; } = new();

        /// <summary>
        /// Gets or sets the timestamp when this metric was last updated.
        /// </summary>
        public DateTimeOffset LastUpdated { get; set; } = DateTimeOffset.UtcNow;

        /// <summary>
        /// Increments the counter by the specified value.
        /// </summary>
        /// <param name="increment">The value to add to the counter.</param>
        public void Increment(long increment = 1)
        {
            Value += increment;
            LastUpdated = DateTimeOffset.UtcNow;
        }
    }

    /// <summary>
    /// Represents a gauge metric that tracks point-in-time values.
    /// </summary>
    public class GaugeMetric
    {
        /// <summary>
        /// Gets or sets the metric name.
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the current value of the gauge.
        /// </summary>
        public double Value { get; set; }

        /// <summary>
        /// Gets or sets the tags associated with this metric.
        /// </summary>
        public Dictionary<string, string> Tags { get; set; } = new();

        /// <summary>
        /// Gets or sets the timestamp when this metric was last updated.
        /// </summary>
        public DateTimeOffset LastUpdated { get; set; } = DateTimeOffset.UtcNow;

        /// <summary>
        /// Sets the gauge to the specified value.
        /// </summary>
        /// <param name="value">The new value for the gauge.</param>
        public void Set(double value)
        {
            Value = value;
            LastUpdated = DateTimeOffset.UtcNow;
        }
    }

    /// <summary>
    /// Represents a histogram metric that tracks distribution of values.
    /// </summary>
    public class HistogramMetric
    {
        private readonly List<double> _values = new();
        private readonly object _lock = new();

        /// <summary>
        /// Gets or sets the metric name.
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the tags associated with this metric.
        /// </summary>
        public Dictionary<string, string> Tags { get; set; } = new();

        /// <summary>
        /// Gets or sets the timestamp when this metric was last updated.
        /// </summary>
        public DateTimeOffset LastUpdated { get; set; } = DateTimeOffset.UtcNow;

        /// <summary>
        /// Gets the number of recorded values.
        /// </summary>
        public int Count
        {
            get
            {
                lock (_lock)
                {
                    return _values.Count;
                }
            }
        }

        /// <summary>
        /// Gets the minimum recorded value.
        /// </summary>
        public double Min
        {
            get
            {
                lock (_lock)
                {
                    return _values.Count > 0 ? _values.Min() : 0;
                }
            }
        }

        /// <summary>
        /// Gets the maximum recorded value.
        /// </summary>
        public double Max
        {
            get
            {
                lock (_lock)
                {
                    return _values.Count > 0 ? _values.Max() : 0;
                }
            }
        }

        /// <summary>
        /// Gets the average of recorded values.
        /// </summary>
        public double Average
        {
            get
            {
                lock (_lock)
                {
                    return _values.Count > 0 ? _values.Average() : 0;
                }
            }
        }

        /// <summary>
        /// Gets the sum of all recorded values.
        /// </summary>
        public double Sum
        {
            get
            {
                lock (_lock)
                {
                    return _values.Sum();
                }
            }
        }

        /// <summary>
        /// Records a value in the histogram.
        /// </summary>
        /// <param name="value">The value to record.</param>
        public void Record(double value)
        {
            lock (_lock)
            {
                _values.Add(value);
                // Keep only the last 1000 values to prevent memory growth
                if (_values.Count > 1000)
                {
                    _values.RemoveAt(0);
                }
                LastUpdated = DateTimeOffset.UtcNow;
            }
        }

        /// <summary>
        /// Gets a percentile value from the recorded data.
        /// </summary>
        /// <param name="percentile">The percentile to calculate (0-100).</param>
        /// <returns>The value at the specified percentile.</returns>
        public double GetPercentile(double percentile)
        {
            if (percentile < 0 || percentile > 100)
                throw new ArgumentOutOfRangeException(nameof(percentile), "Percentile must be between 0 and 100");

            lock (_lock)
            {
                if (_values.Count == 0)
                    return 0;

                var sorted = _values.OrderBy(x => x).ToList();
                var index = (int)Math.Ceiling(percentile / 100.0 * sorted.Count) - 1;
                index = Math.Max(0, Math.Min(index, sorted.Count - 1));
                return sorted[index];
            }
        }

        /// <summary>
        /// Clears all recorded values.
        /// </summary>
        public void Clear()
        {
            lock (_lock)
            {
                _values.Clear();
                LastUpdated = DateTimeOffset.UtcNow;
            }
        }
    }

    /// <summary>
    /// Represents a timing metric that tracks duration measurements.
    /// </summary>
    public class TimingMetric : HistogramMetric
    {
        /// <summary>
        /// Records a timing value.
        /// </summary>
        /// <param name="duration">The duration to record.</param>
        public void RecordTiming(TimeSpan duration)
        {
            Record(duration.TotalMilliseconds);
        }

        /// <summary>
        /// Gets the average duration.
        /// </summary>
        public TimeSpan AverageDuration => TimeSpan.FromMilliseconds(Average);

        /// <summary>
        /// Gets the minimum duration.
        /// </summary>
        public TimeSpan MinDuration => TimeSpan.FromMilliseconds(Min);

        /// <summary>
        /// Gets the maximum duration.
        /// </summary>
        public TimeSpan MaxDuration => TimeSpan.FromMilliseconds(Max);

        /// <summary>
        /// Gets the total duration of all recorded timings.
        /// </summary>
        public TimeSpan TotalDuration => TimeSpan.FromMilliseconds(Sum);

        /// <summary>
        /// Gets a percentile duration from the recorded data.
        /// </summary>
        /// <param name="percentile">The percentile to calculate (0-100).</param>
        /// <returns>The duration at the specified percentile.</returns>
        public TimeSpan GetPercentileDuration(double percentile)
        {
            return TimeSpan.FromMilliseconds(GetPercentile(percentile));
        }
    }
}
