using System;
using System.Threading;
using System.Threading.Tasks;

namespace USDP2
{
    /// <summary>
    /// Interface for circuit breaker implementations that provide resilience patterns for network operations.
    /// This interface enables dependency injection and testing of circuit breaker functionality.
    /// </summary>
    public interface ICircuitBreaker
    {
        /// <summary>
        /// Gets the current state of the circuit breaker.
        /// </summary>
        CircuitBreakerState State { get; }

        /// <summary>
        /// Gets comprehensive statistics about the circuit breaker performance and state.
        /// </summary>
        /// <returns>Statistics including failure rates, state transitions, and timing information.</returns>
        CircuitBreakerStatistics GetStatistics();

        /// <summary>
        /// Executes an operation through the circuit breaker with automatic failure handling and timeout protection.
        /// </summary>
        /// <typeparam name="T">The return type of the operation.</typeparam>
        /// <param name="operation">The operation to execute. Should accept a CancellationToken and return a Task&lt;T&gt;.</param>
        /// <param name="cancellationToken">Cancellation token for the operation. Will be combined with circuit breaker timeout.</param>
        /// <returns>The result of the operation if successful.</returns>
        /// <exception cref="CircuitBreakerOpenException">Thrown when the circuit is open and operations are blocked.</exception>
        /// <exception cref="TimeoutException">Thrown when the operation exceeds the configured timeout.</exception>
        /// <exception cref="OperationCanceledException">Thrown when the operation is cancelled.</exception>
        Task<T> ExecuteAsync<T>(Func<CancellationToken, Task<T>> operation, CancellationToken cancellationToken = default);

        /// <summary>
        /// Executes an operation through the circuit breaker with automatic failure handling (void return).
        /// </summary>
        /// <param name="operation">The operation to execute. Should accept a CancellationToken and return a Task.</param>
        /// <param name="cancellationToken">Cancellation token for the operation. Will be combined with circuit breaker timeout.</param>
        /// <exception cref="CircuitBreakerOpenException">Thrown when the circuit is open and operations are blocked.</exception>
        /// <exception cref="TimeoutException">Thrown when the operation exceeds the configured timeout.</exception>
        /// <exception cref="OperationCanceledException">Thrown when the operation is cancelled.</exception>
        Task ExecuteAsync(Func<CancellationToken, Task> operation, CancellationToken cancellationToken = default);
    }
}
