using System;
using System.Collections.Generic;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using USDP2.Tests.Mocks;

namespace USDP2.Tests
{
    /// <summary>
    /// Tests for the enhanced security features in DirectoryNode including rate limiting,
    /// DoS protection, and security event tracking.
    /// 
    /// These tests verify that DirectoryNode correctly:
    /// - Integrates rate limiting into message processing
    /// - Blocks messages from blocked endpoints
    /// - Records security events during message processing
    /// - Provides security statistics and monitoring
    /// - Handles various attack scenarios gracefully
    /// </summary>
    [TestClass]
    public class DirectoryNodeSecurityTests
    {
        private UsdpConfiguration _testConfig = null!;
        private MockNetworkSender _mockSender = null!;
        private MockNetworkReceiver _mockReceiver = null!;
        private DirectoryNode _directoryNode = null!;

        [TestInitialize]
        public void Setup()
        {
            _testConfig = new UsdpConfiguration(forTesting: true)
            {
                EnableRateLimiting = true,
                RateLimitMaxMessagesPerWindow = 5,
                RateLimitTimeWindow = TimeSpan.FromSeconds(10),
                RateLimitBurstFactor = 2.0,
                EnableEnhancedDosProtection = true,
                MaxNetworkDataSize = 1024
            };

            _mockSender = new MockNetworkSender();
            _mockReceiver = new MockNetworkReceiver();
            _directoryNode = new DirectoryNode(_mockSender, _mockReceiver, _testConfig);
        }

        [TestCleanup]
        public async Task Cleanup()
        {
            await _directoryNode.DisposeAsync();
        }

        [TestMethod]
        public async Task OnMessageReceived_ValidMessage_ShouldProcessSuccessfully()
        {
            // Arrange
            var validMessage = CreateValidServiceAdvertisement();
            var messageData = validMessage.ToCbor();
            var remoteAddress = "*************";
            var remotePort = 12345;

            // Act
            await _mockReceiver.SimulateMessageReception(messageData, remoteAddress, remotePort);

            // Assert
            var securityStats = _directoryNode.GetSecurityStatistics();
            Assert.AreEqual(100, securityStats.SecurityHealthScore, "Health score should remain perfect for valid messages");
            Assert.AreEqual(0, securityStats.SuspiciousEndpoints, "No endpoints should be marked suspicious");
        }

        [TestMethod]
        public async Task OnMessageReceived_RateLimitExceeded_ShouldRejectMessages()
        {
            // Arrange
            var validMessage = CreateValidServiceAdvertisement();
            var messageData = validMessage.ToCbor();
            var remoteAddress = "*************";
            var remotePort = 12345;
            var burstLimit = (int)(_testConfig.RateLimitMaxMessagesPerWindow * _testConfig.RateLimitBurstFactor);

            // Act - Send messages up to burst limit
            for (int i = 0; i < burstLimit; i++)
            {
                await _mockReceiver.SimulateMessageReception(messageData, remoteAddress, remotePort);
            }

            // Send one more to exceed limit
            await _mockReceiver.SimulateMessageReception(messageData, remoteAddress, remotePort);

            // Assert
            var rateLimitStats = _directoryNode.GetRateLimitStatistics();
            var endpointKey = $"{remoteAddress}:{remotePort}";
            Assert.IsTrue(rateLimitStats.ContainsKey(endpointKey), "Endpoint should be tracked");
            Assert.IsTrue(rateLimitStats[endpointKey].RejectedMessages > 0, "Should have rejected messages");

            var securityStats = _directoryNode.GetSecurityStatistics();
            Assert.IsTrue(securityStats.EventsByType.ContainsKey(SecurityEventType.RateLimitExceeded),
                "Should record rate limit exceeded events");
        }

        [TestMethod]
        public async Task OnMessageReceived_BlockedEndpoint_ShouldRejectMessage()
        {
            // Arrange
            var validMessage = CreateValidServiceAdvertisement();
            var messageData = validMessage.ToCbor();
            var remoteAddress = "*************";
            var remotePort = 12345;

            // Block the endpoint
            _directoryNode.BlockEndpoint(remoteAddress, remotePort, "Test block", TimeSpan.FromMinutes(5));

            // Act
            await _mockReceiver.SimulateMessageReception(messageData, remoteAddress, remotePort);

            // Assert
            var securityStats = _directoryNode.GetSecurityStatistics();
            Assert.AreEqual(1, securityStats.BlockedEndpoints, "Endpoint should remain blocked");
            Assert.IsTrue(securityStats.EventsByType.ContainsKey(SecurityEventType.SecurityViolation),
                "Should record security violation for blocked endpoint");
        }

        [TestMethod]
        public async Task OnMessageReceived_OversizedMessage_ShouldRejectAndRecordEvent()
        {
            // Arrange
            var oversizedData = new byte[_testConfig.MaxNetworkDataSize + 100];
            Array.Fill(oversizedData, (byte)'A');
            var remoteAddress = "*************";
            var remotePort = 12345;

            // Act
            await _mockReceiver.SimulateMessageReception(oversizedData, remoteAddress, remotePort);

            // Assert
            var securityStats = _directoryNode.GetSecurityStatistics();
            Assert.IsTrue(securityStats.EventsByType.ContainsKey(SecurityEventType.OversizedMessage),
                "Should record oversized message event");
            Assert.IsTrue(securityStats.SecurityHealthScore < 100, "Health score should be reduced");
        }

        [TestMethod]
        public async Task OnMessageReceived_EmptyData_ShouldRejectAndRecordEvent()
        {
            // Arrange
            var emptyData = new byte[0];
            var remoteAddress = "*************";
            var remotePort = 12345;

            // Act
            await _mockReceiver.SimulateMessageReception(emptyData, remoteAddress, remotePort);

            // Assert
            var securityStats = _directoryNode.GetSecurityStatistics();
            Assert.IsTrue(securityStats.EventsByType.ContainsKey(SecurityEventType.InvalidData),
                "Should record invalid data event");
        }

        [TestMethod]
        public async Task OnMessageReceived_UnknownMessageType_ShouldRecordEvent()
        {
            // Arrange
            var unknownData = Encoding.UTF8.GetBytes("{\"unknown\": \"message type\"}");
            var remoteAddress = "*************";
            var remotePort = 12345;

            // Act
            await _mockReceiver.SimulateMessageReception(unknownData, remoteAddress, remotePort);

            // Assert
            var securityStats = _directoryNode.GetSecurityStatistics();
            Assert.IsTrue(securityStats.EventsByType.ContainsKey(SecurityEventType.UnknownMessageType),
                "Should record unknown message type event");
        }

        [TestMethod]
        public async Task OnMessageReceived_MaliciousData_ShouldRejectAndRecordEvent()
        {
            // Arrange
            var maliciousData = new byte[500];
            Array.Fill(maliciousData, (byte)0); // All null bytes - suspicious pattern
            var remoteAddress = "*************";
            var remotePort = 12345;

            // Act
            await _mockReceiver.SimulateMessageReception(maliciousData, remoteAddress, remotePort);

            // Assert
            var securityStats = _directoryNode.GetSecurityStatistics();
            Assert.IsTrue(securityStats.EventsByType.ContainsKey(SecurityEventType.InvalidData),
                "Should record invalid data event for malicious patterns");
        }

        [TestMethod]
        public void GetSecurityStatistics_AfterVariousEvents_ShouldProvideAccurateStats()
        {
            // Arrange - Create some security events
            _directoryNode.BlockEndpoint("*************", 12345, "Test block", null);

            // Act
            var stats = _directoryNode.GetSecurityStatistics();

            // Assert
            Assert.IsNotNull(stats, "Security statistics should be available");
            Assert.AreEqual(1, stats.BlockedEndpoints, "Should track blocked endpoints");
            Assert.IsTrue(stats.SecurityHealthScore >= 0 && stats.SecurityHealthScore <= 100,
                "Health score should be in valid range");
        }

        [TestMethod]
        public void GetRateLimitStatistics_AfterMessages_ShouldProvideAccurateStats()
        {
            // Arrange - This will be populated after message processing
            // Act
            var stats = _directoryNode.GetRateLimitStatistics();

            // Assert
            Assert.IsNotNull(stats, "Rate limit statistics should be available");
            // Initially empty since no messages have been processed
            Assert.AreEqual(0, stats.Count, "Should start with no tracked endpoints");
        }

        [TestMethod]
        public void BlockEndpoint_ValidEndpoint_ShouldBlockSuccessfully()
        {
            // Arrange
            var remoteAddress = "*************";
            var remotePort = 12345;
            var reason = "Test blocking";
            var duration = TimeSpan.FromMinutes(10);

            // Act
            _directoryNode.BlockEndpoint(remoteAddress, remotePort, reason, duration);

            // Assert
            var stats = _directoryNode.GetSecurityStatistics();
            Assert.AreEqual(1, stats.BlockedEndpoints, "Endpoint should be blocked");
        }

        [TestMethod]
        public void UnblockEndpoint_BlockedEndpoint_ShouldUnblockSuccessfully()
        {
            // Arrange
            var remoteAddress = "*************";
            var remotePort = 12345;

            _directoryNode.BlockEndpoint(remoteAddress, remotePort, "Test block", TimeSpan.FromMinutes(5));
            var initialStats = _directoryNode.GetSecurityStatistics();
            Assert.AreEqual(1, initialStats.BlockedEndpoints, "Endpoint should be blocked initially");

            // Act
            _directoryNode.UnblockEndpoint(remoteAddress, remotePort);

            // Assert
            var finalStats = _directoryNode.GetSecurityStatistics();
            Assert.AreEqual(0, finalStats.BlockedEndpoints, "Endpoint should be unblocked");
        }

        [TestMethod]
        public async Task OnMessageReceived_ExceptionDuringProcessing_ShouldRecordSecurityEvent()
        {
            // Arrange
            var invalidData = new byte[] { 0xFF, 0xFE, 0xFD }; // Invalid data that might cause exceptions
            var remoteAddress = "*************";
            var remotePort = 12345;

            // Act
            await _mockReceiver.SimulateMessageReception(invalidData, remoteAddress, remotePort);

            // Assert
            var securityStats = _directoryNode.GetSecurityStatistics();
            Assert.IsTrue(securityStats.RecentEventsCount > 0, "Should record security events");
        }

        [TestMethod]
        public async Task OnMessageReceived_MultipleEndpoints_ShouldTrackSeparately()
        {
            // Arrange
            var validMessage = CreateValidServiceAdvertisement();
            var messageData = validMessage.ToCbor();
            var endpoints = new[]
            {
                ("*************", 12345),
                ("*************", 12346),
                ("********", 8080)
            };

            // Act
            foreach (var (address, port) in endpoints)
            {
                await _mockReceiver.SimulateMessageReception(messageData, address, port);
            }

            // Assert
            var rateLimitStats = _directoryNode.GetRateLimitStatistics();
            Assert.AreEqual(endpoints.Length, rateLimitStats.Count, "Should track each endpoint separately");

            foreach (var (address, port) in endpoints)
            {
                var endpointKey = $"{address}:{port}";
                Assert.IsTrue(rateLimitStats.ContainsKey(endpointKey), $"Should track endpoint {endpointKey}");
                Assert.AreEqual(1, rateLimitStats[endpointKey].TotalMessages, "Each endpoint should have one message");
            }
        }

        /// <summary>
        /// Creates a valid service advertisement for testing.
        /// </summary>
        /// <returns>A valid ServiceAdvertisement instance.</returns>
        private static ServiceAdvertisement CreateValidServiceAdvertisement()
        {
            var serviceId = new ServiceIdentifier("test", "security-test-service");
            var endpoint = new TransportEndpoint
            {
                Protocol = "http",
                Address = "127.0.0.1",
                Port = 8080,
                Security = "none"
            };

            return new ServiceAdvertisement(serviceId, endpoint)
            {
                Metadata = new Dictionary<string, object>
                {
                    ["version"] = "1.0",
                    ["description"] = "Test service for security validation"
                },
                Ttl = TimeSpan.FromMinutes(30)
            };
        }
    }
}
