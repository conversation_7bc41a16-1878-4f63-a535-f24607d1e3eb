using System;
using System.Buffers;
using System.Net;
using System.Threading;
using System.Threading.Tasks;

namespace USDP2
{
    /// <summary>
    /// High-performance HTTP network receiver that implements zero-allocation message processing.
    /// This receiver uses buffer pooling and span-based APIs to minimize memory allocations and GC pressure.
    /// </summary>
    public class OptimizedHttpNetworkReceiver : IOptimizedNetworkReceiver, IAsyncDisposable
    {
        private readonly HttpListener _listener;
        private readonly ArrayPool<byte> _bufferPool;
        private bool _disposed;

        /// <summary>
        /// Initializes a new instance of the <see cref="OptimizedHttpNetworkReceiver"/> class.
        /// </summary>
        /// <param name="endpoint">The HTTP endpoint to listen on.</param>
        /// <param name="bufferPool">Optional custom buffer pool. If null, uses ArrayPool&lt;byte&gt;.Shared.</param>
        public OptimizedHttpNetworkReceiver(string endpoint, ArrayPool<byte>? bufferPool = null)
        {
            _listener = new HttpListener();
            _listener.Prefixes.Add(endpoint);
            _bufferPool = bufferPool ?? ArrayPool<byte>.Shared;
        }

        /// <inheritdoc />
        public async Task StartReceivingAsync(Func<byte[], string, int, Task> onMessageReceived, CancellationToken cancellationToken = default)
        {
            // Convert array-based callback to span-based for internal processing
            var spanCallback = onMessageReceived.ToSpanCallback();
            await StartReceivingOptimizedAsync(spanCallback, cancellationToken).ConfigureAwait(false);
        }

        /// <inheritdoc />
        public async Task StartReceivingOptimizedAsync(Func<ReadOnlySpan<byte>, string, int, Task> onMessageReceived, CancellationToken cancellationToken = default)
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(OptimizedHttpNetworkReceiver));

            if (onMessageReceived == null)
                throw new ArgumentNullException(nameof(onMessageReceived));

            _listener.Start();

            try
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    var context = await _listener.GetContextAsync().ConfigureAwait(false);
                    
                    // Process each request in a separate task to avoid blocking
                    _ = Task.Run(async () =>
                    {
                        await ProcessRequestOptimizedAsync(context, onMessageReceived, cancellationToken).ConfigureAwait(false);
                    }, cancellationToken);
                }
            }
            catch (ObjectDisposedException)
            {
                // Expected when stopping
            }
            catch (HttpListenerException ex) when (ex.ErrorCode == 995) // ERROR_OPERATION_ABORTED
            {
                // Expected when cancelling
            }
            finally
            {
                _listener.Stop();
            }
        }

        /// <inheritdoc />
        public async Task StartReceivingOptimizedAsync(Func<ReadOnlySpan<byte>, NetworkMessageMetadata, Task> onMessageReceived, CancellationToken cancellationToken = default)
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(OptimizedHttpNetworkReceiver));

            if (onMessageReceived == null)
                throw new ArgumentNullException(nameof(onMessageReceived));

            _listener.Start();

            try
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    var context = await _listener.GetContextAsync().ConfigureAwait(false);
                    
                    // Process each request in a separate task to avoid blocking
                    _ = Task.Run(async () =>
                    {
                        await ProcessRequestOptimizedWithMetadataAsync(context, onMessageReceived, cancellationToken).ConfigureAwait(false);
                    }, cancellationToken);
                }
            }
            catch (ObjectDisposedException)
            {
                // Expected when stopping
            }
            catch (HttpListenerException ex) when (ex.ErrorCode == 995) // ERROR_OPERATION_ABORTED
            {
                // Expected when cancelling
            }
            finally
            {
                _listener.Stop();
            }
        }

        /// <summary>
        /// Processes an HTTP request with optimized span-based message handling.
        /// </summary>
        /// <param name="context">The HTTP listener context.</param>
        /// <param name="onMessageReceived">The span-based message callback.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        private async Task ProcessRequestOptimizedAsync(
            HttpListenerContext context, 
            Func<ReadOnlySpan<byte>, string, int, Task> onMessageReceived,
            CancellationToken cancellationToken)
        {
            var request = context.Request;
            var response = context.Response;

            try
            {
                // Validate and limit buffer size to prevent overflow and memory exhaustion
                const int MaxBufferSize = 1024 * 1024; // 1MB limit
                var contentLength = request.ContentLength64;
                
                // Validate content length
                if (contentLength < 0)
                {
                    response.StatusCode = 400; // Bad Request
                    response.Close();
                    return;
                }
                
                // Prevent integer overflow and limit maximum size
                var bufferSize = (int)Math.Min(Math.Min(contentLength, MaxBufferSize), int.MaxValue);
                var buffer = _bufferPool.Rent(bufferSize);
                
                try
                {
                    int bytesRead = await request.InputStream.ReadAsync(buffer.AsMemory(0, bufferSize), cancellationToken).ConfigureAwait(false);
                    
                    var remoteIp = context.Request.RemoteEndPoint?.Address.ToString() ?? "";
                    var remotePort = context.Request.RemoteEndPoint?.Port ?? 0;

                    // Process the message with zero-allocation span-based approach
                    var processingResult = await ProcessReceivedMessageOptimizedAsync(
                        buffer.AsSpan(0, bytesRead), // Pass span directly - no allocation!
                        remoteIp, 
                        remotePort, 
                        onMessageReceived).ConfigureAwait(false);

                    response.StatusCode = processingResult.Success ? 200 : 400;
                }
                finally
                {
                    _bufferPool.Return(buffer);
                }
            }
            catch (Exception ex)
            {
                Diagnostics.Log("OptimizedHttpReceiver.ProcessingError", new { Error = ex.Message });
                response.StatusCode = 500;
            }
            finally
            {
                response.Close();
            }
        }

        /// <summary>
        /// Processes an HTTP request with optimized span-based message handling and metadata.
        /// </summary>
        /// <param name="context">The HTTP listener context.</param>
        /// <param name="onMessageReceived">The span-based message callback with metadata.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        private async Task ProcessRequestOptimizedWithMetadataAsync(
            HttpListenerContext context, 
            Func<ReadOnlySpan<byte>, NetworkMessageMetadata, Task> onMessageReceived,
            CancellationToken cancellationToken)
        {
            var request = context.Request;
            var response = context.Response;

            try
            {
                // Validate and limit buffer size
                const int MaxBufferSize = 1024 * 1024; // 1MB limit
                var contentLength = request.ContentLength64;
                
                if (contentLength < 0)
                {
                    response.StatusCode = 400;
                    response.Close();
                    return;
                }
                
                var bufferSize = (int)Math.Min(Math.Min(contentLength, MaxBufferSize), int.MaxValue);
                var buffer = _bufferPool.Rent(bufferSize);
                
                try
                {
                    int bytesRead = await request.InputStream.ReadAsync(buffer.AsMemory(0, bufferSize), cancellationToken).ConfigureAwait(false);
                    
                    var remoteIp = context.Request.RemoteEndPoint?.Address.ToString() ?? "";
                    var remotePort = context.Request.RemoteEndPoint?.Port ?? 0;

                    // Create metadata
                    var metadata = new NetworkMessageMetadata(
                        remoteIp, 
                        remotePort, 
                        DateTimeOffset.UtcNow,
                        "HTTP",
                        request.IsSecureConnection,
                        bytesRead);

                    // Process with metadata - still zero allocation for message data
                    await ProcessReceivedMessageOptimizedWithMetadataAsync(
                        buffer.AsSpan(0, bytesRead),
                        metadata,
                        onMessageReceived).ConfigureAwait(false);

                    response.StatusCode = 200;
                }
                finally
                {
                    _bufferPool.Return(buffer);
                }
            }
            catch (Exception ex)
            {
                Diagnostics.Log("OptimizedHttpReceiver.ProcessingError", new { Error = ex.Message });
                response.StatusCode = 500;
            }
            finally
            {
                response.Close();
            }
        }

        /// <summary>
        /// Processes a received message with zero-allocation span-based approach.
        /// </summary>
        /// <param name="messageSpan">The message data as a read-only span.</param>
        /// <param name="remoteIp">The remote IP address.</param>
        /// <param name="remotePort">The remote port.</param>
        /// <param name="onMessageReceived">The message processing callback.</param>
        /// <returns>A processing result indicating success or failure.</returns>
        private static async Task<MessageProcessingResult> ProcessReceivedMessageOptimizedAsync(
            ReadOnlySpan<byte> messageSpan,
            string remoteIp, 
            int remotePort, 
            Func<ReadOnlySpan<byte>, string, int, Task> onMessageReceived)
        {
            try
            {
                // Validate input parameters
                if (messageSpan.IsEmpty)
                {
                    Diagnostics.Log("MessageProcessingError", new 
                    { 
                        RemoteAddress = remoteIp, 
                        RemotePort = remotePort, 
                        Error = "Received empty message data",
                        ErrorType = "ValidationError"
                    });
                    return new MessageProcessingResult(false, "Empty message data");
                }

                if (string.IsNullOrEmpty(remoteIp))
                {
                    Diagnostics.Log("MessageProcessingError", new 
                    { 
                        RemoteAddress = remoteIp, 
                        RemotePort = remotePort, 
                        Error = "Invalid remote IP address",
                        ErrorType = "ValidationError"
                    });
                    return new MessageProcessingResult(false, "Invalid remote IP");
                }

                // Process the message directly with the span - zero allocation!
                await onMessageReceived(messageSpan, remoteIp, remotePort).ConfigureAwait(false);
                
                return new MessageProcessingResult(true, "Message processed successfully");
            }
            catch (Exception ex)
            {
                Diagnostics.Log("MessageProcessingError", new 
                { 
                    RemoteAddress = remoteIp, 
                    RemotePort = remotePort, 
                    Error = ex.Message,
                    ErrorType = ex.GetType().Name
                });
                return new MessageProcessingResult(false, $"Processing error: {ex.Message}");
            }
        }

        /// <summary>
        /// Processes a received message with metadata using zero-allocation approach.
        /// </summary>
        /// <param name="messageSpan">The message data as a read-only span.</param>
        /// <param name="metadata">The message metadata.</param>
        /// <param name="onMessageReceived">The message processing callback.</param>
        private static async Task ProcessReceivedMessageOptimizedWithMetadataAsync(
            ReadOnlySpan<byte> messageSpan,
            NetworkMessageMetadata metadata,
            Func<ReadOnlySpan<byte>, NetworkMessageMetadata, Task> onMessageReceived)
        {
            try
            {
                if (messageSpan.IsEmpty)
                {
                    Diagnostics.Log("MessageProcessingError", new 
                    { 
                        RemoteEndpoint = metadata.RemoteEndpoint,
                        Error = "Received empty message data",
                        ErrorType = "ValidationError"
                    });
                    return;
                }

                // Process the message directly with the span and metadata
                await onMessageReceived(messageSpan, metadata).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                Diagnostics.Log("MessageProcessingError", new 
                { 
                    RemoteEndpoint = metadata.RemoteEndpoint,
                    Error = ex.Message,
                    ErrorType = ex.GetType().Name
                });
            }
        }

        /// <inheritdoc />
        public ValueTask DisposeAsync()
        {
            if (!_disposed)
            {
                _listener?.Stop();
                _listener?.Close();
                _disposed = true;
            }
            return ValueTask.CompletedTask;
        }
    }
}
