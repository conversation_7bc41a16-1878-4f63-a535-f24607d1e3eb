using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;

namespace USDP2.Metrics
{
    /// <summary>
    /// Base implementation of IMetricsCollector providing common functionality.
    /// This class handles thread-safe metric storage and basic operations.
    /// </summary>
    public abstract class BaseMetricsCollector : IMetricsCollector
    {
        private readonly ConcurrentDictionary<string, CounterMetric> _counters = new();
        private readonly ConcurrentDictionary<string, GaugeMetric> _gauges = new();
        private readonly ConcurrentDictionary<string, HistogramMetric> _histograms = new();
        private readonly ConcurrentDictionary<string, TimingMetric> _timings = new();
        private readonly object _statusLock = new();

        private bool _isEnabled = true;
        private DateTimeOffset? _lastCollectionTime;
        private string? _errorMessage;

        /// <summary>
        /// Initializes a new instance of the <see cref="BaseMetricsCollector"/> class.
        /// </summary>
        /// <param name="name">The name of the metrics collector.</param>
        /// <param name="category">The category of metrics this collector handles.</param>
        protected BaseMetricsCollector(string name, MetricsCategory category)
        {
            Name = name ?? throw new ArgumentNullException(nameof(name));
            Category = category;
        }

        /// <inheritdoc />
        public string Name { get; }

        /// <inheritdoc />
        public MetricsCategory Category { get; }

        /// <summary>
        /// Gets or sets whether this collector is enabled.
        /// </summary>
        public bool IsEnabled
        {
            get
            {
                lock (_statusLock)
                {
                    return _isEnabled;
                }
            }
            set
            {
                lock (_statusLock)
                {
                    _isEnabled = value;
                }
            }
        }

        /// <inheritdoc />
        public virtual void RecordCounter(string name, long value = 1, IDictionary<string, string>? tags = null)
        {
            if (!IsEnabled) return;

            try
            {
                var key = CreateMetricKey(name, tags);
                var counter = _counters.GetOrAdd(key, _ => new CounterMetric 
                { 
                    Name = name, 
                    Tags = tags?.ToDictionary(kvp => kvp.Key, kvp => kvp.Value) ?? new Dictionary<string, string>()
                });
                
                counter.Increment(value);
                UpdateLastCollectionTime();
            }
            catch (Exception ex)
            {
                SetError($"Failed to record counter '{name}': {ex.Message}");
            }
        }

        /// <inheritdoc />
        public virtual void RecordGauge(string name, double value, IDictionary<string, string>? tags = null)
        {
            if (!IsEnabled) return;

            try
            {
                var key = CreateMetricKey(name, tags);
                var gauge = _gauges.GetOrAdd(key, _ => new GaugeMetric 
                { 
                    Name = name, 
                    Tags = tags?.ToDictionary(kvp => kvp.Key, kvp => kvp.Value) ?? new Dictionary<string, string>()
                });
                
                gauge.Set(value);
                UpdateLastCollectionTime();
            }
            catch (Exception ex)
            {
                SetError($"Failed to record gauge '{name}': {ex.Message}");
            }
        }

        /// <inheritdoc />
        public virtual void RecordHistogram(string name, double value, IDictionary<string, string>? tags = null)
        {
            if (!IsEnabled) return;

            try
            {
                var key = CreateMetricKey(name, tags);
                var histogram = _histograms.GetOrAdd(key, _ => new HistogramMetric 
                { 
                    Name = name, 
                    Tags = tags?.ToDictionary(kvp => kvp.Key, kvp => kvp.Value) ?? new Dictionary<string, string>()
                });
                
                histogram.Record(value);
                UpdateLastCollectionTime();
            }
            catch (Exception ex)
            {
                SetError($"Failed to record histogram '{name}': {ex.Message}");
            }
        }

        /// <inheritdoc />
        public virtual void RecordTiming(string name, TimeSpan duration, IDictionary<string, string>? tags = null)
        {
            if (!IsEnabled) return;

            try
            {
                var key = CreateMetricKey(name, tags);
                var timing = _timings.GetOrAdd(key, _ => new TimingMetric 
                { 
                    Name = name, 
                    Tags = tags?.ToDictionary(kvp => kvp.Key, kvp => kvp.Value) ?? new Dictionary<string, string>()
                });
                
                timing.RecordTiming(duration);
                UpdateLastCollectionTime();
            }
            catch (Exception ex)
            {
                SetError($"Failed to record timing '{name}': {ex.Message}");
            }
        }

        /// <inheritdoc />
        public virtual MetricsSnapshot GetSnapshot()
        {
            try
            {
                return new MetricsSnapshot
                {
                    Timestamp = DateTimeOffset.UtcNow,
                    CollectorName = Name,
                    Category = Category,
                    Counters = _counters.ToDictionary(kvp => kvp.Key, kvp => kvp.Value),
                    Gauges = _gauges.ToDictionary(kvp => kvp.Key, kvp => kvp.Value),
                    Histograms = _histograms.ToDictionary(kvp => kvp.Key, kvp => kvp.Value),
                    Timings = _timings.ToDictionary(kvp => kvp.Key, kvp => kvp.Value)
                };
            }
            catch (Exception ex)
            {
                SetError($"Failed to create snapshot: {ex.Message}");
                return new MetricsSnapshot
                {
                    CollectorName = Name,
                    Category = Category
                };
            }
        }

        /// <inheritdoc />
        public virtual void Reset()
        {
            try
            {
                _counters.Clear();
                _gauges.Clear();
                _histograms.Clear();
                _timings.Clear();
                
                lock (_statusLock)
                {
                    _lastCollectionTime = null;
                    _errorMessage = null;
                }
            }
            catch (Exception ex)
            {
                SetError($"Failed to reset metrics: {ex.Message}");
            }
        }

        /// <inheritdoc />
        public virtual MetricsCollectorStatus GetStatus()
        {
            lock (_statusLock)
            {
                return new MetricsCollectorStatus
                {
                    IsEnabled = _isEnabled,
                    MetricsCount = _counters.Count + _gauges.Count + _histograms.Count + _timings.Count,
                    LastCollectionTime = _lastCollectionTime,
                    ErrorMessage = _errorMessage,
                    MemoryUsageBytes = EstimateMemoryUsage()
                };
            }
        }

        /// <summary>
        /// Creates a unique key for a metric based on its name and tags.
        /// </summary>
        /// <param name="name">The metric name.</param>
        /// <param name="tags">The metric tags.</param>
        /// <returns>A unique key for the metric.</returns>
        protected virtual string CreateMetricKey(string name, IDictionary<string, string>? tags)
        {
            if (tags == null || tags.Count == 0)
                return name;

            var sortedTags = tags.OrderBy(kvp => kvp.Key).Select(kvp => $"{kvp.Key}={kvp.Value}");
            return $"{name}[{string.Join(",", sortedTags)}]";
        }

        /// <summary>
        /// Updates the last collection time to the current time.
        /// </summary>
        protected void UpdateLastCollectionTime()
        {
            lock (_statusLock)
            {
                _lastCollectionTime = DateTimeOffset.UtcNow;
                _errorMessage = null; // Clear error on successful operation
            }
        }

        /// <summary>
        /// Sets an error message for this collector.
        /// </summary>
        /// <param name="errorMessage">The error message to set.</param>
        protected void SetError(string errorMessage)
        {
            lock (_statusLock)
            {
                _errorMessage = errorMessage;
            }
        }

        /// <summary>
        /// Estimates the memory usage of this collector.
        /// </summary>
        /// <returns>Estimated memory usage in bytes.</returns>
        protected virtual long EstimateMemoryUsage()
        {
            // Rough estimation - each metric entry is approximately 200-500 bytes
            var totalMetrics = _counters.Count + _gauges.Count + _histograms.Count + _timings.Count;
            return totalMetrics * 350; // Average estimate
        }
    }
}
