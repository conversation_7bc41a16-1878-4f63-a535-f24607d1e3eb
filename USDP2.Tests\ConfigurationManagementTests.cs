using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;

namespace USDP2.Tests
{
    /// <summary>
    /// Tests for the centralized configuration management system.
    /// 
    /// These tests verify:
    /// - Configuration factory methods work correctly
    /// - Environment-specific configuration loading
    /// - Configuration validation
    /// - Component creation without constructor parameters
    /// - Error handling for invalid configurations
    /// </summary>
    [TestClass]
    public class ConfigurationManagementTests
    {
        private UsdpConfiguration? _originalConfig;

        [TestInitialize]
        public void Setup()
        {
            // Save original configuration to restore after tests
            _originalConfig = UsdpConfiguration.Instance;
        }

        [TestCleanup]
        public void Cleanup()
        {
            // Restore original configuration
            if (_originalConfig != null)
            {
                // Reset to original state
                var config = UsdpConfiguration.Instance;
                config.Environment = _originalConfig.Environment;
                config.ApplyEnvironmentDefaults();
            }
        }

        [TestMethod]
        public void ConfigurationFactory_CreateLocalDirectory_Success()
        {
            // Arrange & Act
            using var localDirectory = ConfigurationFactory.CreateLocalDirectory();

            // Assert
            Assert.IsNotNull(localDirectory);
        }

        [TestMethod]
        public void ConfigurationFactory_CreateNetworkSender_Local_Success()
        {
            // Arrange & Act
            var sender = ConfigurationFactory.CreateNetworkSender(NetworkScope.Local);

            try
            {
                // Assert
                Assert.IsNotNull(sender);
                Assert.IsInstanceOfType(sender, typeof(UdpNetworkSender));
            }
            finally
            {
                if (sender is IDisposable disposable)
                    disposable.Dispose();
            }
        }

        [TestMethod]
        public void ConfigurationFactory_CreateNetworkSender_Global_Success()
        {
            // Arrange & Act
            var sender = ConfigurationFactory.CreateNetworkSender(NetworkScope.Global);

            try
            {
                // Assert
                Assert.IsNotNull(sender);
                Assert.IsInstanceOfType(sender, typeof(HttpNetworkSender));
            }
            finally
            {
                if (sender is IDisposable disposable)
                    disposable.Dispose();
            }
        }

        [TestMethod]
        public void ConfigurationFactory_CreateNetworkReceiver_Local_Success()
        {
            // Arrange & Act
            var receiver = ConfigurationFactory.CreateNetworkReceiver(NetworkScope.Local);

            try
            {
                // Assert
                Assert.IsNotNull(receiver);
                Assert.IsInstanceOfType(receiver, typeof(UdpNetworkReceiver));
            }
            finally
            {
                if (receiver is IAsyncDisposable asyncDisposable)
                    asyncDisposable.DisposeAsync().AsTask().Wait();
                else if (receiver is IDisposable disposable)
                    disposable.Dispose();
            }
        }

        [TestMethod]
        public void ConfigurationFactory_CreateNetworkReceiver_Global_Success()
        {
            // Arrange & Act
            var receiver = ConfigurationFactory.CreateNetworkReceiver(NetworkScope.Global);

            try
            {
                // Assert
                Assert.IsNotNull(receiver);
                Assert.IsInstanceOfType(receiver, typeof(HttpNetworkReceiver));
            }
            finally
            {
                if (receiver is IAsyncDisposable asyncDisposable)
                    asyncDisposable.DisposeAsync().AsTask().Wait();
                else if (receiver is IDisposable disposable)
                    disposable.Dispose();
            }
        }

        [TestMethod]
        public void ConfigurationFactory_CreateServiceAdvertisement_Success()
        {
            // Arrange & Act
            var serviceAd = ConfigurationFactory.CreateServiceAdvertisement();

            // Assert
            Assert.IsNotNull(serviceAd);
            Assert.IsNotNull(serviceAd.ServiceId);
            Assert.IsNotNull(serviceAd.Endpoint);
            Assert.IsTrue(serviceAd.Ttl > TimeSpan.Zero);
        }

        [TestMethod]
        public void DynamicDnsClient_Create_WithEnvironmentVariable_Success()
        {
            // Arrange
            Environment.SetEnvironmentVariable("USDP_DNS_TOKEN", "test-token");
            var config = new UsdpConfiguration(forTesting: true) { DefaultDnsDomain = "test.example.com" };

            try
            {
                // Act
                using var dnsClient = DynamicDnsClient.Create("test.example.com", "test-token", config);

                // Assert
                Assert.IsNotNull(dnsClient);
            }
            finally
            {
                // Cleanup
                Environment.SetEnvironmentVariable("USDP_DNS_TOKEN", null);
            }
        }

        [TestMethod]
        public void DynamicDnsClient_Create_MissingDomain_ThrowsException()
        {
            // Arrange
            var config = new UsdpConfiguration(forTesting: true) { DefaultDnsDomain = null };

            // Act & Assert
            Assert.ThrowsException<InvalidOperationException>(() =>
                DynamicDnsClient.Create(null, "test-token", config));
        }

        [TestMethod]
        public void DynamicDnsClient_Create_MissingToken_ThrowsException()
        {
            // Arrange
            Environment.SetEnvironmentVariable("USDP_DNS_TOKEN", null);
            var config = new UsdpConfiguration(forTesting: true) { DefaultDnsDomain = "test.example.com" };

            // Act & Assert
            Assert.ThrowsException<InvalidOperationException>(() =>
                DynamicDnsClient.Create("test.example.com", null, config));
        }

        [TestMethod]
        public void LocalDirectory_Create_Success()
        {
            // Arrange & Act
            using var localDirectory = LocalDirectory.Create();

            // Assert
            Assert.IsNotNull(localDirectory);
        }

        [TestMethod]
        public void LocalDirectory_Create_WithCustomConfig_Success()
        {
            // Arrange
            var customConfig = new UsdpConfiguration(forTesting: true)
            {
                DefaultMulticastAddress = "***************",
                DefaultMulticastPort = 5354
            };

            // Act
            using var localDirectory = LocalDirectory.Create(customConfig);

            // Assert
            Assert.IsNotNull(localDirectory);
        }

        [TestMethod]
        public void EnvironmentConfiguration_Development_HasCorrectDefaults()
        {
            // Arrange
            var config = new UsdpConfiguration(forTesting: true) { Environment = UsdpConfiguration.DeploymentEnvironment.Development };

            // Act
            config.ApplyEnvironmentDefaults();

            // Assert
            Assert.IsFalse(config.RequireAuthentication);
            Assert.AreEqual("none", config.DefaultSecurity);
            Assert.IsFalse(config.UseHttps);
            Assert.IsFalse(config.EnableRetryPolicies);
        }

        [TestMethod]
        public void EnvironmentConfiguration_Production_HasCorrectDefaults()
        {
            // Arrange
            var config = new UsdpConfiguration(forTesting: true) { Environment = UsdpConfiguration.DeploymentEnvironment.Production };

            // Act
            config.ApplyEnvironmentDefaults();

            // Assert
            Assert.IsTrue(config.RequireAuthentication);
            Assert.AreEqual("psk-tls1.3", config.DefaultSecurity);
            Assert.IsTrue(config.UseHttps);
            Assert.IsTrue(config.EnableRetryPolicies);
            Assert.IsTrue(config.EnableCircuitBreakers);
        }

        [TestMethod]
        public void ConfigurationValidation_ValidConfiguration_NoErrors()
        {
            // Arrange
            var config = new UsdpConfiguration(forTesting: true);
            config.ApplyEnvironmentDefaults();

            // Act
            var results = ValidateConfiguration.ValidateAll(config);
            var errors = results.Where(r => r.IsError).ToList();

            // Assert
            Assert.AreEqual(0, errors.Count, $"Expected no errors, but found: {string.Join(", ", errors.Select(e => e.Message))}");
        }

        [TestMethod]
        public void ConfigurationValidation_InvalidPort_HasError()
        {
            // Arrange
            var config = new UsdpConfiguration(forTesting: true) { DefaultMulticastPort = 0 };

            // Act
            var results = ValidateConfiguration.ValidateAll(config);
            var errors = results.Where(r => r.IsError).ToList();

            // Assert
            Assert.IsTrue(errors.Count > 0);
            Assert.IsTrue(errors.Any(e => e.PropertyName.Contains("Port")));
        }

        [TestMethod]
        public void ConfigurationValidation_ProductionWithoutSecurity_HasError()
        {
            // Arrange
            var config = new UsdpConfiguration(forTesting: true)
            {
                Environment = UsdpConfiguration.DeploymentEnvironment.Production,
                RequireAuthentication = false,
                DefaultSecurity = "none",
                UseHttps = false
            };

            // Act
            var results = ValidateConfiguration.ValidateAll(config);
            var errors = results.Where(r => r.IsError).ToList();

            // Assert
            Assert.IsTrue(errors.Count > 0);
            Assert.IsTrue(errors.Any(e => e.Message.Contains("production")));
        }

        [TestMethod]
        public async Task EnvironmentConfigurationLoader_LoadConfiguration_Success()
        {
            // Arrange
            var tempDir = Path.GetTempPath();
            var configPath = Path.Combine(tempDir, "test_config.json");

            // Create a test configuration file
            var testConfig = @"{
                ""defaultMulticastPort"": 5354,
                ""useHttps"": true,
                ""requireAuthentication"": true
            }";

            await File.WriteAllTextAsync(configPath, testConfig);

            try
            {
                // Act
                var loader = new EnvironmentConfigurationLoader(tempDir);
                // Note: This would normally load the configuration, but we'll just test creation

                // Assert
                Assert.IsNotNull(loader);
            }
            finally
            {
                // Cleanup
                if (File.Exists(configPath))
                {
                    File.Delete(configPath);
                }
            }
        }

        [TestMethod]
        public void NetworkSenderFactory_CreateSender_Local_Success()
        {
            // Arrange
            var config = new UsdpConfiguration(forTesting: true)
            {
                DefaultMulticastAddress = "***************",
                DefaultMulticastPort = 5353
            };

            // Act
            var sender = NetworkSenderFactory.CreateSender(NetworkScope.Local, config);

            try
            {
                // Assert
                Assert.IsNotNull(sender);
                Assert.IsInstanceOfType(sender, typeof(UdpNetworkSender));
            }
            finally
            {
                if (sender is IDisposable disposable)
                    disposable.Dispose();
            }
        }

        [TestMethod]
        public void NetworkSenderFactory_CreateSender_InvalidPort_ShouldStillCreate()
        {
            // Arrange
            var config = new UsdpConfiguration(forTesting: true)
            {
                DefaultMulticastAddress = "***************",
                DefaultMulticastPort = 0
            };

            // Act & Assert - Should create but may have issues during operation
            var sender = NetworkSenderFactory.CreateSender(NetworkScope.Local, config);

            try
            {
                Assert.IsNotNull(sender);
            }
            finally
            {
                if (sender is IDisposable disposable)
                    disposable.Dispose();
            }
        }

        [TestMethod]
        public void ConfigurationFactory_ValidateConfiguration_Development_NoIssues()
        {
            // Arrange & Act
            var issues = ConfigurationFactory.ValidateConfiguration(UsdpConfiguration.DeploymentEnvironment.Development);

            // Assert
            // Development environment should be permissive, so minimal issues expected
            Assert.IsNotNull(issues);
        }

        [TestMethod]
        public void ConfigurationFactory_ValidateConfiguration_Production_HasSecurityRequirements()
        {
            // Arrange
            var config = new UsdpConfiguration(forTesting: true)
            {
                Environment = UsdpConfiguration.DeploymentEnvironment.Production,
                RequireAuthentication = false,
                UseHttps = false,
                DefaultSecurity = "none"
            };

            // Act
            var issues = ConfigurationFactory.ValidateConfiguration(UsdpConfiguration.DeploymentEnvironment.Production, config);

            // Assert
            Assert.IsTrue(issues.Count > 0);
            Assert.IsTrue(issues.Any(i => i.Contains("Authentication") || i.Contains("HTTPS") || i.Contains("Security")));
        }

        [TestMethod]
        public void UsdpConfiguration_DetectEnvironment_ReturnsValidEnvironment()
        {
            // Arrange & Act
            var environment = UsdpConfiguration.Instance.Environment;

            // Assert
            Assert.IsTrue(Enum.IsDefined(typeof(UsdpConfiguration.DeploymentEnvironment), environment));
        }

        [TestMethod]
        public void UsdpConfiguration_ApplyEnvironmentDefaults_ChangesConfiguration()
        {
            // Arrange
            var config = UsdpConfiguration.Instance;
            var originalSecurity = config.DefaultSecurity;
            var originalEnv = config.Environment;

            try
            {
                config.Environment = UsdpConfiguration.DeploymentEnvironment.Development;

                // Act
                config.ApplyEnvironmentDefaults();

                // Assert
                // Development should change security settings
                Assert.IsNotNull(config.DefaultSecurity);
            }
            finally
            {
                // Restore original configuration
                config.Environment = originalEnv;
                config.ApplyEnvironmentDefaults();
            }
        }

        #region Security Level Tests

        [TestMethod]
        public void SecurityConfigurationManager_GetEncryptionLevel_ReturnsCorrectMapping()
        {
            // Arrange & Act & Assert
            Assert.AreEqual(SecurityConfigurationManager.EncryptionLevel.None,
                SecurityConfigurationManager.GetEncryptionLevel(UsdpConfiguration.SecurityLevel.None));

            Assert.AreEqual(SecurityConfigurationManager.EncryptionLevel.AES128,
                SecurityConfigurationManager.GetEncryptionLevel(UsdpConfiguration.SecurityLevel.Low));

            Assert.AreEqual(SecurityConfigurationManager.EncryptionLevel.AES256_GCM,
                SecurityConfigurationManager.GetEncryptionLevel(UsdpConfiguration.SecurityLevel.Medium));

            Assert.AreEqual(SecurityConfigurationManager.EncryptionLevel.AES256_GCM_PFS,
                SecurityConfigurationManager.GetEncryptionLevel(UsdpConfiguration.SecurityLevel.High));
        }

        [TestMethod]
        public void SecurityConfigurationManager_GetAuthenticationLevel_ReturnsCorrectMapping()
        {
            // Arrange & Act & Assert
            Assert.AreEqual(SecurityConfigurationManager.AuthenticationLevel.None,
                SecurityConfigurationManager.GetAuthenticationLevel(UsdpConfiguration.SecurityLevel.None));

            Assert.AreEqual(SecurityConfigurationManager.AuthenticationLevel.PreSharedKey,
                SecurityConfigurationManager.GetAuthenticationLevel(UsdpConfiguration.SecurityLevel.Low));

            Assert.AreEqual(SecurityConfigurationManager.AuthenticationLevel.Certificate,
                SecurityConfigurationManager.GetAuthenticationLevel(UsdpConfiguration.SecurityLevel.Medium));

            Assert.AreEqual(SecurityConfigurationManager.AuthenticationLevel.MultiFactor,
                SecurityConfigurationManager.GetAuthenticationLevel(UsdpConfiguration.SecurityLevel.High));
        }

        [TestMethod]
        public void ConfigurationFactory_CreateSecureUsdpSystem_NoSecurity_Success()
        {
            // Arrange & Act
            var (localDirectory, dnsClient, localSender, globalSender) =
                ConfigurationFactory.CreateSecureUsdpSystem(UsdpConfiguration.SecurityLevel.None);

            // Assert
            Assert.IsNotNull(localDirectory);
            Assert.IsNotNull(localSender);
            Assert.IsNotNull(globalSender);
            // dnsClient may be null if DNS configuration is missing

            // Cleanup
            localDirectory.Dispose();
            dnsClient?.Dispose();
            if (localSender is IDisposable disposableLocal) disposableLocal.Dispose();
            if (globalSender is IDisposable disposableGlobal) disposableGlobal.Dispose();
        }

        [TestMethod]
        public void ConfigurationFactory_CreateLocalDirectoryWithSecurity_HighSecurity_Success()
        {
            // Arrange & Act
            using var localDirectory = ConfigurationFactory.CreateLocalDirectoryWithSecurity(
                UsdpConfiguration.SecurityLevel.High);

            // Assert
            Assert.IsNotNull(localDirectory);
        }

        [TestMethod]
        public void ConfigurationFactory_ValidateSecurityLevelConfiguration_NoSecurity_HasIssues()
        {
            // Arrange & Act
            var issues = ConfigurationFactory.ValidateSecurityLevelConfiguration(UsdpConfiguration.SecurityLevel.None);

            // Assert
            Assert.IsNotNull(issues);
            // No security level should have some validation warnings about placeholders
        }

        [TestMethod]
        public void SecurityConfigurationManager_GetSecurityRecommendations_AllLevels_ReturnsRecommendations()
        {
            // Arrange
            var securityLevels = Enum.GetValues<UsdpConfiguration.SecurityLevel>();

            foreach (var level in securityLevels)
            {
                // Act
                var recommendations = SecurityConfigurationManager.GetSecurityRecommendations(level);

                // Assert
                Assert.IsNotNull(recommendations);
                Assert.IsTrue(recommendations.Count > 0);
                Assert.IsTrue(recommendations.ContainsKey("SecurityLevel"));
                Assert.AreEqual(level.ToString(), recommendations["SecurityLevel"]);
            }
        }

        [TestMethod]
        public void UsdpConfiguration_SecurityLevel_DetectedCorrectly()
        {
            // Arrange & Act
            var config = UsdpConfiguration.Instance;

            // Assert
            Assert.IsTrue(Enum.IsDefined(typeof(UsdpConfiguration.SecurityLevel), config.Security));
        }

        [TestMethod]
        public void UsdpConfiguration_ApplySecurityLevelDefaults_ChangesConfiguration()
        {
            // Arrange
            var config = UsdpConfiguration.Instance;
            var originalSecurity = config.Security;
            var originalAuth = config.RequireAuthentication;

            try
            {
                config.Security = UsdpConfiguration.SecurityLevel.High;

                // Act
                config.ApplySecurityLevelDefaults();

                // Assert
                Assert.IsTrue(config.RequireAuthentication); // High security should require auth
                Assert.IsTrue(config.UseHttps); // High security should use HTTPS
            }
            finally
            {
                // Restore original configuration
                config.Security = originalSecurity;
                config.RequireAuthentication = originalAuth;
                config.ApplySecurityLevelDefaults();
            }
        }

        [TestMethod]
        public void ValidateConfiguration_IncludesSecurityLevelValidation()
        {
            // Arrange
            var config = UsdpConfiguration.Instance;

            // Act
            var results = ValidateConfiguration.ValidateAll(config);

            // Assert
            Assert.IsNotNull(results);
            // Should include security level validation results
            var securityResults = results.Where(r => r.PropertyName.Contains("Security")).ToList();
            Assert.IsTrue(securityResults.Count > 0);
        }

        #endregion
    }
}
