using System;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using USDP2.RetryPolicy;

namespace USDP2
{
    /// <summary>
    /// Secure Dynamic DNS (DDNS) client for updating DNS records via HTTP-based DDNS providers with retry policy support.
    ///
    /// This client provides:
    /// - Secure HTTPS-only DNS updates
    /// - Automatic retry on transient failures
    /// - Exponential backoff with jitter
    /// - Comprehensive logging and monitoring
    /// - Support for multiple authentication methods
    /// </summary>
    public class DynamicDnsClient : IDisposable
    {
        /// <summary>
        /// The http client.
        /// </summary>
        private readonly HttpClient _httpClient;

        /// <summary>
        /// The retry policy executor for HTTP operations.
        /// </summary>
        private readonly RetryPolicyExecutor _retryPolicy;
        /// <summary>
        /// The domain name for DNS updates.
        /// </summary>
        private readonly string? _domain;

        /// <summary>
        /// The authentication token for DNS updates.
        /// </summary>
        private readonly string? _token;

        /// <summary>
        /// Initializes a new instance of the <see cref="DynamicDnsClient"/> class.
        /// </summary>
        /// <param name="domain">The domain.</param>
        /// <param name="token">The token.</param>
        public DynamicDnsClient(string domain, string token)
        {
            _domain = domain ?? throw new ArgumentNullException(nameof(domain));
            _token = token ?? throw new ArgumentNullException(nameof(token));

            // Initialize the HttpClient
            _httpClient = new HttpClient();
            _retryPolicy = UsdpConfiguration.Instance.EnableRetryPolicies
                ? RetryPolicyExecutor.CreateHttpRetryPolicy()
                : RetryPolicyExecutor.CreateCustomRetryPolicy(maxAttempts: 1); // No retries if disabled

            UsdpLogger.Log("DnsClientInitialized", new
            {
                domain,
                AuthenticationMethod = "Token",
                hasToken = !string.IsNullOrEmpty(token),
                RetryEnabled = UsdpConfiguration.Instance.EnableRetryPolicies,
                MaxRetryAttempts = UsdpConfiguration.Instance.HttpRetryMaxAttempts
            });
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="DynamicDnsClient"/> class.
        /// </summary>
        /// <param name="handler">The handler.</param>
        public DynamicDnsClient(HttpMessageHandler? handler = null)
        {
            // Enforce HTTPS and allow custom handlers for advanced scenarios
            _httpClient = handler != null ? new HttpClient(handler, disposeHandler: true) : new HttpClient();
            _retryPolicy = UsdpConfiguration.Instance.EnableRetryPolicies
                ? RetryPolicyExecutor.CreateHttpRetryPolicy()
                : RetryPolicyExecutor.CreateCustomRetryPolicy(maxAttempts: 1); // No retries if disabled

            UsdpLogger.Log("DnsClientInitialized", new
            {
                hasCustomHandler = handler != null,
                RetryEnabled = UsdpConfiguration.Instance.EnableRetryPolicies,
                MaxRetryAttempts = UsdpConfiguration.Instance.HttpRetryMaxAttempts
            });
        }

        /// <summary>
        /// Creates a new DynamicDnsClient instance using configuration from UsdpConfiguration.
        /// This factory method eliminates the need to pass domain and token parameters manually.
        /// </summary>
        /// <param name="domain">DNS domain to update. If null, uses configuration default.</param>
        /// <param name="token">Authentication token. If null, attempts to load from environment or configuration.</param>
        /// <param name="config">Optional configuration override. If null, uses UsdpConfiguration.Instance.</param>
        /// <returns>A configured DynamicDnsClient instance.</returns>
        /// <exception cref="InvalidOperationException">Thrown when required configuration is missing.</exception>
        public static DynamicDnsClient Create(
            string? domain = null,
            string? token = null,
            UsdpConfiguration? config = null)
        {
            var configuration = config ?? UsdpConfiguration.Instance;

            // Use provided values or fall back to configuration defaults
            var effectiveDomain = domain ?? configuration.DefaultDnsDomain;
            var effectiveToken = token ?? LoadTokenFromEnvironment();

            if (string.IsNullOrEmpty(effectiveDomain))
            {
                throw new InvalidOperationException(
                    "DNS domain must be specified either as a parameter or in UsdpConfiguration.DefaultDnsDomain. " +
                    "Set the domain in configuration or pass it explicitly to the Create method.");
            }

            if (string.IsNullOrEmpty(effectiveToken))
            {
                throw new InvalidOperationException(
                    "DNS authentication token must be specified either as a parameter or in the USDP_DNS_TOKEN environment variable. " +
                    "Set the USDP_DNS_TOKEN environment variable or pass the token explicitly to the Create method.");
            }

            return new DynamicDnsClient(effectiveDomain, effectiveToken);
        }

        /// <summary>
        /// Creates a DynamicDnsClient with a custom HTTP handler and configuration.
        /// </summary>
        /// <param name="handler">Custom HTTP message handler.</param>
        /// <param name="config">Optional configuration override.</param>
        /// <returns>A configured DynamicDnsClient instance.</returns>
        public static DynamicDnsClient CreateWithHandler(
            HttpMessageHandler handler,
            UsdpConfiguration? config = null)
        {
            if (handler == null)
                throw new ArgumentNullException(nameof(handler));

            return new DynamicDnsClient(handler);
        }

        /// <summary>
        /// Attempts to load DNS token from environment variables.
        /// </summary>
        private static string? LoadTokenFromEnvironment()
        {
            // Try multiple environment variable names for flexibility
            return Environment.GetEnvironmentVariable("USDP_DNS_TOKEN") ??
                   Environment.GetEnvironmentVariable("DNS_TOKEN") ??
                   Environment.GetEnvironmentVariable("DDNS_TOKEN");
        }

        /// <summary>
        /// Updates the DNS record at the DDNS provider using HTTPS and secure authentication with automatic retry on failures.
        ///
        /// This method automatically retries on transient failures such as:
        /// - Network timeouts and connectivity issues
        /// - HTTP 5xx server errors (500, 502, 503, 504)
        /// - HTTP 408 (Request Timeout) and 429 (Too Many Requests)
        /// - Socket and network infrastructure errors
        ///
        /// The retry policy uses exponential backoff with jitter to prevent thundering herd problems.
        /// </summary>
        /// <param name="updateUrl">The provider's update URL (must use HTTPS).</param>
        /// <param name="username">Optional username for basic auth.</param>
        /// <param name="password">Optional password for basic auth.</param>
        /// <param name="bearerToken">Optional OAuth2 bearer token.</param>
        /// <param name="cancellationToken">Cancellation token for the operation.</param>
        /// <returns>True if update was successful, false otherwise.</returns>
        public async Task<bool> UpdateAsync(
            string updateUrl,
            string? username = null,
            string? password = null,
            string? bearerToken = null,
            CancellationToken cancellationToken = default)
        {
            if (!Uri.TryCreate(updateUrl, UriKind.Absolute, out var uri) || uri.Scheme != Uri.UriSchemeHttps)
                throw new ArgumentException("Update URL must use HTTPS.", nameof(updateUrl));

            var authType = !string.IsNullOrEmpty(bearerToken) ? "Bearer" :
                           !string.IsNullOrEmpty(username) ? "Basic" : "None";

            UsdpLogger.Log("DnsUpdateAttempt", new
            {
                updateUrl = uri.Host, // Log host only for security
                authType,
                timestamp = DateTimeOffset.UtcNow,
                RetryEnabled = UsdpConfiguration.Instance.EnableRetryPolicies
            });

            try
            {
                return await _retryPolicy.ExecuteAsync(async ct =>
                {
                    using var request = new HttpRequestMessage(HttpMethod.Get, updateUrl);

                    if (!string.IsNullOrEmpty(bearerToken))
                    {
                        request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", bearerToken);
                    }
                    else if (!string.IsNullOrEmpty(username) && password != null)
                    {
                        var byteArray = System.Text.Encoding.ASCII.GetBytes($"{username}:{password}");
                        request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Basic", Convert.ToBase64String(byteArray));
                    }

                    var response = await _httpClient.SendAsync(request, ct).ConfigureAwait(false);

                    if (response.IsSuccessStatusCode)
                    {
                        UsdpLogger.Log("DnsUpdateSuccess", new
                        {
                            host = uri.Host,
                            statusCode = (int)response.StatusCode,
                            authType
                        });
                        return true;
                    }
                    else
                    {
                        UsdpLogger.Log("DnsUpdateFailure", new
                        {
                            host = uri.Host,
                            statusCode = (int)response.StatusCode,
                            reasonPhrase = response.ReasonPhrase,
                            authType
                        });

                        // For HTTP errors, throw an exception to trigger retry logic
                        // The retry policy will determine if this should be retried based on status code
                        throw new HttpRequestException($"DNS update failed with status code {response.StatusCode}: {response.ReasonPhrase}");
                    }
                }, cancellationToken).ConfigureAwait(false);
            }
            catch (HttpRequestException ex)
            {
                UsdpLogger.Log("DnsUpdateError", new
                {
                    host = uri.Host,
                    error = "HttpRequestException",
                    message = ex.Message,
                    authType,
                    FinalFailure = true
                });
                return false;
            }
            catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException)
            {
                UsdpLogger.Log("DnsUpdateError", new
                {
                    host = uri.Host,
                    error = "Timeout",
                    message = ex.Message,
                    authType,
                    FinalFailure = true
                });
                return false;
            }
            catch (OperationCanceledException) when (cancellationToken.IsCancellationRequested)
            {
                UsdpLogger.Log("DnsUpdateCancelled", new
                {
                    host = uri.Host,
                    authType
                });
                return false;
            }
            catch (Exception ex)
            {
                UsdpLogger.Log("DnsUpdateError", new
                {
                    host = uri.Host,
                    error = ex.GetType().Name,
                    message = ex.Message,
                    authType,
                    FinalFailure = true
                });
                return false;
            }
        }

        /// <summary>
        /// TODO: Add Summary.
        /// </summary>
        public void Dispose()
        {
            Diagnostics.Log("DnsClientDisposed", new { timestamp = DateTimeOffset.UtcNow });
            _httpClient.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}