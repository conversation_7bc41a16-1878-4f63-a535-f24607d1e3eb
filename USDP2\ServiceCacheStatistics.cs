using System;

namespace USDP2
{
    /// <summary>
    /// Represents comprehensive statistics for the ServiceAdvertisementCache.
    /// Provides detailed metrics for monitoring cache performance, memory usage,
    /// and operational efficiency.
    /// </summary>
    public sealed class ServiceCacheStatistics
    {
        /// <summary>
        /// Gets or sets the current number of entries in the cache.
        /// </summary>
        public int CurrentSize { get; set; }

        /// <summary>
        /// Gets or sets the maximum configured cache size.
        /// </summary>
        public int MaxSize { get; set; }

        /// <summary>
        /// Gets or sets the current memory usage in bytes.
        /// </summary>
        public long CurrentMemoryBytes { get; set; }

        /// <summary>
        /// Gets or sets the maximum configured memory limit in bytes.
        /// </summary>
        public long MaxMemoryBytes { get; set; }

        /// <summary>
        /// Gets or sets the total number of cache hits since startup.
        /// </summary>
        public long TotalHits { get; set; }

        /// <summary>
        /// Gets or sets the total number of cache misses since startup.
        /// </summary>
        public long TotalMisses { get; set; }

        /// <summary>
        /// Gets or sets the total number of entries added to the cache.
        /// </summary>
        public long TotalAdds { get; set; }

        /// <summary>
        /// Gets or sets the total number of entries evicted from the cache.
        /// </summary>
        public long TotalEvictions { get; set; }

        /// <summary>
        /// Gets or sets the number of entries evicted due to TTL expiration.
        /// </summary>
        public long TtlEvictions { get; set; }

        /// <summary>
        /// Gets or sets the number of entries evicted due to size limits.
        /// </summary>
        public long SizeEvictions { get; set; }

        /// <summary>
        /// Gets or sets the number of entries evicted due to memory limits.
        /// </summary>
        public long MemoryEvictions { get; set; }

        /// <summary>
        /// Gets or sets the timestamp when statistics collection started.
        /// </summary>
        public DateTimeOffset StartTime { get; set; }

        /// <summary>
        /// Gets or sets the timestamp of the last statistics update.
        /// </summary>
        public DateTimeOffset LastUpdateTime { get; set; }

        /// <summary>
        /// Gets the cache hit rate as a percentage (0-100).
        /// </summary>
        public double HitRate => TotalHits + TotalMisses > 0 
            ? (double)TotalHits / (TotalHits + TotalMisses) * 100 
            : 0;

        /// <summary>
        /// Gets the cache miss rate as a percentage (0-100).
        /// </summary>
        public double MissRate => 100 - HitRate;

        /// <summary>
        /// Gets the cache utilization as a percentage of maximum size (0-100).
        /// </summary>
        public double SizeUtilization => MaxSize > 0 
            ? (double)CurrentSize / MaxSize * 100 
            : 0;

        /// <summary>
        /// Gets the memory utilization as a percentage of maximum memory (0-100).
        /// </summary>
        public double MemoryUtilization => MaxMemoryBytes > 0 
            ? (double)CurrentMemoryBytes / MaxMemoryBytes * 100 
            : 0;

        /// <summary>
        /// Gets the average memory usage per cache entry in bytes.
        /// </summary>
        public double AverageEntrySize => CurrentSize > 0 
            ? (double)CurrentMemoryBytes / CurrentSize 
            : 0;

        /// <summary>
        /// Gets the total runtime duration since statistics collection started.
        /// </summary>
        public TimeSpan Runtime => LastUpdateTime - StartTime;

        /// <summary>
        /// Gets the average operations per second (hits + misses + adds).
        /// </summary>
        public double OperationsPerSecond
        {
            get
            {
                var totalSeconds = Runtime.TotalSeconds;
                return totalSeconds > 0 
                    ? (TotalHits + TotalMisses + TotalAdds) / totalSeconds 
                    : 0;
            }
        }

        /// <summary>
        /// Gets the eviction rate as evictions per hour.
        /// </summary>
        public double EvictionsPerHour
        {
            get
            {
                var totalHours = Runtime.TotalHours;
                return totalHours > 0 
                    ? TotalEvictions / totalHours 
                    : 0;
            }
        }

        /// <summary>
        /// Initializes a new instance of the ServiceCacheStatistics class.
        /// </summary>
        public ServiceCacheStatistics()
        {
            StartTime = DateTimeOffset.UtcNow;
            LastUpdateTime = StartTime;
        }

        /// <summary>
        /// Creates a copy of the current statistics.
        /// </summary>
        /// <returns>A new ServiceCacheStatistics instance with the same values.</returns>
        public ServiceCacheStatistics Clone()
        {
            return new ServiceCacheStatistics
            {
                CurrentSize = CurrentSize,
                MaxSize = MaxSize,
                CurrentMemoryBytes = CurrentMemoryBytes,
                MaxMemoryBytes = MaxMemoryBytes,
                TotalHits = TotalHits,
                TotalMisses = TotalMisses,
                TotalAdds = TotalAdds,
                TotalEvictions = TotalEvictions,
                TtlEvictions = TtlEvictions,
                SizeEvictions = SizeEvictions,
                MemoryEvictions = MemoryEvictions,
                StartTime = StartTime,
                LastUpdateTime = LastUpdateTime
            };
        }

        /// <summary>
        /// Returns a string representation of the cache statistics.
        /// </summary>
        /// <returns>A formatted string containing key statistics.</returns>
        public override string ToString()
        {
            return $"ServiceCacheStatistics: Size={CurrentSize}/{MaxSize} ({SizeUtilization:F1}%), " +
                   $"Memory={CurrentMemoryBytes / 1024}KB/{MaxMemoryBytes / 1024}KB ({MemoryUtilization:F1}%), " +
                   $"HitRate={HitRate:F1}%, Ops/sec={OperationsPerSecond:F1}";
        }
    }
}
