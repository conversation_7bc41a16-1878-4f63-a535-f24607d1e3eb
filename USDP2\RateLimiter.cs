using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace USDP2
{
    /// <summary>
    /// Provides rate limiting functionality to prevent DoS attacks and message flooding.
    /// Uses a sliding window algorithm for accurate rate calculation and supports
    /// burst allowances for legitimate traffic spikes.
    /// 
    /// This class is thread-safe and designed for high-performance scenarios with
    /// minimal overhead for normal traffic patterns.
    /// </summary>
    public class RateLimiter : IDisposable
    {
        private readonly ConcurrentDictionary<string, EndpointTracker> _endpointTrackers = new();
        private readonly Timer _cleanupTimer;
        private readonly UsdpConfiguration _config;
        private readonly object _cleanupLock = new object();
        private bool _disposed = false;

        /// <summary>
        /// Initializes a new instance of the RateLimiter class.
        /// </summary>
        /// <param name="config">The USDP configuration instance. If null, uses the singleton instance.</param>
        public RateLimiter(UsdpConfiguration? config = null)
        {
            _config = config ?? UsdpConfiguration.Instance;
            
            // Start cleanup timer if rate limiting is enabled
            if (_config.EnableRateLimiting)
            {
                _cleanupTimer = new Timer(CleanupExpiredTrackers, null, 
                    _config.RateLimitCleanupInterval, _config.RateLimitCleanupInterval);
            }
            else
            {
                _cleanupTimer = null!;
            }
        }

        /// <summary>
        /// Checks if a message from the specified endpoint should be allowed based on rate limits.
        /// </summary>
        /// <param name="remoteAddress">The remote IP address.</param>
        /// <param name="remotePort">The remote port number.</param>
        /// <param name="messageSize">The size of the message in bytes.</param>
        /// <returns>A RateLimitResult indicating whether the message should be allowed.</returns>
        public RateLimitResult CheckRateLimit(string remoteAddress, int remotePort, int messageSize)
        {
            if (!_config.EnableRateLimiting)
            {
                return RateLimitResult.Allowed();
            }

            var endpointKey = $"{remoteAddress}:{remotePort}";
            var now = DateTime.UtcNow;
            
            var tracker = _endpointTrackers.GetOrAdd(endpointKey, key => new EndpointTracker(key, _config));
            
            return tracker.CheckMessage(now, messageSize);
        }

        /// <summary>
        /// Gets rate limiting statistics for monitoring and debugging.
        /// </summary>
        /// <returns>A dictionary of endpoint statistics.</returns>
        public Dictionary<string, RateLimitStats> GetStatistics()
        {
            var stats = new Dictionary<string, RateLimitStats>();
            var now = DateTime.UtcNow;

            foreach (var kvp in _endpointTrackers)
            {
                var tracker = kvp.Value;
                stats[kvp.Key] = tracker.GetStats(now);
            }

            return stats;
        }

        /// <summary>
        /// Clears all rate limiting data. Useful for testing or manual reset.
        /// </summary>
        public void Reset()
        {
            _endpointTrackers.Clear();
        }

        /// <summary>
        /// Cleanup timer callback that removes expired endpoint trackers.
        /// </summary>
        private void CleanupExpiredTrackers(object? state)
        {
            if (_disposed)
                return;

            lock (_cleanupLock)
            {
                if (_disposed)
                    return;

                var now = DateTime.UtcNow;
                var expiredKeys = new List<string>();
                var cleanupThreshold = _config.RateLimitTimeWindow.Add(_config.RateLimitCleanupInterval);

                foreach (var kvp in _endpointTrackers)
                {
                    var tracker = kvp.Value;
                    if (now - tracker.LastActivity > cleanupThreshold)
                    {
                        expiredKeys.Add(kvp.Key);
                    }
                }

                foreach (var key in expiredKeys)
                {
                    _endpointTrackers.TryRemove(key, out _);
                }

                if (expiredKeys.Count > 0)
                {
                    UsdpLogger.Log("RateLimiter.CleanupCompleted", new
                    {
                        ExpiredEndpoints = expiredKeys.Count,
                        ActiveEndpoints = _endpointTrackers.Count,
                        CleanupThreshold = cleanupThreshold.TotalSeconds
                    });
                }
            }
        }

        /// <summary>
        /// Disposes the rate limiter and releases resources.
        /// </summary>
        public void Dispose()
        {
            if (_disposed)
                return;

            _disposed = true;
            _cleanupTimer?.Dispose();
            _endpointTrackers.Clear();
        }
    }

    /// <summary>
    /// Tracks message frequency and patterns for a specific endpoint.
    /// </summary>
    internal class EndpointTracker
    {
        private readonly string _endpointKey;
        private readonly UsdpConfiguration _config;
        private readonly Queue<MessageRecord> _messageHistory = new();
        private readonly Dictionary<int, int> _messageSizeDistribution = new();
        private readonly object _lock = new object();
        
        public DateTime LastActivity { get; private set; } = DateTime.UtcNow;
        public int TotalMessages { get; private set; }
        public int RejectedMessages { get; private set; }

        public EndpointTracker(string endpointKey, UsdpConfiguration config)
        {
            _endpointKey = endpointKey;
            _config = config;
        }

        public RateLimitResult CheckMessage(DateTime now, int messageSize)
        {
            lock (_lock)
            {
                LastActivity = now;
                TotalMessages++;

                // Clean up old messages outside the time window
                CleanupOldMessages(now);

                // Check basic rate limit
                var currentCount = _messageHistory.Count;
                var maxMessages = _config.RateLimitMaxMessagesPerWindow;
                var burstLimit = (int)(maxMessages * _config.RateLimitBurstFactor);

                if (currentCount >= burstLimit)
                {
                    RejectedMessages++;
                    
                    UsdpLogger.Log("RateLimiter.MessageRejected", new
                    {
                        Endpoint = _endpointKey,
                        CurrentCount = currentCount,
                        BurstLimit = burstLimit,
                        MessageSize = messageSize,
                        Reason = "BurstLimitExceeded",
                        Severity = "Warning"
                    });

                    return RateLimitResult.Rejected("Burst limit exceeded", currentCount, burstLimit);
                }

                // Check for enhanced DoS protection patterns
                if (_config.EnableEnhancedDosProtection)
                {
                    var suspiciousResult = CheckSuspiciousPatterns(messageSize);
                    if (!suspiciousResult.IsAllowed)
                    {
                        RejectedMessages++;
                        return suspiciousResult;
                    }
                }

                // Record the message
                _messageHistory.Enqueue(new MessageRecord(now, messageSize));
                UpdateMessageSizeDistribution(messageSize);

                return RateLimitResult.Allowed();
            }
        }

        public RateLimitStats GetStats(DateTime now)
        {
            lock (_lock)
            {
                CleanupOldMessages(now);
                
                return new RateLimitStats
                {
                    EndpointKey = _endpointKey,
                    CurrentMessageCount = _messageHistory.Count,
                    TotalMessages = TotalMessages,
                    RejectedMessages = RejectedMessages,
                    LastActivity = LastActivity,
                    MessageSizeDistribution = new Dictionary<int, int>(_messageSizeDistribution)
                };
            }
        }

        private void CleanupOldMessages(DateTime now)
        {
            var cutoff = now - _config.RateLimitTimeWindow;
            
            while (_messageHistory.Count > 0 && _messageHistory.Peek().Timestamp < cutoff)
            {
                _messageHistory.Dequeue();
            }
        }

        private RateLimitResult CheckSuspiciousPatterns(int messageSize)
        {
            // Check for suspicious message size patterns
            if (_messageSizeDistribution.Count > 10) // Only check if we have enough data
            {
                var totalMessages = _messageSizeDistribution.Values.Sum();
                var dominantSizeCount = _messageSizeDistribution.Values.Max();
                var dominantSizeRatio = (double)dominantSizeCount / totalMessages;

                if (dominantSizeRatio > _config.SuspiciousMessageSizeThreshold)
                {
                    UsdpLogger.Log("RateLimiter.SuspiciousPattern", new
                    {
                        Endpoint = _endpointKey,
                        DominantSizeRatio = dominantSizeRatio,
                        Threshold = _config.SuspiciousMessageSizeThreshold,
                        TotalMessages = totalMessages,
                        Reason = "SuspiciousMessageSizePattern",
                        Severity = "Warning"
                    });

                    return RateLimitResult.Rejected("Suspicious message size pattern detected", 
                        totalMessages, (int)(_config.SuspiciousMessageSizeThreshold * totalMessages));
                }
            }

            return RateLimitResult.Allowed();
        }

        private void UpdateMessageSizeDistribution(int messageSize)
        {
            // Group message sizes into ranges for pattern detection
            var sizeRange = (messageSize / 100) * 100; // Group by 100-byte ranges
            
            if (_messageSizeDistribution.ContainsKey(sizeRange))
            {
                _messageSizeDistribution[sizeRange]++;
            }
            else
            {
                _messageSizeDistribution[sizeRange] = 1;
            }

            // Limit the size of the distribution map to prevent memory leaks
            if (_messageSizeDistribution.Count > 50)
            {
                var oldestEntry = _messageSizeDistribution.OrderBy(kvp => kvp.Value).First();
                _messageSizeDistribution.Remove(oldestEntry.Key);
            }
        }
    }

    /// <summary>
    /// Represents a message record for rate limiting tracking.
    /// </summary>
    internal record MessageRecord(DateTime Timestamp, int Size);
}
