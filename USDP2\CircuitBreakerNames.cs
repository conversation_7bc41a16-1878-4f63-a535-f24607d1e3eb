namespace USDP2
{
    /// <summary>
    /// Centralized constants for circuit breaker names to prevent typos and ensure consistency.
    /// This class provides compile-time safety for circuit breaker name references.
    /// </summary>
    public static class CircuitBreakerNames
    {
        /// <summary>
        /// Circuit breaker name for MDNS proxy publish operations.
        /// </summary>
        public const string MdnsPublish = "MdnsProxy.Publish";

        /// <summary>
        /// Circuit breaker name for MDNS proxy import operations.
        /// </summary>
        public const string MdnsImport = "MdnsProxy.Import";

        /// <summary>
        /// Circuit breaker name for HTTP network sender operations.
        /// </summary>
        public const string HttpNetworkSender = "HttpNetworkSender";

        /// <summary>
        /// Circuit breaker name for UDP network sender operations.
        /// </summary>
        public const string UdpNetworkSender = "UdpNetworkSender";

        /// <summary>
        /// Circuit breaker name for TCP network sender operations.
        /// </summary>
        public const string TcpNetworkSender = "TcpNetworkSender";

        /// <summary>
        /// Circuit breaker name for HTTP network receiver operations.
        /// </summary>
        public const string HttpNetworkReceiver = "HttpNetworkReceiver";

        /// <summary>
        /// Circuit breaker name for UDP network receiver operations.
        /// </summary>
        public const string UdpNetworkReceiver = "UdpNetworkReceiver";

        /// <summary>
        /// Circuit breaker name for directory node operations.
        /// </summary>
        public const string DirectoryNode = "DirectoryNode";

        /// <summary>
        /// Circuit breaker name for chord node operations.
        /// </summary>
        public const string ChordNode = "ChordNode";

        /// <summary>
        /// Circuit breaker name for key management provider operations.
        /// </summary>
        public const string KeyManagementProvider = "KeyManagementProvider";

        /// <summary>
        /// Circuit breaker name for DNS client operations.
        /// </summary>
        public const string DnsClient = "DnsClient";

        /// <summary>
        /// Circuit breaker name for service discovery operations.
        /// </summary>
        public const string ServiceDiscovery = "ServiceDiscovery";

        /// <summary>
        /// Gets all defined circuit breaker names for validation and monitoring purposes.
        /// </summary>
        /// <returns>An array of all circuit breaker names.</returns>
        public static string[] GetAllNames()
        {
            return new[]
            {
                MdnsPublish,
                MdnsImport,
                HttpNetworkSender,
                UdpNetworkSender,
                TcpNetworkSender,
                HttpNetworkReceiver,
                UdpNetworkReceiver,
                DirectoryNode,
                ChordNode,
                KeyManagementProvider,
                DnsClient,
                ServiceDiscovery
            };
        }

        /// <summary>
        /// Validates that a circuit breaker name is one of the predefined constants.
        /// </summary>
        /// <param name="name">The name to validate.</param>
        /// <returns>True if the name is valid, false otherwise.</returns>
        public static bool IsValidName(string name)
        {
            if (string.IsNullOrWhiteSpace(name))
                return false;

            var allNames = GetAllNames();
            return Array.Exists(allNames, n => string.Equals(n, name, StringComparison.Ordinal));
        }
    }
}
