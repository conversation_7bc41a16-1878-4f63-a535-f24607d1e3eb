using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace USDP2
{
    /// <summary>
    /// Advanced configuration loader that supports environment-specific configuration files
    /// and hierarchical configuration merging.
    /// 
    /// This loader supports the following configuration file hierarchy:
    /// 1. Base configuration: usdp_config.json
    /// 2. Environment-specific: usdp_config.{environment}.json
    /// 3. Local overrides: usdp_config.local.json
    /// 4. Environment variables: USDP_*
    /// 
    /// Configuration sources are merged in order, with later sources overriding earlier ones.
    /// </summary>
    public class EnvironmentConfigurationLoader
    {
        private readonly string _baseConfigPath;
        private readonly UsdpConfiguration.DeploymentEnvironment _environment;
        private readonly ILogger? _logger;

        /// <summary>
        /// Initializes a new instance of the EnvironmentConfigurationLoader.
        /// </summary>
        /// <param name="baseConfigPath">Base directory for configuration files.</param>
        /// <param name="environment">Target environment for configuration loading.</param>
        /// <param name="logger">Optional logger for configuration loading events.</param>
        public EnvironmentConfigurationLoader(
            string? baseConfigPath = null,
            UsdpConfiguration.DeploymentEnvironment? environment = null,
            ILogger? logger = null)
        {
            _baseConfigPath = baseConfigPath ?? GetDefaultConfigPath();
            _environment = environment ?? UsdpConfiguration.Instance.Environment;
            _logger = logger;
        }

        /// <summary>
        /// Loads and applies configuration from all available sources.
        /// </summary>
        /// <returns>A task representing the asynchronous operation.</returns>
        public async Task LoadConfigurationAsync()
        {
            var config = UsdpConfiguration.Instance;
            
            _logger?.LogInformation("Loading configuration for environment: {Environment}", _environment);
            
            // 1. Apply environment defaults first
            config.Environment = _environment;
            config.ApplyEnvironmentDefaults();
            
            // 2. Load base configuration
            await LoadConfigurationFile("usdp_config.json");
            
            // 3. Load environment-specific configuration
            var envConfigFile = $"usdp_config.{_environment.ToString().ToLowerInvariant()}.json";
            await LoadConfigurationFile(envConfigFile);
            
            // 4. Load local overrides
            await LoadConfigurationFile("usdp_config.local.json");
            
            // 5. Apply environment variable overrides
            ApplyEnvironmentVariables();
            
            _logger?.LogInformation("Configuration loading completed for environment: {Environment}", _environment);
        }

        /// <summary>
        /// Loads configuration from a specific file if it exists.
        /// </summary>
        /// <param name="fileName">Name of the configuration file.</param>
        private async Task LoadConfigurationFile(string fileName)
        {
            var filePath = Path.Combine(_baseConfigPath, fileName);
            
            if (!File.Exists(filePath))
            {
                _logger?.LogDebug("Configuration file not found: {FilePath}", filePath);
                return;
            }

            try
            {
                _logger?.LogInformation("Loading configuration from: {FilePath}", filePath);
                
                var configProvider = new ConfigurationProvider(filePath);
                await configProvider.LoadAsync();
                await configProvider.ApplyToUsdpConfigurationAsync();
                
                _logger?.LogInformation("Successfully loaded configuration from: {FilePath}", filePath);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to load configuration from: {FilePath}", filePath);
                throw new InvalidOperationException($"Failed to load configuration from {filePath}", ex);
            }
        }

        /// <summary>
        /// Applies configuration overrides from environment variables.
        /// </summary>
        private void ApplyEnvironmentVariables()
        {
            var config = UsdpConfiguration.Instance;
            
            // Network configuration
            ApplyEnvironmentVariable("USDP_MULTICAST_ADDRESS", value => config.DefaultMulticastAddress = value);
            ApplyEnvironmentVariable("USDP_MULTICAST_PORT", value => 
            {
                if (int.TryParse(value, out var port))
                    config.DefaultMulticastPort = port;
            });
            ApplyEnvironmentVariable("USDP_HTTP_PORT", value => 
            {
                if (int.TryParse(value, out var port))
                    config.DefaultHttpPort = port;
            });
            ApplyEnvironmentVariable("USDP_HTTPS_PORT", value => 
            {
                if (int.TryParse(value, out var port))
                    config.DefaultHttpsPort = port;
            });
            
            // Security configuration
            ApplyEnvironmentVariable("USDP_USE_HTTPS", value => 
            {
                if (bool.TryParse(value, out var useHttps))
                    config.UseHttps = useHttps;
            });
            ApplyEnvironmentVariable("USDP_REQUIRE_AUTH", value => 
            {
                if (bool.TryParse(value, out var requireAuth))
                    config.RequireAuthentication = requireAuth;
            });
            ApplyEnvironmentVariable("USDP_DEFAULT_SECURITY", value => config.DefaultSecurity = value);
            
            // DNS configuration
            ApplyEnvironmentVariable("USDP_DNS_DOMAIN", value => config.DefaultDnsDomain = value);
            ApplyEnvironmentVariable("USDP_DNS_PROVIDER", value => config.DefaultDnsProvider = value);
            
            // Logging configuration
            ApplyEnvironmentVariable("USDP_LOG_LEVEL", value => 
            {
                if (Enum.TryParse<LogLevel>(value, true, out var logLevel))
                    config.MinimumLogLevel = logLevel;
            });
            ApplyEnvironmentVariable("USDP_LOG_MODE", value => 
            {
                if (Enum.TryParse<UsdpConfiguration.LoggingMode>(value, true, out var logMode))
                    config.LogMode = logMode;
            });
            
            // Timeout configuration
            ApplyEnvironmentVariable("USDP_NETWORK_TIMEOUT", value => 
            {
                if (int.TryParse(value, out var seconds))
                    config.NetworkTimeout = TimeSpan.FromSeconds(seconds);
            });
            
            _logger?.LogDebug("Applied environment variable overrides");
        }

        /// <summary>
        /// Applies a single environment variable if it exists.
        /// </summary>
        /// <param name="variableName">Name of the environment variable.</param>
        /// <param name="applyAction">Action to apply the value.</param>
        private void ApplyEnvironmentVariable(string variableName, Action<string> applyAction)
        {
            var value = Environment.GetEnvironmentVariable(variableName);
            if (!string.IsNullOrEmpty(value))
            {
                try
                {
                    applyAction(value);
                    _logger?.LogDebug("Applied environment variable: {Variable} = {Value}", variableName, value);
                }
                catch (Exception ex)
                {
                    _logger?.LogWarning(ex, "Failed to apply environment variable: {Variable} = {Value}", variableName, value);
                }
            }
        }

        /// <summary>
        /// Gets the default configuration path based on the current platform.
        /// </summary>
        private static string GetDefaultConfigPath()
        {
            // Try application directory first
            var appDir = AppDomain.CurrentDomain.BaseDirectory;
            if (Directory.Exists(appDir))
            {
                return appDir;
            }
            
            // Fall back to current directory
            return Directory.GetCurrentDirectory();
        }

        /// <summary>
        /// Creates environment-specific configuration files with appropriate defaults.
        /// </summary>
        /// <param name="outputPath">Directory to create configuration files in.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        public static async Task CreateEnvironmentConfigurationFilesAsync(string outputPath)
        {
            Directory.CreateDirectory(outputPath);
            
            // Create base configuration
            await CreateConfigurationFile(outputPath, "usdp_config.json", CreateBaseConfiguration());
            
            // Create environment-specific configurations
            await CreateConfigurationFile(outputPath, "usdp_config.development.json", CreateDevelopmentConfiguration());
            await CreateConfigurationFile(outputPath, "usdp_config.testing.json", CreateTestingConfiguration());
            await CreateConfigurationFile(outputPath, "usdp_config.staging.json", CreateStagingConfiguration());
            await CreateConfigurationFile(outputPath, "usdp_config.production.json", CreateProductionConfiguration());
            
            // Create local override template
            await CreateConfigurationFile(outputPath, "usdp_config.local.json.template", CreateLocalConfiguration());
        }

        /// <summary>
        /// Creates a configuration file with the specified content.
        /// </summary>
        private static async Task CreateConfigurationFile(string path, string fileName, object configuration)
        {
            var filePath = Path.Combine(path, fileName);
            var json = JsonSerializer.Serialize(configuration, new JsonSerializerOptions 
            { 
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });
            
            await File.WriteAllTextAsync(filePath, json);
        }

        /// <summary>
        /// Creates base configuration object.
        /// </summary>
        private static object CreateBaseConfiguration()
        {
            return new
            {
                DefaultMulticastAddress = "***************",
                DefaultMulticastPort = 5353,
                DefaultHttpPort = 8080,
                DefaultHttpsPort = 8443,
                UseHttps = true,
                RequireAuthentication = true,
                DefaultSecurity = "psk-tls1.3",
                NetworkTimeoutMs = 30000,
                QueryResponseTimeoutMs = 2000
            };
        }

        /// <summary>
        /// Creates development environment configuration.
        /// </summary>
        private static object CreateDevelopmentConfiguration()
        {
            return new
            {
                UseHttps = false,
                RequireAuthentication = false,
                DefaultSecurity = "none",
                MinimumLogLevel = "Debug",
                LogMode = "Console",
                NetworkTimeoutMs = 10000,
                QueryResponseTimeoutMs = 1000,
                EnableRetryPolicies = false
            };
        }

        /// <summary>
        /// Creates testing environment configuration.
        /// </summary>
        private static object CreateTestingConfiguration()
        {
            return new
            {
                UseHttps = true,
                RequireAuthentication = false,
                DefaultSecurity = "psk-tls1.3",
                MinimumLogLevel = "Information",
                LogMode = "File",
                NetworkTimeoutMs = 15000,
                QueryResponseTimeoutMs = 2000,
                EnableRetryPolicies = true
            };
        }

        /// <summary>
        /// Creates staging environment configuration.
        /// </summary>
        private static object CreateStagingConfiguration()
        {
            return new
            {
                UseHttps = true,
                RequireAuthentication = true,
                DefaultSecurity = "psk-tls1.3",
                MinimumLogLevel = "Information",
                LogMode = "Auto",
                NetworkTimeoutMs = 30000,
                QueryResponseTimeoutMs = 2000,
                EnableRetryPolicies = true
            };
        }

        /// <summary>
        /// Creates production environment configuration.
        /// </summary>
        private static object CreateProductionConfiguration()
        {
            return new
            {
                UseHttps = true,
                RequireAuthentication = true,
                DefaultSecurity = "psk-tls1.3",
                MinimumLogLevel = "Warning",
                LogMode = "Auto",
                NetworkTimeoutMs = 30000,
                QueryResponseTimeoutMs = 2000,
                EnableRetryPolicies = true,
                EnableCircuitBreakers = true
            };
        }

        /// <summary>
        /// Creates local override configuration template.
        /// </summary>
        private static object CreateLocalConfiguration()
        {
            return new
            {
                _comment = "This file contains local configuration overrides. Copy to usdp_config.local.json and modify as needed.",
                DefaultMulticastPort = 5354,
                DefaultHttpPort = 8081,
                DefaultHttpsPort = 8444,
                Keys = new
                {
                    AuthPsk = "your-local-psk-key-here"
                }
            };
        }
    }
}
