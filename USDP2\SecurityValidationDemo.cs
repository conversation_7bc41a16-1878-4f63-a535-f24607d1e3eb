using System;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace USDP2
{
    /// <summary>
    /// Demonstrates the enhanced security validation features including rate limiting,
    /// DoS protection, and comprehensive security monitoring.
    /// 
    /// This class provides examples of how the security features work and can be used
    /// for testing and validation of the security enhancements.
    /// </summary>
    public class SecurityValidationDemo
    {
        private readonly DirectoryNode _directoryNode;
        private readonly UsdpConfiguration _config;

        /// <summary>
        /// Initializes a new instance of the SecurityValidationDemo class.
        /// </summary>
        /// <param name="directoryNode">The directory node to demonstrate with.</param>
        /// <param name="config">The configuration instance.</param>
        public SecurityValidationDemo(DirectoryNode directoryNode, UsdpConfiguration? config = null)
        {
            _directoryNode = directoryNode ?? throw new ArgumentNullException(nameof(directoryNode));
            _config = config ?? UsdpConfiguration.Instance;
        }

        /// <summary>
        /// Demonstrates rate limiting functionality by simulating message floods.
        /// </summary>
        /// <returns>A task representing the demonstration.</returns>
        public async Task DemonstrateRateLimitingAsync()
        {
            Console.WriteLine("=== Rate Limiting Demonstration ===");
            Console.WriteLine($"Rate limit: {_config.RateLimitMaxMessagesPerWindow} messages per {_config.RateLimitTimeWindow.TotalSeconds} seconds");
            Console.WriteLine($"Burst factor: {_config.RateLimitBurstFactor}x");
            Console.WriteLine();

            // Create a test message
            var testMessage = CreateTestServiceAdvertisement();
            var messageData = testMessage.ToCbor();

            // Simulate rapid message sending to trigger rate limiting
            var testEndpoint = "************0";
            var testPort = 12345;

            Console.WriteLine($"Sending messages rapidly from {testEndpoint}:{testPort}...");

            for (int i = 1; i <= _config.RateLimitMaxMessagesPerWindow + 50; i++)
            {
                try
                {
                    // Simulate message reception (this would normally come from network)
                    await SimulateMessageReception(messageData, testEndpoint, testPort);
                    
                    if (i % 10 == 0)
                    {
                        Console.WriteLine($"Sent {i} messages...");
                        
                        // Show current rate limit statistics
                        var rateLimitStats = _directoryNode.GetRateLimitStatistics();
                        if (rateLimitStats.TryGetValue($"{testEndpoint}:{testPort}", out var stats))
                        {
                            Console.WriteLine($"  Current count: {stats.CurrentMessageCount}, Rejected: {stats.RejectedMessages}");
                        }
                    }

                    // Small delay to simulate realistic timing
                    await Task.Delay(10);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Message {i} failed: {ex.Message}");
                }
            }

            Console.WriteLine("\nFinal rate limiting statistics:");
            DisplayRateLimitStatistics();
            Console.WriteLine();
        }

        /// <summary>
        /// Demonstrates DoS protection by sending various types of malicious payloads.
        /// </summary>
        /// <returns>A task representing the demonstration.</returns>
        public async Task DemonstrateDosProtectionAsync()
        {
            Console.WriteLine("=== DoS Protection Demonstration ===");
            Console.WriteLine();

            var attackerEndpoint = "10.0.0.1";
            var attackerPort = 54321;

            // Test 1: Oversized payload
            Console.WriteLine("Test 1: Sending oversized payload...");
            var oversizedData = new byte[_config.MaxNetworkDataSize + 1000];
            Array.Fill(oversizedData, (byte)'A');
            await SimulateMessageReception(oversizedData, attackerEndpoint, attackerPort);

            // Test 2: Suspicious binary patterns
            Console.WriteLine("Test 2: Sending payload with suspicious binary patterns...");
            var suspiciousData = new byte[1000];
            Array.Fill(suspiciousData, (byte)0); // All null bytes
            await SimulateMessageReception(suspiciousData, attackerEndpoint, attackerPort);

            // Test 3: Highly repetitive data
            Console.WriteLine("Test 3: Sending highly repetitive data...");
            var repetitiveData = Encoding.UTF8.GetBytes(new string('X', 2000));
            await SimulateMessageReception(repetitiveData, attackerEndpoint, attackerPort);

            // Test 4: Very small payload (scanning detection)
            Console.WriteLine("Test 4: Sending very small payload...");
            var tinyData = new byte[] { 0x01, 0x02 };
            await SimulateMessageReception(tinyData, attackerEndpoint, attackerPort);

            // Test 5: Buffer-sized payload
            Console.WriteLine("Test 5: Sending buffer-sized payload...");
            var bufferSizedData = new byte[4096]; // Common buffer size
            Array.Fill(bufferSizedData, (byte)'B');
            await SimulateMessageReception(bufferSizedData, attackerEndpoint, attackerPort);

            Console.WriteLine("\nDoS protection results:");
            DisplaySecurityStatistics();
            Console.WriteLine();
        }

        /// <summary>
        /// Demonstrates security event tracking and analysis.
        /// </summary>
        /// <returns>A task representing the demonstration.</returns>
        public async Task DemonstrateSecurityTrackingAsync()
        {
            Console.WriteLine("=== Security Event Tracking Demonstration ===");
            Console.WriteLine();

            // Simulate various security events from different endpoints
            var endpoints = new[]
            {
                ("************", 1001),
                ("************", 1002),
                ("********", 2001),
                ("************", 3001)
            };

            foreach (var (address, port) in endpoints)
            {
                Console.WriteLine($"Simulating security events from {address}:{port}...");
                
                // Send some normal messages first
                var normalMessage = CreateTestServiceAdvertisement();
                for (int i = 0; i < 5; i++)
                {
                    await SimulateMessageReception(normalMessage.ToCbor(), address, port);
                    await Task.Delay(100);
                }

                // Then send some suspicious messages
                var maliciousData = new byte[500];
                Array.Fill(maliciousData, (byte)0xFF);
                await SimulateMessageReception(maliciousData, address, port);

                // Send invalid JSON
                var invalidJson = Encoding.UTF8.GetBytes("{\"invalid\": json data with <script>alert('xss')</script>}");
                await SimulateMessageReception(invalidJson, address, port);
            }

            Console.WriteLine("\nSecurity tracking results:");
            DisplaySecurityStatistics();
            Console.WriteLine();
        }

        /// <summary>
        /// Demonstrates manual endpoint blocking and unblocking.
        /// </summary>
        public void DemonstrateEndpointBlocking()
        {
            Console.WriteLine("=== Endpoint Blocking Demonstration ===");
            Console.WriteLine();

            var suspiciousEndpoint = "*************";
            var suspiciousPort = 9999;

            Console.WriteLine($"Blocking endpoint {suspiciousEndpoint}:{suspiciousPort} for security reasons...");
            _directoryNode.BlockEndpoint(suspiciousEndpoint, suspiciousPort, 
                "Demonstration of manual blocking", TimeSpan.FromMinutes(5));

            Console.WriteLine("Attempting to send message from blocked endpoint...");
            var testMessage = CreateTestServiceAdvertisement();
            _ = SimulateMessageReception(testMessage.ToCbor(), suspiciousEndpoint, suspiciousPort);

            Console.WriteLine($"Unblocking endpoint {suspiciousEndpoint}:{suspiciousPort}...");
            _directoryNode.UnblockEndpoint(suspiciousEndpoint, suspiciousPort);

            Console.WriteLine("Endpoint blocking demonstration complete.");
            Console.WriteLine();
        }

        /// <summary>
        /// Displays current rate limiting statistics.
        /// </summary>
        private void DisplayRateLimitStatistics()
        {
            var stats = _directoryNode.GetRateLimitStatistics();
            
            Console.WriteLine($"Rate limiting statistics for {stats.Count} endpoints:");
            foreach (var kvp in stats)
            {
                var stat = kvp.Value;
                Console.WriteLine($"  {stat.EndpointKey}:");
                Console.WriteLine($"    Current: {stat.CurrentMessageCount}, Total: {stat.TotalMessages}");
                Console.WriteLine($"    Rejected: {stat.RejectedMessages} ({stat.RejectionRate:F1}%)");
                Console.WriteLine($"    Suspicious: {stat.IsSuspicious}, Last Activity: {stat.LastActivity:HH:mm:ss}");
            }
        }

        /// <summary>
        /// Displays current security statistics.
        /// </summary>
        private void DisplaySecurityStatistics()
        {
            var stats = _directoryNode.GetSecurityStatistics();
            
            Console.WriteLine($"Security Health Score: {stats.SecurityHealthScore}/100");
            Console.WriteLine($"Total Endpoints: {stats.TotalEndpoints}");
            Console.WriteLine($"Suspicious Endpoints: {stats.SuspiciousEndpoints}");
            Console.WriteLine($"Blocked Endpoints: {stats.BlockedEndpoints}");
            Console.WriteLine($"Recent Events: {stats.RecentEventsCount}");
            
            if (stats.EventsByType.Count > 0)
            {
                Console.WriteLine("Events by Type:");
                foreach (var kvp in stats.EventsByType)
                {
                    Console.WriteLine($"  {kvp.Key}: {kvp.Value}");
                }
            }

            if (stats.TopSuspiciousEndpoints.Count > 0)
            {
                Console.WriteLine("Top Suspicious Endpoints:");
                foreach (var endpoint in stats.TopSuspiciousEndpoints)
                {
                    Console.WriteLine($"  {endpoint.EndpointKey}: Threat Score {endpoint.ThreatScore:F1}");
                }
            }
        }

        /// <summary>
        /// Creates a test service advertisement for demonstrations.
        /// </summary>
        /// <returns>A test service advertisement.</returns>
        private static ServiceAdvertisement CreateTestServiceAdvertisement()
        {
            var serviceId = new ServiceIdentifier("demo", "test-service");
            var endpoint = new TransportEndpoint
            {
                Protocol = "udp",
                Address = "************",
                Port = 8080,
                Security = "none"
            };

            return new ServiceAdvertisement(serviceId, endpoint)
            {
                Metadata = new Dictionary<string, object>
                {
                    ["version"] = "1.0",
                    ["description"] = "Test service for security demonstration"
                },
                Ttl = TimeSpan.FromMinutes(30)
            };
        }

        /// <summary>
        /// Simulates message reception for testing purposes.
        /// </summary>
        /// <param name="data">The message data.</param>
        /// <param name="remoteAddress">The remote address.</param>
        /// <param name="remotePort">The remote port.</param>
        /// <returns>A task representing the simulation.</returns>
        private async Task SimulateMessageReception(byte[] data, string remoteAddress, int remotePort)
        {
            // This would normally be called by the network receiver
            // For demonstration, we'll use reflection to call the private method
            var method = typeof(DirectoryNode).GetMethod("OnMessageReceivedAsync", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
            if (method != null)
            {
                var task = (Task)method.Invoke(_directoryNode, new object[] { data, remoteAddress, remotePort })!;
                await task;
            }
        }
    }
}
