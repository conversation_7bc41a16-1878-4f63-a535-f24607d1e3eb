using System;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using USDP2.Tests.Mocks;

namespace USDP2.Tests
{
    /// <summary>
    /// Integration tests for the complete security validation system including rate limiting,
    /// DoS protection, security event tracking, and their interactions.
    /// 
    /// These tests verify the end-to-end security functionality and demonstrate how
    /// all security components work together to protect against various attack scenarios.
    /// </summary>
    [TestClass]
    public class SecurityValidationIntegrationTests
    {
        private UsdpConfiguration _testConfig = null!;
        private DirectoryNode _directoryNode = null!;
        private MockNetworkSender _mockSender = null!;
        private MockNetworkReceiver _mockReceiver = null!;

        [TestInitialize]
        public void Setup()
        {
            _testConfig = new UsdpConfiguration(forTesting: true)
            {
                EnableRateLimiting = true,
                RateLimitMaxMessagesPerWindow = 10,
                RateLimitTimeWindow = TimeSpan.FromSeconds(5),
                RateLimitBurstFactor = 2.0,
                EnableEnhancedDosProtection = true,
                SuspiciousMessageSizeThreshold = 0.8,
                MaxNetworkDataSize = 2048,
                MaxJsonDataSize = 1024,
                RejectSuspiciousBinaryPatterns = true
            };

            _mockSender = new MockNetworkSender();
            _mockReceiver = new MockNetworkReceiver();
            _directoryNode = new DirectoryNode(_mockSender, _mockReceiver, _testConfig);
        }

        [TestCleanup]
        public async Task Cleanup()
        {
            await _directoryNode.DisposeAsync();
        }

        [TestMethod]
        public async Task CompleteAttackScenario_ShouldDetectAndMitigate()
        {
            // This test simulates a complete attack scenario with multiple attack vectors
            var attackerAddress = "********";
            var attackerPort = 54321;

            // Phase 1: Reconnaissance - Small probing messages
            Console.WriteLine("Phase 1: Reconnaissance attack");
            for (int i = 0; i < 3; i++)
            {
                var probeData = new byte[] { 0x01, 0x02 }; // Very small payload
                await _mockReceiver.SimulateMessageReception(probeData, attackerAddress, attackerPort);
            }

            // Phase 2: Message flooding attack
            Console.WriteLine("Phase 2: Message flooding attack");
            var validMessage = CreateValidServiceAdvertisement();
            var messageData = validMessage.ToCbor();
            var burstLimit = (int)(_testConfig.RateLimitMaxMessagesPerWindow * _testConfig.RateLimitBurstFactor);

            for (int i = 0; i < burstLimit + 5; i++) // Exceed burst limit
            {
                await _mockReceiver.SimulateMessageReception(messageData, attackerAddress, attackerPort);
            }

            // Phase 3: Payload-based attacks
            Console.WriteLine("Phase 3: Payload-based attacks");
            
            // Oversized payload
            var oversizedData = new byte[_testConfig.MaxNetworkDataSize + 100];
            Array.Fill(oversizedData, (byte)'A');
            await _mockReceiver.SimulateMessageReception(oversizedData, attackerAddress, attackerPort);

            // Suspicious binary patterns
            var suspiciousData = new byte[1000];
            Array.Fill(suspiciousData, (byte)0); // All null bytes
            await _mockReceiver.SimulateMessageReception(suspiciousData, attackerAddress, attackerPort);

            // Malicious JSON
            var maliciousJson = "{\"script\": \"<script>alert('xss')</script>\", \"data\": \"" + new string('X', 500) + "\"}";
            var maliciousData = Encoding.UTF8.GetBytes(maliciousJson);
            await _mockReceiver.SimulateMessageReception(maliciousData, attackerAddress, attackerPort);

            // Assert - Verify security system detected and mitigated the attacks
            var securityStats = _directoryNode.GetSecurityStatistics();
            var rateLimitStats = _directoryNode.GetRateLimitStatistics();

            // Check that security events were recorded
            Assert.IsTrue(securityStats.RecentEventsCount > 0, "Should have recorded security events");
            Assert.IsTrue(securityStats.SecurityHealthScore < 100, "Security health score should be degraded");
            
            // Check specific event types
            Assert.IsTrue(securityStats.EventsByType.ContainsKey(SecurityEventType.SuspiciousContent) ||
                         securityStats.EventsByType.ContainsKey(SecurityEventType.InvalidData), 
                         "Should detect suspicious content");
            Assert.IsTrue(securityStats.EventsByType.ContainsKey(SecurityEventType.RateLimitExceeded), 
                         "Should detect rate limit violations");
            Assert.IsTrue(securityStats.EventsByType.ContainsKey(SecurityEventType.OversizedMessage), 
                         "Should detect oversized messages");

            // Check rate limiting worked
            var endpointKey = $"{attackerAddress}:{attackerPort}";
            Assert.IsTrue(rateLimitStats.ContainsKey(endpointKey), "Should track attacker endpoint");
            Assert.IsTrue(rateLimitStats[endpointKey].RejectedMessages > 0, "Should have rejected messages");

            // Check threat detection
            Assert.IsTrue(securityStats.SuspiciousEndpoints > 0, "Should identify suspicious endpoints");

            Console.WriteLine($"Attack mitigation results:");
            Console.WriteLine($"- Security Health Score: {securityStats.SecurityHealthScore}/100");
            Console.WriteLine($"- Total Events: {securityStats.RecentEventsCount}");
            Console.WriteLine($"- Suspicious Endpoints: {securityStats.SuspiciousEndpoints}");
            Console.WriteLine($"- Messages Rejected: {rateLimitStats[endpointKey].RejectedMessages}");
        }

        [TestMethod]
        public async Task LegitimateTrafficMixed_ShouldAllowGoodTrafficWhileBlockingBad()
        {
            // Simulate mixed traffic - legitimate users and attackers
            var legitimateUser = "*************";
            var attacker = "********";
            var port = 12345;

            var validMessage = CreateValidServiceAdvertisement();
            var messageData = validMessage.ToCbor();

            // Legitimate user sends normal traffic
            for (int i = 0; i < 5; i++)
            {
                await _mockReceiver.SimulateMessageReception(messageData, legitimateUser, port);
                await Task.Delay(100); // Normal spacing
            }

            // Attacker floods with messages
            for (int i = 0; i < 25; i++) // Well over the limit
            {
                await _mockReceiver.SimulateMessageReception(messageData, attacker, port);
            }

            // Legitimate user continues normal operation
            for (int i = 0; i < 3; i++)
            {
                await _mockReceiver.SimulateMessageReception(messageData, legitimateUser, port);
                await Task.Delay(100);
            }

            // Assert
            var rateLimitStats = _directoryNode.GetRateLimitStatistics();
            var securityStats = _directoryNode.GetSecurityStatistics();

            var legitKey = $"{legitimateUser}:{port}";
            var attackerKey = $"{attacker}:{port}";

            // Legitimate user should have low rejection rate
            Assert.IsTrue(rateLimitStats.ContainsKey(legitKey), "Should track legitimate user");
            Assert.IsTrue(rateLimitStats[legitKey].RejectionRate < 10, "Legitimate user should have low rejection rate");

            // Attacker should have high rejection rate
            Assert.IsTrue(rateLimitStats.ContainsKey(attackerKey), "Should track attacker");
            Assert.IsTrue(rateLimitStats[attackerKey].RejectionRate > 50, "Attacker should have high rejection rate");

            // System should identify the attacker as suspicious
            Assert.IsTrue(securityStats.SuspiciousEndpoints > 0, "Should identify suspicious endpoints");
        }

        [TestMethod]
        public async Task CoordinatedAttack_ShouldDetectDistributedPattern()
        {
            // Simulate coordinated attack from multiple endpoints
            var attackerEndpoints = new[]
            {
                ("********", 12345),
                ("********", 12346),
                ("********", 12347),
                ("********", 12348)
            };

            var suspiciousData = new byte[100];
            Array.Fill(suspiciousData, (byte)0xFF); // Suspicious pattern

            // All attackers send similar suspicious messages rapidly
            foreach (var (address, port) in attackerEndpoints)
            {
                for (int i = 0; i < 8; i++) // Multiple messages from each
                {
                    await _mockReceiver.SimulateMessageReception(suspiciousData, address, port);
                }
            }

            // Assert
            var securityStats = _directoryNode.GetSecurityStatistics();

            Assert.AreEqual(attackerEndpoints.Length, securityStats.TotalEndpoints, 
                "Should track all attacker endpoints");
            Assert.IsTrue(securityStats.RecentEventsCount >= attackerEndpoints.Length * 8, 
                "Should record events from all attackers");
            Assert.IsTrue(securityStats.SecurityHealthScore < 80, 
                "Health score should be significantly degraded");
        }

        [TestMethod]
        public async Task AutomaticBlocking_ShouldBlockHighThreatEndpoints()
        {
            var maliciousEndpoint = "*************";
            var port = 9999;

            // Send multiple critical security events to trigger automatic blocking
            for (int i = 0; i < 6; i++) // More than the threshold
            {
                var maliciousData = new byte[500];
                Array.Fill(maliciousData, (byte)(i % 256)); // Varied but suspicious
                await _mockReceiver.SimulateMessageReception(maliciousData, maliciousEndpoint, port);
            }

            // Try to send more messages after potential blocking
            var normalMessage = CreateValidServiceAdvertisement();
            await _mockReceiver.SimulateMessageReception(normalMessage.ToCbor(), maliciousEndpoint, port);

            // Assert
            var securityStats = _directoryNode.GetSecurityStatistics();
            
            // Check if endpoint was identified as highly suspicious or blocked
            Assert.IsTrue(securityStats.SuspiciousEndpoints > 0 || securityStats.BlockedEndpoints > 0, 
                "Should identify or block malicious endpoint");
            Assert.IsTrue(securityStats.SecurityHealthScore < 90, 
                "Health score should reflect security concerns");
        }

        [TestMethod]
        public async Task RecoveryAfterAttack_ShouldGraduallyImproveHealthScore()
        {
            var attackerAddress = "********";
            var port = 12345;

            // Phase 1: Attack
            var maliciousData = new byte[1000];
            Array.Fill(maliciousData, (byte)0);
            
            for (int i = 0; i < 10; i++)
            {
                await _mockReceiver.SimulateMessageReception(maliciousData, attackerAddress, port);
            }

            var attackHealthScore = _directoryNode.GetSecurityStatistics().SecurityHealthScore;

            // Phase 2: Normal operation resumes
            var validMessage = CreateValidServiceAdvertisement();
            var normalUser = "*************";
            
            for (int i = 0; i < 5; i++)
            {
                await _mockReceiver.SimulateMessageReception(validMessage.ToCbor(), normalUser, port + i);
                await Task.Delay(50);
            }

            var recoveryHealthScore = _directoryNode.GetSecurityStatistics().SecurityHealthScore;

            // Assert
            Assert.IsTrue(attackHealthScore < 100, "Health score should be degraded during attack");
            // Note: Recovery might be gradual and depend on time-based factors
            // In a real scenario, the health score would improve over time
        }

        [TestMethod]
        public void ManualSecurityManagement_ShouldProvideControlCapabilities()
        {
            var suspiciousEndpoint = "*************";
            var port = 9999;

            // Manual blocking
            _directoryNode.BlockEndpoint(suspiciousEndpoint, port, "Manual security test", TimeSpan.FromMinutes(5));
            
            var statsAfterBlock = _directoryNode.GetSecurityStatistics();
            Assert.AreEqual(1, statsAfterBlock.BlockedEndpoints, "Should show blocked endpoint");

            // Manual unblocking
            _directoryNode.UnblockEndpoint(suspiciousEndpoint, port);
            
            var statsAfterUnblock = _directoryNode.GetSecurityStatistics();
            Assert.AreEqual(0, statsAfterUnblock.BlockedEndpoints, "Should show unblocked endpoint");
        }

        /// <summary>
        /// Creates a valid service advertisement for testing.
        /// </summary>
        private static ServiceAdvertisement CreateValidServiceAdvertisement()
        {
            var serviceId = new ServiceIdentifier("test", "integration-test-service");
            var endpoint = new TransportEndpoint
            {
                Protocol = "http",
                Address = "127.0.0.1",
                Port = 8080,
                Security = "none"
            };

            return new ServiceAdvertisement(serviceId, endpoint)
            {
                Metadata = new Dictionary<string, object>
                {
                    ["version"] = "1.0",
                    ["description"] = "Integration test service"
                },
                Ttl = TimeSpan.FromMinutes(30)
            };
        }
    }
}
