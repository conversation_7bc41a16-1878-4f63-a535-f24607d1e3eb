using System;

namespace USDP2
{
    /// <summary>
    /// Represents the result of a rate limiting check.
    /// Contains information about whether the request was allowed and details about the decision.
    /// </summary>
    public class RateLimitResult
    {
        /// <summary>
        /// Gets a value indicating whether the request is allowed.
        /// </summary>
        public bool IsAllowed { get; }

        /// <summary>
        /// Gets the reason for rejection if the request was not allowed.
        /// </summary>
        public string? RejectionReason { get; }

        /// <summary>
        /// Gets the current count of requests in the time window.
        /// </summary>
        public int CurrentCount { get; }

        /// <summary>
        /// Gets the maximum allowed count for the time window.
        /// </summary>
        public int MaxCount { get; }

        /// <summary>
        /// Gets the timestamp when this result was created.
        /// </summary>
        public DateTime Timestamp { get; }

        /// <summary>
        /// Initializes a new instance of the RateLimitResult class.
        /// </summary>
        /// <param name="isAllowed">Whether the request is allowed.</param>
        /// <param name="rejectionReason">The reason for rejection if not allowed.</param>
        /// <param name="currentCount">The current count of requests.</param>
        /// <param name="maxCount">The maximum allowed count.</param>
        private RateLimitResult(bool isAllowed, string? rejectionReason, int currentCount, int maxCount)
        {
            IsAllowed = isAllowed;
            RejectionReason = rejectionReason;
            CurrentCount = currentCount;
            MaxCount = maxCount;
            Timestamp = DateTime.UtcNow;
        }

        /// <summary>
        /// Creates a result indicating the request is allowed.
        /// </summary>
        /// <param name="currentCount">The current count of requests.</param>
        /// <param name="maxCount">The maximum allowed count.</param>
        /// <returns>A RateLimitResult indicating the request is allowed.</returns>
        public static RateLimitResult Allowed(int currentCount = 0, int maxCount = int.MaxValue)
        {
            return new RateLimitResult(true, null, currentCount, maxCount);
        }

        /// <summary>
        /// Creates a result indicating the request is rejected.
        /// </summary>
        /// <param name="reason">The reason for rejection.</param>
        /// <param name="currentCount">The current count of requests.</param>
        /// <param name="maxCount">The maximum allowed count.</param>
        /// <returns>A RateLimitResult indicating the request is rejected.</returns>
        public static RateLimitResult Rejected(string reason, int currentCount, int maxCount)
        {
            return new RateLimitResult(false, reason, currentCount, maxCount);
        }

        /// <summary>
        /// Returns a string representation of the rate limit result.
        /// </summary>
        /// <returns>A string describing the rate limit result.</returns>
        public override string ToString()
        {
            if (IsAllowed)
            {
                return $"Allowed (Count: {CurrentCount}/{MaxCount})";
            }
            else
            {
                return $"Rejected: {RejectionReason} (Count: {CurrentCount}/{MaxCount})";
            }
        }
    }

    /// <summary>
    /// Represents statistics for rate limiting monitoring.
    /// </summary>
    public class RateLimitStats
    {
        /// <summary>
        /// Gets or sets the endpoint key (IP:Port).
        /// </summary>
        public string EndpointKey { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the current number of messages in the time window.
        /// </summary>
        public int CurrentMessageCount { get; set; }

        /// <summary>
        /// Gets or sets the total number of messages received from this endpoint.
        /// </summary>
        public int TotalMessages { get; set; }

        /// <summary>
        /// Gets or sets the number of messages rejected due to rate limiting.
        /// </summary>
        public int RejectedMessages { get; set; }

        /// <summary>
        /// Gets or sets the timestamp of the last activity from this endpoint.
        /// </summary>
        public DateTime LastActivity { get; set; }

        /// <summary>
        /// Gets or sets the message size distribution for pattern analysis.
        /// Key is the size range, value is the count of messages in that range.
        /// </summary>
        public Dictionary<int, int> MessageSizeDistribution { get; set; } = new();

        /// <summary>
        /// Gets the rejection rate as a percentage.
        /// </summary>
        public double RejectionRate => TotalMessages > 0 ? (double)RejectedMessages / TotalMessages * 100 : 0;

        /// <summary>
        /// Gets a value indicating whether this endpoint shows suspicious patterns.
        /// </summary>
        public bool IsSuspicious => RejectionRate > 10 || HasSuspiciousMessageSizePattern();

        /// <summary>
        /// Checks if the message size distribution shows suspicious patterns.
        /// </summary>
        /// <returns>True if suspicious patterns are detected.</returns>
        private bool HasSuspiciousMessageSizePattern()
        {
            if (MessageSizeDistribution.Count == 0)
                return false;

            var totalMessages = MessageSizeDistribution.Values.Sum();
            if (totalMessages < 10) // Need enough data for pattern analysis
                return false;

            var dominantSizeCount = MessageSizeDistribution.Values.Max();
            var dominantSizeRatio = (double)dominantSizeCount / totalMessages;

            return dominantSizeRatio > 0.8; // More than 80% of messages have the same size pattern
        }

        /// <summary>
        /// Returns a string representation of the rate limit statistics.
        /// </summary>
        /// <returns>A string describing the statistics.</returns>
        public override string ToString()
        {
            return $"Endpoint: {EndpointKey}, Current: {CurrentMessageCount}, " +
                   $"Total: {TotalMessages}, Rejected: {RejectedMessages} ({RejectionRate:F1}%), " +
                   $"Suspicious: {IsSuspicious}, Last Activity: {LastActivity:yyyy-MM-dd HH:mm:ss}";
        }
    }
}
