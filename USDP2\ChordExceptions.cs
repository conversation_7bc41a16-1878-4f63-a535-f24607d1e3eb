using System;

namespace USDP2
{
    /// <summary>
    /// Error codes for Chord-specific operations.
    /// </summary>
    public enum ChordErrorCode
    {
        /// <summary>
        /// Unknown or unspecified error.
        /// </summary>
        Unknown = 0,

        /// <summary>
        /// Network timeout during Chord operation.
        /// </summary>
        NetworkTimeout = 1000,

        /// <summary>
        /// Network connectivity issues.
        /// </summary>
        NetworkConnectivity = 1001,

        /// <summary>
        /// Unknown network error.
        /// </summary>
        UnknownNetworkError = 1002,

        /// <summary>
        /// Network failure after all retry attempts.
        /// </summary>
        NetworkFailureAfterRetries = 1003,

        /// <summary>
        /// Node is not available or unreachable.
        /// </summary>
        NodeUnavailable = 1004,

        /// <summary>
        /// Chord ring is inconsistent or corrupted.
        /// </summary>
        RingInconsistency = 2000,

        /// <summary>
        /// Key lookup failed.
        /// </summary>
        LookupFailure = 2001,

        /// <summary>
        /// Join operation failed.
        /// </summary>
        JoinFailure = 2002,

        /// <summary>
        /// Store operation failed.
        /// </summary>
        StoreFailure = 2003,

        /// <summary>
        /// Unexpected error during operation.
        /// </summary>
        UnexpectedError = 9999
    }

    /// <summary>
    /// Base exception for all Chord-related errors.
    /// </summary>
    public abstract class ChordException : Exception
    {
        /// <summary>
        /// Gets the Chord-specific error code.
        /// </summary>
        public ChordErrorCode ErrorCode { get; }

        /// <summary>
        /// Initializes a new instance of the <see cref="ChordException"/> class.
        /// </summary>
        /// <param name="message">The error message.</param>
        /// <param name="errorCode">The Chord-specific error code.</param>
        protected ChordException(string message, ChordErrorCode errorCode) : base(message)
        {
            ErrorCode = errorCode;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="ChordException"/> class.
        /// </summary>
        /// <param name="message">The error message.</param>
        /// <param name="errorCode">The Chord-specific error code.</param>
        /// <param name="innerException">The inner exception.</param>
        protected ChordException(string message, ChordErrorCode errorCode, Exception? innerException) 
            : base(message, innerException)
        {
            ErrorCode = errorCode;
        }
    }

    /// <summary>
    /// Exception thrown when Chord network operations fail.
    /// </summary>
    public class ChordNetworkException : ChordException
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="ChordNetworkException"/> class.
        /// </summary>
        /// <param name="message">The error message.</param>
        /// <param name="errorCode">The Chord-specific error code.</param>
        public ChordNetworkException(string message, ChordErrorCode errorCode) 
            : base(message, errorCode)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="ChordNetworkException"/> class.
        /// </summary>
        /// <param name="message">The error message.</param>
        /// <param name="errorCode">The Chord-specific error code.</param>
        /// <param name="innerException">The inner exception.</param>
        public ChordNetworkException(string message, ChordErrorCode errorCode, Exception? innerException) 
            : base(message, errorCode, innerException)
        {
        }
    }

    /// <summary>
    /// Exception thrown when Chord serialization operations fail.
    /// </summary>
    public class ChordSerializationException : ChordException
    {
        /// <summary>
        /// Gets the serialization error code.
        /// </summary>
        public SerializationErrorCode SerializationErrorCode { get; }

        /// <summary>
        /// Gets additional context information about the serialization failure.
        /// </summary>
        public object? SerializationContext { get; }

        /// <summary>
        /// Initializes a new instance of the <see cref="ChordSerializationException"/> class.
        /// </summary>
        /// <param name="message">The error message.</param>
        /// <param name="serializationErrorCode">The serialization-specific error code.</param>
        /// <param name="innerException">The inner exception.</param>
        /// <param name="context">Additional context information.</param>
        public ChordSerializationException(string message, SerializationErrorCode serializationErrorCode, Exception? innerException = null, object? context = null) 
            : base(message, ChordErrorCode.UnexpectedError, innerException)
        {
            SerializationErrorCode = serializationErrorCode;
            SerializationContext = context;
        }
    }

    /// <summary>
    /// Exception thrown when Chord lookup operations fail.
    /// </summary>
    public class ChordLookupException : ChordException
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="ChordLookupException"/> class.
        /// </summary>
        /// <param name="message">The error message.</param>
        /// <param name="errorCode">The Chord-specific error code.</param>
        public ChordLookupException(string message, ChordErrorCode errorCode) 
            : base(message, errorCode)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="ChordLookupException"/> class.
        /// </summary>
        /// <param name="message">The error message.</param>
        /// <param name="errorCode">The Chord-specific error code.</param>
        /// <param name="innerException">The inner exception.</param>
        public ChordLookupException(string message, ChordErrorCode errorCode, Exception? innerException) 
            : base(message, errorCode, innerException)
        {
        }
    }

    /// <summary>
    /// Context information for Chord lookup operations.
    /// </summary>
    public class ChordLookupContext
    {
        /// <summary>
        /// Gets the key being looked up.
        /// </summary>
        public System.Numerics.BigInteger Key { get; }

        /// <summary>
        /// Gets the local node identifier.
        /// </summary>
        public string LocalNode { get; }

        /// <summary>
        /// Gets the current attempt number.
        /// </summary>
        public int AttemptNumber { get; private set; }

        /// <summary>
        /// Gets the timestamp when the lookup started.
        /// </summary>
        public DateTimeOffset StartTime { get; }

        /// <summary>
        /// Initializes a new instance of the <see cref="ChordLookupContext"/> class.
        /// </summary>
        /// <param name="key">The key being looked up.</param>
        /// <param name="localNode">The local node identifier.</param>
        public ChordLookupContext(System.Numerics.BigInteger key, string localNode)
        {
            Key = key;
            LocalNode = localNode;
            AttemptNumber = 0;
            StartTime = DateTimeOffset.UtcNow;
        }

        /// <summary>
        /// Increments the attempt number.
        /// </summary>
        public void IncrementAttempt()
        {
            AttemptNumber++;
        }

        /// <summary>
        /// Gets the elapsed time since the lookup started.
        /// </summary>
        public TimeSpan ElapsedTime => DateTimeOffset.UtcNow - StartTime;
    }
}
