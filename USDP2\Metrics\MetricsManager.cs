using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace USDP2.Metrics
{
    /// <summary>
    /// Centralized manager for all metrics collection and reporting in the USDP2 system.
    /// Provides a unified interface for registering collectors and aggregating metrics.
    /// </summary>
    public class MetricsManager : IDisposable
    {
        private readonly ConcurrentDictionary<string, IMetricsCollector> _collectors = new();
        private readonly Timer? _reportingTimer;
        private readonly MetricsConfiguration _configuration;
        private bool _disposed;

        /// <summary>
        /// Initializes a new instance of the <see cref="MetricsManager"/> class.
        /// </summary>
        /// <param name="configuration">The metrics configuration.</param>
        public MetricsManager(MetricsConfiguration? configuration = null)
        {
            _configuration = configuration ?? new MetricsConfiguration();

            if (_configuration.AutoReportingEnabled && _configuration.ReportingInterval > TimeSpan.Zero)
            {
                _reportingTimer = new Timer(AutoReport, null,
                    _configuration.ReportingInterval, _configuration.ReportingInterval);
            }

            // Register default collectors
            RegisterDefaultCollectors();
        }

        /// <summary>
        /// Gets the singleton instance of the metrics manager.
        /// </summary>
        public static MetricsManager Instance { get; } = new MetricsManager();

        /// <summary>
        /// Registers a metrics collector.
        /// </summary>
        /// <param name="collector">The collector to register.</param>
        /// <exception cref="ArgumentNullException">Thrown when collector is null.</exception>
        /// <exception cref="InvalidOperationException">Thrown when a collector with the same name is already registered.</exception>
        public void RegisterCollector(IMetricsCollector collector)
        {
            if (collector == null)
                throw new ArgumentNullException(nameof(collector));

            if (!_collectors.TryAdd(collector.Name, collector))
            {
                throw new InvalidOperationException($"A collector with name '{collector.Name}' is already registered.");
            }
        }

        /// <summary>
        /// Unregisters a metrics collector.
        /// </summary>
        /// <param name="collectorName">The name of the collector to unregister.</param>
        /// <returns>True if the collector was unregistered, false if it wasn't found.</returns>
        public bool UnregisterCollector(string collectorName)
        {
            return _collectors.TryRemove(collectorName, out _);
        }

        /// <summary>
        /// Gets a metrics collector by name.
        /// </summary>
        /// <typeparam name="T">The type of collector to retrieve.</typeparam>
        /// <param name="collectorName">The name of the collector.</param>
        /// <returns>The collector if found, null otherwise.</returns>
        public T? GetCollector<T>(string collectorName) where T : class, IMetricsCollector
        {
            return _collectors.TryGetValue(collectorName, out var collector) ? collector as T : null;
        }

        /// <summary>
        /// Gets all registered collectors.
        /// </summary>
        /// <returns>A collection of all registered collectors.</returns>
        public IEnumerable<IMetricsCollector> GetAllCollectors()
        {
            return _collectors.Values.ToList();
        }

        /// <summary>
        /// Gets collectors by category.
        /// </summary>
        /// <param name="category">The category to filter by.</param>
        /// <returns>Collectors matching the specified category.</returns>
        public IEnumerable<IMetricsCollector> GetCollectorsByCategory(MetricsCategory category)
        {
            return _collectors.Values.Where(c => c.Category == category).ToList();
        }

        /// <summary>
        /// Gets a comprehensive metrics report from all collectors.
        /// </summary>
        /// <returns>A comprehensive metrics report.</returns>
        public MetricsReport GetReport()
        {
            var report = new MetricsReport
            {
                Timestamp = DateTimeOffset.UtcNow,
                CollectorCount = _collectors.Count
            };

            foreach (var collector in _collectors.Values)
            {
                try
                {
                    var snapshot = collector.GetSnapshot();
                    report.Snapshots.Add(snapshot);

                    var status = collector.GetStatus();
                    report.CollectorStatuses[collector.Name] = status;
                }
                catch (Exception ex)
                {
                    report.Errors[collector.Name] = ex.Message;
                }
            }

            return report;
        }

        /// <summary>
        /// Gets a metrics report as JSON string.
        /// </summary>
        /// <param name="includeDetails">Whether to include detailed metrics or just summaries.</param>
        /// <returns>JSON representation of the metrics report.</returns>
        public string GetReportAsJson(bool includeDetails = true)
        {
            var report = GetReport();

            var options = new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };

            if (!includeDetails)
            {
                // Create a summary report without detailed metric values
                var summary = new
                {
                    report.Timestamp,
                    report.CollectorCount,
                    CollectorSummaries = report.Snapshots.Select(s => new
                    {
                        s.CollectorName,
                        s.Category,
                        CounterCount = s.Counters.Count,
                        GaugeCount = s.Gauges.Count,
                        HistogramCount = s.Histograms.Count,
                        TimingCount = s.Timings.Count
                    }),
                    report.CollectorStatuses,
                    report.Errors
                };
                return JsonSerializer.Serialize(summary, options);
            }

            return JsonSerializer.Serialize(report, options);
        }

        /// <summary>
        /// Resets all metrics in all collectors.
        /// </summary>
        public void ResetAllMetrics()
        {
            foreach (var collector in _collectors.Values)
            {
                try
                {
                    collector.Reset();
                }
                catch (Exception ex)
                {
                    // Log error but continue with other collectors
                    UsdpLogger.Log($"MetricsManager.ResetError", new { Collector = collector.Name, Error = ex.Message });
                }
            }
        }

        /// <summary>
        /// Gets the status of the metrics manager.
        /// </summary>
        /// <returns>Status information about the metrics manager.</returns>
        public MetricsManagerStatus GetStatus()
        {
            var enabledCollectors = _collectors.Values.Count(c => c.GetStatus().IsEnabled);
            var totalMetrics = _collectors.Values.Sum(c => c.GetStatus().MetricsCount);
            var collectorsWithErrors = _collectors.Values.Count(c => !string.IsNullOrEmpty(c.GetStatus().ErrorMessage));

            return new MetricsManagerStatus
            {
                IsEnabled = _configuration.Enabled,
                CollectorCount = _collectors.Count,
                EnabledCollectorCount = enabledCollectors,
                TotalMetricsCount = totalMetrics,
                CollectorsWithErrors = collectorsWithErrors,
                AutoReportingEnabled = _configuration.AutoReportingEnabled,
                ReportingInterval = _configuration.ReportingInterval,
                LastReportTime = DateTimeOffset.UtcNow // This would be tracked in a real implementation
            };
        }

        /// <summary>
        /// Registers default collectors for common metrics.
        /// </summary>
        private void RegisterDefaultCollectors()
        {
            try
            {
                RegisterCollector(new SerializationMetricsCollector());
                RegisterCollector(new PerformanceMetricsCollector());
                // TODO: Add other collectors when they are implemented
                // RegisterCollector(new SecurityMetricsCollector());
                // RegisterCollector(new NetworkMetricsCollector());
                // RegisterCollector(new CacheMetricsCollector());
            }
            catch (Exception ex)
            {
                UsdpLogger.Log("MetricsManager.DefaultCollectorRegistrationError", new { Error = ex.Message });
            }
        }

        /// <summary>
        /// Auto-reporting timer callback.
        /// </summary>
        /// <param name="state">Timer state (unused).</param>
        private void AutoReport(object? state)
        {
            if (_disposed || !_configuration.Enabled)
                return;

            try
            {
                var report = GetReportAsJson(false); // Summary only for auto-reporting
                UsdpLogger.Log("MetricsManager.AutoReport", new { ReportSize = report.Length });

                // In a real implementation, this would send the report to a monitoring system
                // For now, we just log that auto-reporting occurred
            }
            catch (Exception ex)
            {
                UsdpLogger.Log("MetricsManager.AutoReportError", new { Error = ex.Message });
            }
        }

        /// <inheritdoc />
        public void Dispose()
        {
            if (_disposed)
                return;

            _reportingTimer?.Dispose();
            _disposed = true;
        }
    }

    /// <summary>
    /// Configuration for the metrics manager.
    /// </summary>
    public class MetricsConfiguration
    {
        /// <summary>
        /// Gets or sets whether metrics collection is enabled.
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// Gets or sets whether auto-reporting is enabled.
        /// </summary>
        public bool AutoReportingEnabled { get; set; } = false;

        /// <summary>
        /// Gets or sets the interval for auto-reporting.
        /// </summary>
        public TimeSpan ReportingInterval { get; set; } = TimeSpan.FromMinutes(5);

        /// <summary>
        /// Gets or sets the maximum number of metrics to retain per collector.
        /// </summary>
        public int MaxMetricsPerCollector { get; set; } = 1000;
    }

    /// <summary>
    /// Comprehensive metrics report containing data from all collectors.
    /// </summary>
    public class MetricsReport
    {
        /// <summary>
        /// Gets or sets the timestamp when this report was generated.
        /// </summary>
        public DateTimeOffset Timestamp { get; set; }

        /// <summary>
        /// Gets or sets the number of collectors included in this report.
        /// </summary>
        public int CollectorCount { get; set; }

        /// <summary>
        /// Gets or sets the snapshots from all collectors.
        /// </summary>
        public List<MetricsSnapshot> Snapshots { get; set; } = new();

        /// <summary>
        /// Gets or sets the status of each collector.
        /// </summary>
        public Dictionary<string, MetricsCollectorStatus> CollectorStatuses { get; set; } = new();

        /// <summary>
        /// Gets or sets any errors encountered during report generation.
        /// </summary>
        public Dictionary<string, string> Errors { get; set; } = new();
    }

    /// <summary>
    /// Status information about the metrics manager.
    /// </summary>
    public class MetricsManagerStatus
    {
        /// <summary>
        /// Gets or sets whether the metrics manager is enabled.
        /// </summary>
        public bool IsEnabled { get; set; }

        /// <summary>
        /// Gets or sets the total number of registered collectors.
        /// </summary>
        public int CollectorCount { get; set; }

        /// <summary>
        /// Gets or sets the number of enabled collectors.
        /// </summary>
        public int EnabledCollectorCount { get; set; }

        /// <summary>
        /// Gets or sets the total number of metrics across all collectors.
        /// </summary>
        public int TotalMetricsCount { get; set; }

        /// <summary>
        /// Gets or sets the number of collectors with errors.
        /// </summary>
        public int CollectorsWithErrors { get; set; }

        /// <summary>
        /// Gets or sets whether auto-reporting is enabled.
        /// </summary>
        public bool AutoReportingEnabled { get; set; }

        /// <summary>
        /// Gets or sets the auto-reporting interval.
        /// </summary>
        public TimeSpan ReportingInterval { get; set; }

        /// <summary>
        /// Gets or sets the last time a report was generated.
        /// </summary>
        public DateTimeOffset? LastReportTime { get; set; }
    }
}
