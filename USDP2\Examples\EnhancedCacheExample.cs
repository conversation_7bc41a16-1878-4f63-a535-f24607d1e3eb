using System;
using System.Threading;
using System.Threading.Tasks;

namespace USDP2.Examples
{
    /// <summary>
    /// Demonstrates the enhanced ServiceAdvertisementCache functionality including
    /// eviction policies, statistics, events, and memory management.
    /// </summary>
    public static class EnhancedCacheExample
    {
        /// <summary>
        /// Runs the enhanced cache demonstration.
        /// </summary>
        public static async Task RunAsync()
        {
            Console.WriteLine("Enhanced ServiceAdvertisementCache Demonstration");
            Console.WriteLine("==============================================");
            Console.WriteLine();

            // Configure cache settings
            await ConfigureCacheSettingsAsync();

            // Demonstrate basic functionality
            await DemonstrateBasicFunctionalityAsync();

            // Demonstrate statistics
            await DemonstrateStatisticsAsync();

            // Demonstrate eviction policies
            await DemonstrateEvictionPoliciesAsync();

            // Demonstrate events
            await DemonstrateEventsAsync();

            // Demonstrate memory management
            await DemonstrateMemoryManagementAsync();

            Console.WriteLine();
            Console.WriteLine("Enhanced cache demonstration completed!");
        }

        /// <summary>
        /// Configures cache settings for demonstration.
        /// </summary>
        private static async Task ConfigureCacheSettingsAsync()
        {
            Console.WriteLine("1. Configuring Enhanced Cache Settings");
            Console.WriteLine("=====================================");

            // Configure cache settings
            UsdpConfiguration.Instance.ServiceCacheMaxSize = 50;
            UsdpConfiguration.Instance.ServiceCacheMaxMemoryBytes = 1024 * 1024; // 1MB
            UsdpConfiguration.Instance.ServiceCacheEvictionPolicy = "LRU";
            UsdpConfiguration.Instance.ServiceCacheStatisticsEnabled = true;
            UsdpConfiguration.Instance.ServiceCacheEventsEnabled = true;
            UsdpConfiguration.Instance.ServiceCacheMemoryWarningThresholdPercent = 80;
            UsdpConfiguration.Instance.ServiceCacheMaintenanceInterval = TimeSpan.FromSeconds(5);

            Console.WriteLine($"Max Size: {UsdpConfiguration.Instance.ServiceCacheMaxSize} entries");
            Console.WriteLine($"Max Memory: {UsdpConfiguration.Instance.ServiceCacheMaxMemoryBytes / 1024}KB");
            Console.WriteLine($"Eviction Policy: {UsdpConfiguration.Instance.ServiceCacheEvictionPolicy}");
            Console.WriteLine($"Statistics Enabled: {UsdpConfiguration.Instance.ServiceCacheStatisticsEnabled}");
            Console.WriteLine($"Events Enabled: {UsdpConfiguration.Instance.ServiceCacheEventsEnabled}");
            Console.WriteLine();

            await Task.Delay(100); // Minimal async operation
        }

        /// <summary>
        /// Demonstrates basic cache functionality.
        /// </summary>
        private static async Task DemonstrateBasicFunctionalityAsync()
        {
            Console.WriteLine("2. Basic Cache Functionality");
            Console.WriteLine("============================");

            using var cache = new ServiceAdvertisementCache();

            // Add some services
            for (int i = 0; i < 10; i++)
            {
                var serviceId = new ServiceIdentifier("demo", $"service-{i}");
                var endpoint = new TransportEndpoint
                {
                    Address = $"192.168.1.{100 + i}",
                    Port = 8000 + i,
                    Protocol = "http"
                };
                var advertisement = new ServiceAdvertisement(serviceId, endpoint);
                cache.AddOrUpdate(advertisement);
            }

            // Retrieve services
            var activeServices = cache.GetActiveAdvertisements();
            Console.WriteLine($"Added 10 services, retrieved {activeServices.Count()} active services");

            // Test specific retrieval
            var specificService = cache.GetAdvertisement(new ServiceIdentifier("demo", "service-5"));
            Console.WriteLine($"Retrieved specific service: {specificService?.ServiceId}");

            // Test heartbeat
            cache.ReceiveHeartbeat(new ServiceIdentifier("demo", "service-3"));
            Console.WriteLine("Sent heartbeat for service-3");

            Console.WriteLine();
            await Task.Delay(100);
        }

        /// <summary>
        /// Demonstrates cache statistics functionality.
        /// </summary>
        private static async Task DemonstrateStatisticsAsync()
        {
            Console.WriteLine("3. Cache Statistics");
            Console.WriteLine("==================");

            using var cache = new ServiceAdvertisementCache();

            // Add services and access them to generate statistics
            for (int i = 0; i < 20; i++)
            {
                var serviceId = new ServiceIdentifier("stats", $"service-{i}");
                var endpoint = new TransportEndpoint
                {
                    Address = $"10.0.0.{i + 1}",
                    Port = 9000 + i,
                    Protocol = "tcp"
                };
                var advertisement = new ServiceAdvertisement(serviceId, endpoint);
                cache.AddOrUpdate(advertisement);
            }

            // Access some services multiple times
            for (int round = 0; round < 3; round++)
            {
                for (int i = 0; i < 10; i++)
                {
                    var serviceId = new ServiceIdentifier("stats", $"service-{i}");
                    cache.GetAdvertisement(serviceId);
                }
            }

            // Try to access non-existent services (generate misses)
            for (int i = 0; i < 5; i++)
            {
                var serviceId = new ServiceIdentifier("stats", $"nonexistent-{i}");
                cache.GetAdvertisement(serviceId);
            }

            // Get and display statistics
            var stats = cache.GetStatistics();
            Console.WriteLine($"Current Size: {stats.CurrentSize}/{stats.MaxSize} ({stats.SizeUtilization:F1}%)");
            Console.WriteLine($"Memory Usage: {stats.CurrentMemoryBytes / 1024}KB/{stats.MaxMemoryBytes / 1024}KB ({stats.MemoryUtilization:F1}%)");
            Console.WriteLine($"Hit Rate: {stats.HitRate:F1}% ({stats.TotalHits} hits, {stats.TotalMisses} misses)");
            Console.WriteLine($"Total Operations: {stats.TotalAdds} adds, {stats.TotalEvictions} evictions");
            Console.WriteLine($"Average Entry Size: {stats.AverageEntrySize:F0} bytes");
            Console.WriteLine($"Operations/Second: {stats.OperationsPerSecond:F1}");
            Console.WriteLine($"Runtime: {stats.Runtime.TotalSeconds:F1} seconds");

            Console.WriteLine();
            await Task.Delay(100);
        }

        /// <summary>
        /// Demonstrates different eviction policies.
        /// </summary>
        private static async Task DemonstrateEvictionPoliciesAsync()
        {
            Console.WriteLine("4. Eviction Policies");
            Console.WriteLine("===================");

            var policies = new[] { "LRU", "LFU", "TTL", "Hybrid" };

            foreach (var policy in policies)
            {
                Console.WriteLine($"Testing {policy} eviction policy:");

                // Create configuration with small cache size to trigger evictions
                var config = new UsdpConfiguration(forTesting: true)
                {
                    ServiceCacheMaxSize = 5,
                    ServiceCacheEvictionPolicy = policy,
                    ServiceCacheStatisticsEnabled = true,
                    ServiceCacheMaintenanceInterval = TimeSpan.FromMilliseconds(100)
                };

                using var cache = new ServiceAdvertisementCache(config);

                // Add more services than the cache can hold
                for (int i = 0; i < 10; i++)
                {
                    var serviceId = new ServiceIdentifier("eviction", $"{policy.ToLower()}-service-{i}");
                    var endpoint = new TransportEndpoint
                    {
                        Address = $"172.16.0.{i + 1}",
                        Port = 7000 + i,
                        Protocol = "udp"
                    };
                    var advertisement = new ServiceAdvertisement(serviceId, endpoint);
                    cache.AddOrUpdate(advertisement);

                    // For LFU testing, access some services more frequently
                    if (policy == "LFU" && i < 3)
                    {
                        for (int j = 0; j < 5; j++)
                        {
                            cache.GetAdvertisement(serviceId);
                        }
                    }
                }

                // Wait for maintenance to run
                await Task.Delay(200);

                var stats = cache.GetStatistics();
                Console.WriteLine($"  Final size: {stats.CurrentSize}, Evictions: {stats.TotalEvictions}");
            }

            Console.WriteLine();
        }

        /// <summary>
        /// Demonstrates cache events.
        /// </summary>
        private static async Task DemonstrateEventsAsync()
        {
            Console.WriteLine("5. Cache Events");
            Console.WriteLine("==============");

            using var cache = new ServiceAdvertisementCache();

            var hitCount = 0;
            var missCount = 0;
            var addCount = 0;
            var evictCount = 0;

            // Subscribe to events
            cache.CacheHit += (sender, args) =>
            {
                hitCount++;
                Console.WriteLine($"  Cache Hit: {args.ServiceId} (access #{args.AccessCount})");
            };

            cache.CacheMiss += (sender, args) =>
            {
                missCount++;
                Console.WriteLine($"  Cache Miss: {args.ServiceId} (reason: {args.Reason})");
            };

            cache.EntryAdded += (sender, args) =>
            {
                addCount++;
                Console.WriteLine($"  Entry Added: {args.ServiceId} ({args.EstimatedSize} bytes, update: {args.IsUpdate})");
            };

            cache.EntryEvicted += (sender, args) =>
            {
                evictCount++;
                Console.WriteLine($"  Entry Evicted: {args.ServiceId} (reason: {args.Reason}, age: {args.Age.TotalSeconds:F1}s)");
            };

            // Generate events
            Console.WriteLine("Adding services...");
            var serviceId = new ServiceIdentifier("events", "test-service");
            var endpoint = new TransportEndpoint { Address = "127.0.0.1", Port = 8080, Protocol = "http" };
            var advertisement = new ServiceAdvertisement(serviceId, endpoint);
            cache.AddOrUpdate(advertisement);

            Console.WriteLine("Accessing services...");
            cache.GetAdvertisement(serviceId); // Hit
            cache.GetAdvertisement(new ServiceIdentifier("events", "nonexistent")); // Miss

            Console.WriteLine("Removing service...");
            cache.Remove(serviceId); // Eviction

            Console.WriteLine($"Event Summary: {addCount} adds, {hitCount} hits, {missCount} misses, {evictCount} evictions");
            Console.WriteLine();

            await Task.Delay(100);
        }

        /// <summary>
        /// Demonstrates memory management features.
        /// </summary>
        private static async Task DemonstrateMemoryManagementAsync()
        {
            Console.WriteLine("6. Memory Management");
            Console.WriteLine("===================");

            // Create configuration with small memory limit
            var config = new UsdpConfiguration(forTesting: true)
            {
                ServiceCacheMaxMemoryBytes = 50 * 1024, // 50KB
                ServiceCacheMemoryWarningThresholdPercent = 70,
                ServiceCacheEventsEnabled = true,
                ServiceCacheMaintenanceInterval = TimeSpan.FromMilliseconds(100)
            };

            using var cache = new ServiceAdvertisementCache(config);

            var warningCount = 0;
            cache.MemoryThresholdWarning += (sender, args) =>
            {
                warningCount++;
                Console.WriteLine($"  Memory Warning: {args.UtilizationPercent:F1}% usage ({args.CurrentMemoryBytes / 1024}KB/{args.MaxMemoryBytes / 1024}KB)");
            };

            // Add services with large metadata to consume memory
            for (int i = 0; i < 100; i++)
            {
                var serviceId = new ServiceIdentifier("memory", $"large-service-{i}");
                var endpoint = new TransportEndpoint
                {
                    Address = $"192.168.100.{i % 255 + 1}",
                    Port = 6000 + i,
                    Protocol = "http"
                };
                var advertisement = new ServiceAdvertisement(serviceId, endpoint);

                // Add large metadata to increase memory usage
                for (int j = 0; j < 10; j++)
                {
                    advertisement.Metadata[$"large_metadata_key_{j}"] = $"This is a large metadata value for key {j} in service {i} that takes up significant memory space";
                }

                cache.AddOrUpdate(advertisement);

                // Check memory usage periodically
                if (i % 20 == 0)
                {
                    var stats = cache.GetStatistics();
                    Console.WriteLine($"Added {i + 1} services: {stats.CurrentMemoryBytes / 1024}KB memory, {stats.MemoryUtilization:F1}% utilization");
                }
            }

            // Wait for maintenance and memory management
            await Task.Delay(500);

            var finalStats = cache.GetStatistics();
            Console.WriteLine($"Final state: {finalStats.CurrentSize} entries, {finalStats.CurrentMemoryBytes / 1024}KB memory");
            Console.WriteLine($"Memory evictions: {finalStats.MemoryEvictions}, Warnings: {warningCount}");

            Console.WriteLine();
        }
    }
}
