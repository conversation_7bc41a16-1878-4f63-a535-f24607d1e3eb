using System;
using System.Collections.Generic;

namespace USDP2.Metrics
{
    /// <summary>
    /// Core interface for collecting and reporting metrics in the USDP2 system.
    /// Provides a unified approach to metrics collection across all components.
    /// </summary>
    public interface IMetricsCollector
    {
        /// <summary>
        /// Gets the name of this metrics collector.
        /// </summary>
        string Name { get; }

        /// <summary>
        /// Gets the category of metrics this collector handles.
        /// </summary>
        MetricsCategory Category { get; }

        /// <summary>
        /// Records a counter metric (monotonically increasing value).
        /// </summary>
        /// <param name="name">The metric name.</param>
        /// <param name="value">The value to add to the counter.</param>
        /// <param name="tags">Optional tags for metric categorization.</param>
        void RecordCounter(string name, long value = 1, IDictionary<string, string>? tags = null);

        /// <summary>
        /// Records a gauge metric (point-in-time value).
        /// </summary>
        /// <param name="name">The metric name.</param>
        /// <param name="value">The current value.</param>
        /// <param name="tags">Optional tags for metric categorization.</param>
        void RecordGauge(string name, double value, IDictionary<string, string>? tags = null);

        /// <summary>
        /// Records a histogram metric (distribution of values).
        /// </summary>
        /// <param name="name">The metric name.</param>
        /// <param name="value">The value to record.</param>
        /// <param name="tags">Optional tags for metric categorization.</param>
        void RecordHistogram(string name, double value, IDictionary<string, string>? tags = null);

        /// <summary>
        /// Records a timing metric (duration measurement).
        /// </summary>
        /// <param name="name">The metric name.</param>
        /// <param name="duration">The duration to record.</param>
        /// <param name="tags">Optional tags for metric categorization.</param>
        void RecordTiming(string name, TimeSpan duration, IDictionary<string, string>? tags = null);

        /// <summary>
        /// Gets all collected metrics as a snapshot.
        /// </summary>
        /// <returns>A snapshot of all metrics collected by this collector.</returns>
        MetricsSnapshot GetSnapshot();

        /// <summary>
        /// Resets all collected metrics.
        /// </summary>
        void Reset();

        /// <summary>
        /// Gets the current status of the metrics collector.
        /// </summary>
        /// <returns>Status information about the collector.</returns>
        MetricsCollectorStatus GetStatus();
    }

    /// <summary>
    /// Categories of metrics for organizational purposes.
    /// </summary>
    public enum MetricsCategory
    {
        /// <summary>
        /// Performance-related metrics (latency, throughput, etc.).
        /// </summary>
        Performance,

        /// <summary>
        /// Security-related metrics (authentication, authorization, threats).
        /// </summary>
        Security,

        /// <summary>
        /// Network-related metrics (connections, bandwidth, errors).
        /// </summary>
        Network,

        /// <summary>
        /// Serialization-related metrics (encoding, decoding, cache).
        /// </summary>
        Serialization,

        /// <summary>
        /// Cache-related metrics (hits, misses, evictions).
        /// </summary>
        Cache,

        /// <summary>
        /// Circuit breaker metrics (state changes, failures).
        /// </summary>
        CircuitBreaker,

        /// <summary>
        /// Health check metrics (status, response times).
        /// </summary>
        HealthCheck,

        /// <summary>
        /// Rate limiting metrics (requests, blocks, quotas).
        /// </summary>
        RateLimit,

        /// <summary>
        /// System-level metrics (memory, CPU, resources).
        /// </summary>
        System,

        /// <summary>
        /// Application-specific business metrics.
        /// </summary>
        Application
    }

    /// <summary>
    /// Represents a snapshot of metrics at a point in time.
    /// </summary>
    public class MetricsSnapshot
    {
        /// <summary>
        /// Gets or sets the timestamp when this snapshot was taken.
        /// </summary>
        public DateTimeOffset Timestamp { get; set; } = DateTimeOffset.UtcNow;

        /// <summary>
        /// Gets or sets the collector name that generated this snapshot.
        /// </summary>
        public string CollectorName { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the metrics category.
        /// </summary>
        public MetricsCategory Category { get; set; }

        /// <summary>
        /// Gets or sets the counter metrics.
        /// </summary>
        public Dictionary<string, CounterMetric> Counters { get; set; } = new();

        /// <summary>
        /// Gets or sets the gauge metrics.
        /// </summary>
        public Dictionary<string, GaugeMetric> Gauges { get; set; } = new();

        /// <summary>
        /// Gets or sets the histogram metrics.
        /// </summary>
        public Dictionary<string, HistogramMetric> Histograms { get; set; } = new();

        /// <summary>
        /// Gets or sets the timing metrics.
        /// </summary>
        public Dictionary<string, TimingMetric> Timings { get; set; } = new();
    }

    /// <summary>
    /// Status information about a metrics collector.
    /// </summary>
    public class MetricsCollectorStatus
    {
        /// <summary>
        /// Gets or sets whether the collector is currently enabled.
        /// </summary>
        public bool IsEnabled { get; set; }

        /// <summary>
        /// Gets or sets the number of metrics currently being tracked.
        /// </summary>
        public int MetricsCount { get; set; }

        /// <summary>
        /// Gets or sets the last time metrics were collected.
        /// </summary>
        public DateTimeOffset? LastCollectionTime { get; set; }

        /// <summary>
        /// Gets or sets any error messages from the collector.
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Gets or sets the memory usage of the collector in bytes.
        /// </summary>
        public long MemoryUsageBytes { get; set; }
    }
}
