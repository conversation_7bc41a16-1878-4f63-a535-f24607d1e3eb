using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;

namespace USDP2.RetryPolicy
{
    /// <summary>
    /// Executes operations with configurable retry policies.
    /// </summary>
    public class RetryPolicyExecutor
    {
        private readonly RetryPolicyOptions _options;
        private readonly Random _random = new();

        /// <summary>
        /// Initializes a new instance of the <see cref="RetryPolicyExecutor"/> class.
        /// </summary>
        /// <param name="options">The retry policy options.</param>
        public RetryPolicyExecutor(RetryPolicyOptions options)
        {
            _options = options ?? throw new ArgumentNullException(nameof(options));
        }

        /// <summary>
        /// Executes an operation with retry policy.
        /// </summary>
        /// <param name="operation">The operation to execute.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>A task representing the operation.</returns>
        public async Task ExecuteAsync(Func<CancellationToken, Task> operation, CancellationToken cancellationToken = default)
        {
            await ExecuteAsync(async ct =>
            {
                await operation(ct).ConfigureAwait(false);
                return true; // Dummy return value
            }, cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        /// Executes an operation with retry policy and returns a result.
        /// </summary>
        /// <typeparam name="T">The type of the result.</typeparam>
        /// <param name="operation">The operation to execute.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>A task representing the operation result.</returns>
        public async Task<T> ExecuteAsync<T>(Func<CancellationToken, Task<T>> operation, CancellationToken cancellationToken = default)
        {
            var stopwatch = Stopwatch.StartNew();
            var attemptNumber = 0;
            Exception? lastException = null;

            while (attemptNumber < _options.MaxAttempts)
            {
                attemptNumber++;

                try
                {
                    if (_options.LogRetryAttempts && attemptNumber > 1)
                    {
                        UsdpLogger.Log("RetryPolicy.AttemptStarted", new
                        {
                            AttemptNumber = attemptNumber,
                            MaxAttempts = _options.MaxAttempts,
                            ElapsedTime = stopwatch.Elapsed.TotalMilliseconds,
                            LastException = lastException?.GetType().Name
                        });
                    }

                    var result = await operation(cancellationToken).ConfigureAwait(false);

                    if (_options.LogRetryAttempts && attemptNumber > 1)
                    {
                        UsdpLogger.Log("RetryPolicy.Success", new
                        {
                            AttemptNumber = attemptNumber,
                            TotalAttempts = attemptNumber,
                            TotalTime = stopwatch.Elapsed.TotalMilliseconds
                        });
                    }

                    return result;
                }
                catch (Exception ex) when (!(ex is OperationCanceledException && cancellationToken.IsCancellationRequested))
                {
                    lastException = ex;

                    // Check if we've exceeded the maximum total time
                    if (stopwatch.Elapsed >= _options.MaxTotalTime)
                    {
                        if (_options.LogRetryAttempts)
                        {
                            UsdpLogger.Log("RetryPolicy.MaxTimeExceeded", new
                            {
                                AttemptNumber = attemptNumber,
                                ElapsedTime = stopwatch.Elapsed.TotalMilliseconds,
                                MaxTotalTime = _options.MaxTotalTime.TotalMilliseconds,
                                Exception = ex.GetType().Name,
                                Message = ex.Message
                            });
                        }
                        throw;
                    }

                    // Create retry context
                    var context = new RetryContext(attemptNumber, ex, stopwatch.Elapsed);

                    // Check retry conditions
                    var shouldRetry = ShouldRetry(context);

                    if (shouldRetry == RetryDecision.DoNotRetry || attemptNumber >= _options.MaxAttempts)
                    {
                        if (_options.LogRetryAttempts)
                        {
                            UsdpLogger.Log("RetryPolicy.FinalFailure", new
                            {
                                AttemptNumber = attemptNumber,
                                MaxAttempts = _options.MaxAttempts,
                                TotalTime = stopwatch.Elapsed.TotalMilliseconds,
                                ShouldRetry = shouldRetry.ToString(),
                                Exception = ex.GetType().Name,
                                Message = ex.Message
                            });
                        }
                        throw;
                    }

                    // Calculate delay for next attempt
                    if (shouldRetry == RetryDecision.Retry && attemptNumber < _options.MaxAttempts)
                    {
                        var delay = CalculateDelay(attemptNumber);

                        if (_options.LogRetryAttempts)
                        {
                            UsdpLogger.Log("RetryPolicy.RetryScheduled", new
                            {
                                AttemptNumber = attemptNumber,
                                NextAttempt = attemptNumber + 1,
                                DelayMs = delay.TotalMilliseconds,
                                Exception = ex.GetType().Name,
                                Message = ex.Message
                            });
                        }

                        try
                        {
                            await Task.Delay(delay, cancellationToken).ConfigureAwait(false);
                        }
                        catch (OperationCanceledException) when (cancellationToken.IsCancellationRequested)
                        {
                            throw;
                        }
                    }
                }
            }

            // This should never be reached, but just in case
            throw lastException ?? new InvalidOperationException("Retry policy execution failed unexpectedly");
        }

        /// <summary>
        /// Determines whether the operation should be retried based on the context.
        /// </summary>
        /// <param name="context">The retry context.</param>
        /// <returns>The retry decision.</returns>
        private RetryDecision ShouldRetry(RetryContext context)
        {
            foreach (var condition in _options.RetryConditions)
            {
                var decision = condition.ShouldRetry(context);
                if (decision != RetryDecision.Retry)
                {
                    return decision; // Return first non-retry decision
                }
            }

            return RetryDecision.Retry; // All conditions say retry
        }

        /// <summary>
        /// Calculates the delay before the next retry attempt.
        /// </summary>
        /// <param name="attemptNumber">The current attempt number.</param>
        /// <returns>The delay before the next retry attempt.</returns>
        private TimeSpan CalculateDelay(int attemptNumber)
        {
            var baseDelay = _options.DelayStrategy.CalculateDelay(attemptNumber, _options.BaseDelay);

            // Apply maximum delay limit
            if (baseDelay > _options.MaxDelay)
            {
                baseDelay = _options.MaxDelay;
            }

            // Apply jitter if enabled
            if (_options.UseJitter && _options.JitterFactor > 0)
            {
                var jitterRange = baseDelay.TotalMilliseconds * _options.JitterFactor;
                var jitter = (_random.NextDouble() - 0.5) * 2 * jitterRange; // -jitterRange to +jitterRange
                var jitteredDelay = TimeSpan.FromMilliseconds(Math.Max(0, baseDelay.TotalMilliseconds + jitter));
                return jitteredDelay;
            }

            return baseDelay;
        }

        /// <summary>
        /// Creates a retry policy executor with default network retry settings.
        /// </summary>
        /// <returns>A retry policy executor configured for network operations.</returns>
        public static RetryPolicyExecutor CreateNetworkRetryPolicy()
        {
            var options = new RetryPolicyOptions
            {
                MaxAttempts = UsdpConfiguration.Instance.NetworkRetryMaxAttempts,
                BaseDelay = UsdpConfiguration.Instance.NetworkRetryBaseDelay,
                MaxDelay = UsdpConfiguration.Instance.NetworkRetryMaxDelay,
                MaxTotalTime = UsdpConfiguration.Instance.NetworkRetryMaxTotalTime,
                DelayStrategy = new ExponentialBackoffStrategy(UsdpConfiguration.Instance.NetworkRetryBackoffMultiplier),
                RetryConditions = new List<IRetryCondition> { new NetworkErrorRetryCondition() },
                UseJitter = UsdpConfiguration.Instance.NetworkRetryUseJitter,
                JitterFactor = UsdpConfiguration.Instance.NetworkRetryJitterFactor,
                LogRetryAttempts = UsdpConfiguration.Instance.LogNetworkRetryAttempts
            };

            return new RetryPolicyExecutor(options);
        }

        /// <summary>
        /// Creates a retry policy executor with default HTTP retry settings.
        /// </summary>
        /// <returns>A retry policy executor configured for HTTP operations.</returns>
        public static RetryPolicyExecutor CreateHttpRetryPolicy()
        {
            var options = new RetryPolicyOptions
            {
                MaxAttempts = UsdpConfiguration.Instance.HttpRetryMaxAttempts,
                BaseDelay = UsdpConfiguration.Instance.HttpRetryBaseDelay,
                MaxDelay = UsdpConfiguration.Instance.HttpRetryMaxDelay,
                MaxTotalTime = UsdpConfiguration.Instance.HttpRetryMaxTotalTime,
                DelayStrategy = new ExponentialBackoffStrategy(UsdpConfiguration.Instance.HttpRetryBackoffMultiplier),
                RetryConditions = new List<IRetryCondition> { new HttpRetryCondition() },
                UseJitter = UsdpConfiguration.Instance.HttpRetryUseJitter,
                JitterFactor = UsdpConfiguration.Instance.HttpRetryJitterFactor,
                LogRetryAttempts = UsdpConfiguration.Instance.LogHttpRetryAttempts
            };

            return new RetryPolicyExecutor(options);
        }

        /// <summary>
        /// Creates a retry policy executor with custom settings.
        /// </summary>
        /// <param name="maxAttempts">Maximum number of retry attempts.</param>
        /// <param name="baseDelay">Base delay between attempts.</param>
        /// <param name="useExponentialBackoff">Whether to use exponential backoff.</param>
        /// <param name="useJitter">Whether to use jitter.</param>
        /// <returns>A retry policy executor with custom settings.</returns>
        public static RetryPolicyExecutor CreateCustomRetryPolicy(
            int maxAttempts = 3,
            TimeSpan? baseDelay = null,
            bool useExponentialBackoff = true,
            bool useJitter = true)
        {
            var options = new RetryPolicyOptions
            {
                MaxAttempts = maxAttempts,
                BaseDelay = baseDelay ?? TimeSpan.FromSeconds(1),
                DelayStrategy = useExponentialBackoff ? new ExponentialBackoffStrategy() : new FixedDelayStrategy(),
                UseJitter = useJitter,
                RetryConditions = new List<IRetryCondition> { new NetworkErrorRetryCondition() }
            };

            return new RetryPolicyExecutor(options);
        }

        /// <summary>
        /// Creates a retry policy executor with default Chord-specific retry settings.
        /// </summary>
        /// <returns>A retry policy executor configured for Chord operations.</returns>
        public static RetryPolicyExecutor CreateChordRetryPolicy()
        {
            var options = new RetryPolicyOptions
            {
                MaxAttempts = UsdpConfiguration.Instance.NetworkRetryMaxAttempts,
                BaseDelay = UsdpConfiguration.Instance.NetworkRetryBaseDelay,
                MaxDelay = UsdpConfiguration.Instance.NetworkRetryMaxDelay,
                MaxTotalTime = UsdpConfiguration.Instance.NetworkRetryMaxTotalTime,
                DelayStrategy = new ExponentialBackoffStrategy(UsdpConfiguration.Instance.NetworkRetryBackoffMultiplier),
                RetryConditions = new List<IRetryCondition> { new ChordRetryCondition() },
                UseJitter = true,
                LogRetryAttempts = UsdpConfiguration.Instance.LogNetworkRetryAttempts
            };

            return new RetryPolicyExecutor(options);
        }
    }

    /// <summary>
    /// HTTP-specific retry condition.
    /// </summary>
    public class HttpRetryCondition : IRetryCondition
    {
        /// <summary>
        /// Determines whether to retry based on HTTP-specific error conditions.
        /// </summary>
        /// <param name="context">The retry context.</param>
        /// <returns>The retry decision.</returns>
        public RetryDecision ShouldRetry(RetryContext context)
        {
            if (context.Exception == null)
                return RetryDecision.DoNotRetry;

            return context.Exception switch
            {
                // Always retry on timeout
                TimeoutException => RetryDecision.Retry,
                TaskCanceledException when context.Exception.InnerException is TimeoutException => RetryDecision.Retry,

                // Retry on specific HTTP errors
                HttpRequestException httpEx => GetHttpRetryDecision(httpEx),

                // Use base network condition for other errors
                _ => new NetworkErrorRetryCondition().ShouldRetry(context)
            };
        }

        private static RetryDecision GetHttpRetryDecision(HttpRequestException httpEx)
        {
            var message = httpEx.Message.ToLowerInvariant();

            // Immediate retry for rate limiting (with jitter this helps)
            if (message.Contains("429") || message.Contains("too many requests"))
                return RetryDecision.Retry;

            // Retry on server errors
            if (message.Contains("500") || message.Contains("502") || message.Contains("503") || message.Contains("504"))
                return RetryDecision.Retry;

            // Retry on timeout
            if (message.Contains("408") || message.Contains("request timeout"))
                return RetryDecision.Retry;

            // Don't retry on client errors
            if (message.Contains("400") || message.Contains("401") || message.Contains("403") || message.Contains("404"))
                return RetryDecision.DoNotRetry;

            // Default to retry for unknown HTTP errors
            return RetryDecision.Retry;
        }
    }
}
